CREATE TABLE `tbl_verification_code`
(
    `id`         int NOT NULL AUTO_INCREMENT,
    `user_id`    varchar(64) DEFAULT NULL COMMENT '用户id',
    `code`       int         DEFAULT NULL COMMENT '验证码',
    `mobile`     bigint      DEFAULT NULL COMMENT '手机号',
    `createTime` datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `validTime`  datetime    DEFAULT NULL COMMENT '有效时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8;
