<?xml version="1.0" encoding="UTF-8"?>
<ehcache updateCheck="false" name="defaultCache">

	<diskStore path="../temp/jeeplus/ehcache" />

	<!-- 默认缓存配置. -->
	<defaultCache maxEntriesLocalHeap="100" eternal="false" timeToIdleSeconds="1800" timeToLiveSeconds="3600"
		overflowToDisk="true" maxEntriesLocalDisk="100000" />

	<cache name="SystemAuthorizingRealm" maxEntriesLocalHeap="2000"
		   eternal="false" timeToIdleSeconds="3600" timeToLiveSeconds="0"
		   overflowToDisk="false" statistics="true">
	</cache>


	<cache name="shiro-activeSessionCache" maxEntriesLocalHeap="2000"
		   eternal="false" timeToIdleSeconds="3600" timeToLiveSeconds="0"
		   overflowToDisk="false" statistics="true">
	</cache>

	<!-- 系统缓存 -->
	<cache name="sysCache" maxEntriesLocalHeap="100" eternal="true" overflowToDisk="true"/>

	<!-- 用户缓存 -->
	<cache name="userCache" maxEntriesLocalHeap="100" eternal="true" overflowToDisk="true"/>

	<!-- 工作流模块缓存 -->
	<cache name="actCache" maxEntriesLocalHeap="100" eternal="true" overflowToDisk="true"/>

	<cache name="sys.config" maxEntriesLocalHeap="100" eternal="true" overflowToDisk="true"/>

	<!-- 系统活动会话缓存 -->
    <cache name="activeSessionsCache" maxEntriesLocalHeap="10000" overflowToDisk="true"
           eternal="true" timeToLiveSeconds="0" timeToIdleSeconds="0"
           diskPersistent="true" diskExpiryThreadIntervalSeconds="600"/>
    

    	
</ehcache>