<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.oa.mapper.OaNotifyMapper">

    <sql id="oaNotifyColumns">
        a.ID AS "id",
        a.TYPE AS "type",
        a.TITLE AS "title",
        a.CONTENT AS "content",
        a.FILES AS "files",
        a.STATUS AS "status",
        a.CREATE_BY AS "createBy.id",
        a.CREATE_DATE AS "createDate",
        a.UPDATE_BY AS "updateBy.id",
        a.UPDATE_DATE AS "updateDate",
        a.REMARKS AS "remarks",
        a.DEL_FLAG AS "delFlag",
        b.read_num,
        b.un_read_num,
        u.name AS "createBy.name",
        u.photo AS "createBy.photo"

    </sql>

    <sql id="oaNotifyJoins">
        <!-- 查询已读和未读条数 -->
        LEFT JOIN (
        SELECT r.oa_notify_id,
        sum(case when r.read_flag = '1' then 1 else 0 end) read_num,
        sum(case when r.read_flag != '1' then 1 else 0 end) un_read_num
        FROM plugin_oa_notify_record r GROUP BY r.oa_notify_id
        ) b ON b.oa_notify_id = a.id

        JOIN sys_user u ON u.id = a.CREATE_BY
    </sql>

    <select id="get" resultType="OaNotify">
        SELECT
        <include refid="oaNotifyColumns"/>
        FROM plugin_oa_notify a
        <include refid="oaNotifyJoins"/>
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="OaNotify">
        SELECT
        <include refid="oaNotifyColumns"/>
        <if test="isSelf">,
            r.read_flag AS "readFlag"
        </if>
        FROM plugin_oa_notify a
        <include refid="oaNotifyJoins"/>
        <!-- 我的通知 -->
        <if test="isSelf">
            JOIN plugin_oa_notify_record r ON r.oa_notify_id = a.id AND r.user_id = #{currentUser.id}
            <if test="readFlag != null and readFlag != ''">
                AND r.read_flag = #{readFlag}
            </if>
        </if>
        WHERE a.del_flag = #{DEL_FLAG_NORMAL}
        <if test="title != null and title != ''">
            AND a.TITLE LIKE CONCAT('%', #{title}, '%') 
        </if>
        <if test="type != null and type != ''">
            AND a.TYPE = #{type}
        </if>
        <if test="status != null and status != ''">
            AND a.STATUS = #{status}
        </if>
        <if test="isSelf">
            AND a.STATUS = '1'
        </if>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findAllList" resultType="OaNotify">
        SELECT
        <include refid="oaNotifyColumns"/>
        FROM plugin_oa_notify a
        <include refid="oaNotifyJoins"/>
        WHERE a.del_flag = #{DEL_FLAG_NORMAL}
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findCount" resultType="Long">
        SELECT
        count(1)
        FROM plugin_oa_notify a
        <if test="isSelf">
            JOIN plugin_oa_notify_record r ON r.oa_notify_id = a.id AND r.user_id = #{currentUser.id}
            <if test="readFlag != null and readFlag != ''">
                AND r.read_flag = #{readFlag}
            </if>
        </if>
        WHERE a.del_flag = #{DEL_FLAG_NORMAL}
        <if test="isSelf">
            AND a.STATUS = '1'
        </if>
    </select>

    <insert id="insert">
        INSERT INTO plugin_oa_notify(
        ID,
        TYPE,
        TITLE,
        CONTENT,
        FILES,
        STATUS,
        CREATE_BY,
        CREATE_DATE,
        UPDATE_BY,
        UPDATE_DATE,
        REMARKS,
        DEL_FLAG
        ) VALUES (
        #{id},
        #{type},
        #{title},
        #{content},
        #{files},
        #{status},
        #{createBy.id},
        #{createDate},
        #{updateBy.id},
        #{updateDate},
        #{remarks},
        #{delFlag}
        )
    </insert>

    <update id="update">
        UPDATE plugin_oa_notify SET
        TYPE = #{type},
        TITLE = #{title},
        CONTENT = #{content},
        FILES = #{files},
        STATUS = #{status},
        UPDATE_BY = #{updateBy.id},
        UPDATE_DATE = #{updateDate},
        REMARKS = #{remarks}
        WHERE id = #{id}
    </update>

    <update id="delete">
        DELETE FROM plugin_oa_notify
        WHERE id = #{id}
    </update>

    <update id="deleteByLogic">
        UPDATE plugin_oa_notify SET
        del_flag = #{DEL_FLAG_DELETE}
        WHERE id = #{id}
    </update>

</mapper>