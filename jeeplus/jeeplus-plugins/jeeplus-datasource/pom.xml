<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.jeeplus</groupId>
        <artifactId>jeeplus</artifactId>
        <version>8.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>jeeplus-datasource</artifactId>
    <packaging>jar</packaging>

    <name>jeeplus-datasource</name>
    <description>datasource project for jeeplus</description>

    <dependencies>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
      
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
            <version>4.5</version>
        </dependency>
        <dependency>
            <groupId>com.bstek.ureport</groupId>
            <artifactId>ureport2-core</artifactId>
            <version>2.2.9</version>
        </dependency>
        <dependency>
            <groupId>com.bstek.ureport</groupId>
            <artifactId>ureport2-console</artifactId>
            <version>2.2.9</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <!-- https://mvnrepository.com/artifact/commons-lang/commons-lang -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
