package com.jeeplus.modules.datasource.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jeeplus.modules.datasource.config.DBContextHolder;
import com.jeeplus.modules.datasource.config.DynamicDataSource;
import com.jeeplus.modules.datasource.mapper.DataSourceMapper;
import com.jeeplus.modules.datasource.po.SysDataBaseInfo;
import com.jeeplus.modules.datasource.service.DBChangeService;

@Service
public class DBChangeServiceImpl implements DBChangeService {

   @Autowired
   DataSourceMapper dataSourceMapper;
    @Autowired
    private DynamicDataSource dynamicDataSource;
    @Override
    public List<SysDataBaseInfo> get() {
        return dataSourceMapper.get();
    }

    @Override
    public boolean changeDb(String datasourceId)   {

        //默认切换到主数据源,进行整体资源的查找
        DBContextHolder.clearDataSource();

        List<SysDataBaseInfo> dataSourcesList = dataSourceMapper.get();

        for (SysDataBaseInfo sysDataBaseInfo : dataSourcesList) {
            if (sysDataBaseInfo.getCode().equals(datasourceId)) {
                //创建数据源连接&检查 若存在则不需重新创建
                dynamicDataSource.createDataSourceWithCheck(sysDataBaseInfo);
                //切换到该数据源
                DBContextHolder.setDataSource(sysDataBaseInfo.getCode());
                return true;
            }
        }
        return false;

    }

}