/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.office.service;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.service.TreeService;
import com.jeeplus.modules.office.entity.DocCategory;
import com.jeeplus.modules.office.mapper.DocCategoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 文书模板Service
 * <AUTHOR>
 * @version 2020-06-23
 */
@Service
@Transactional(readOnly = true)
public class DocCategoryService extends TreeService<DocCategoryMapper, DocCategory> {

	@Autowired
	private DocTemplateService docTemplateService;

	public DocCategory get(String id) {
		return super.get(id);
	}

	public List<DocCategory> findList(DocCategory docCategory) {
		if (StringUtils.isNotBlank(docCategory.getParentIds())){
			docCategory.setParentIds(","+docCategory.getParentIds()+",");
		}
		return super.findList(docCategory);
	}

	@Transactional(readOnly = false)
	public void save(DocCategory docCategory) {
		super.save(docCategory);
	}

	@Transactional(readOnly = false)
	public void delete(DocCategory docCategory) {
		// 删除模版文件数据
		docTemplateService.deleteByCategory(docCategory);
		// 删除分类数据
		super.delete(docCategory);
	}

}
