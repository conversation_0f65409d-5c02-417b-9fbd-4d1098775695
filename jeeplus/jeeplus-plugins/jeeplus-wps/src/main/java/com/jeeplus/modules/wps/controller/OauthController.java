package com.jeeplus.modules.wps.controller;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.jeeplus.modules.wps.model.UrlModel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "WPS授权认证")
@RestController
public class OauthController  {
    private static Map<String, String> fileTypeMap = new HashMap<String, String>();

    
    @ApiOperation(value = "获取访问路径")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "文件id 保证唯一性", name = "fileid", required = true),
    })
    @RequestMapping(value="/wpsoffice/url", method = RequestMethod.GET)
    public Object  getapp_Token( @RequestParam("_w_fileid") String fileid) throws UnsupportedEncodingException {
        if (fileid == null || fileid.isEmpty()) {
            return null;
        }
        UrlModel urlModel = new UrlModel();
        urlModel.wpsUrl = fileid;
        urlModel.token = "1";
        return  urlModel;
    }
   
    
    
    /**
    @ApiOperation(value = "获取访问路径")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "文件名/文件访问路径", name = "filename", required = true),
            @ApiImplicitParam(value = "文件id 保证唯一性", name = "fileid", required = true),
            @ApiImplicitParam(value = "操作类型 默认预览  read 预览   write 编辑 ", name = "operateType")
    })
    @RequestMapping(value="/wpsoffice/url", method = RequestMethod.GET)
    public Object  getapp_Token(@RequestParam("_w_fname") String filename, @RequestParam("_w_fileid") String fileid, String operateType) throws UnsupportedEncodingException {
        if (fileid == null || fileid.isEmpty()) {
            return null;
        }
        String url = ApplicationProperties.domain + "/office";
        if (filename.endsWith("xls") || filename.endsWith("xlsx")) {
            url += "/s";
        } else if (filename.endsWith("ppt") || filename.endsWith("pptx")) {
            url += "/p";
        } else if (filename.endsWith("pdf")) {
            url += "/f";
        } else {
            url += "/w";
        }
        url = url + "/" + fileid + "?" ;
        //// TODO: 注意：签名前，参数不要urlencode,要签名以后统一处理url编码，防止签名不过，带中文等字符容易导致签名不过，要注意签名与编成的顺序，最好不要带中文等特殊字符
        Map paramMap= new HashMap<String, String>();
        paramMap.put("_w_appid", ApplicationProperties.appid);
        paramMap.put("_w_fname", filename);
        paramMap.put("_w_fileid", fileid);
        operateType = StringUtils.isNotBlank(operateType) ? operateType : WebOfficeController.permission_read;
        paramMap.put("_w_operateType", operateType);
        paramMap.put("_w_tokentype", "1");
       paramMap.put("_w_userid", "435759745");
       // paramMap.put("_w_userid", UserUtils.getUser ().getLoginName ());
        String signature = WpsUtils.getSignature(paramMap, ApplicationProperties.appSecret);
        url += WpsUtils.getUrlParam(paramMap) + "&_w_signature=" + signature;
        UrlModel urlModel = new UrlModel();
        urlModel.wpsUrl = url;
        urlModel.token = "1";
        return  urlModel;
    }
**/
}
