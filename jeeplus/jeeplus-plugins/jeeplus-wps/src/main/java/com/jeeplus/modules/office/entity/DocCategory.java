/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.office.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.Lists;
import com.jeeplus.core.persistence.TreeEntity;

import java.util.List;

/**
 * 文书模板Entity
 * <AUTHOR>
 * @version 2020-06-23
 */
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class DocCategory extends TreeEntity<DocCategory> {

	private static final long serialVersionUID = 1L;

	private List<DocTemplate> docTemplateList = Lists.newArrayList();		// 子表列表

	public DocCategory() {
		super();
	}

	public DocCategory(String id){
		super(id);
	}

	public  DocCategory getParent() {
			return parent;
	}

	@Override
	public void setParent(DocCategory parent) {
		this.parent = parent;

	}

	public List<DocTemplate> getDocTemplateList() {
		return docTemplateList;
	}

	public void setDocTemplateList(List<DocTemplate> docTemplateList) {
		this.docTemplateList = docTemplateList;
	}
	public String getParentId() {
		return parent != null && parent.getId() != null ? parent.getId() : "0";
	}
}
