/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.office.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;

import javax.validation.constraints.NotBlank;

/**
 * 文书模板Entity
 * <AUTHOR>
 * @version 2020-06-23
 */
public class DocTemplate extends DataEntity<DocTemplate> {

	private static final long serialVersionUID = 1L;
	/** 上传文件保存路径 */
	public static final String FILE_PATH = "/wps/docTemplate/";

	private String name;		// 文书名称
	private String path;		// 存储路径
	private String version;		// 版本
	private String imgPath;		// 文件图片路径
	private DocCategory category;		// 类型 父类

	public DocTemplate() {
		super();
	}

	public DocTemplate(String id){
		super(id);
	}

	public DocTemplate(DocCategory category){
		this.category = category;
	}

	@NotBlank( message = "名称不能为空" )
	@ExcelField(title="文书名称", align=2, sort=1)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@ExcelField(title="存储路径", align=2, sort=2)
	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	@ExcelField(title="版本", align=2, sort=3)
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getImgPath() {
		return imgPath;
	}

	public void setImgPath(String imgPath) {
		this.imgPath = imgPath;
	}

	public DocCategory getCategory() {
		return category;
	}

	public void setCategory(DocCategory category) {
		this.category = category;
	}

}
