<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.office.mapper.DocTemplateMapper">

	<sql id="docTemplateColumns">
		a.id AS "id",
		a.name AS "name",
		a.path AS "path",
		a.version AS "version",
		a.img_path AS "imgPath",
		a.category_id AS "category.id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		b.name AS "category.name",
		b.parent_ids AS "category.parentIds"
	</sql>

	<sql id="docTemplateJoins">
			LEFT JOIN plugin_doc_category b ON b.id = a.category_id
	</sql>


	<select id="get" resultType="DocTemplate" >
		SELECT
			<include refid="docTemplateColumns"/>
		FROM plugin_doc_template a
		<include refid="docTemplateJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="DocTemplate" >
		SELECT
			<include refid="docTemplateColumns"/>
		FROM plugin_doc_template a
		<include refid="docTemplateJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="name != null  and name != ''">
				AND a.name LIKE CONCAT('%', #{name}, '%')
			</if>
			<if test="category != null  and category.id != null and category.id != ''">
				AND (b.id = #{category.id} OR b.parent_ids LIKE 
				CONCAT('%,', #{category.id}, ',%')) 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date ASC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="DocTemplate" >
		SELECT
			<include refid="docTemplateColumns"/>
		FROM plugin_doc_template a
		<include refid="docTemplateJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date ASC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO plugin_doc_template(
			id,
			`name`,
			path,
			version,
			img_path,
			category_id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag
		) VALUES (
			#{id},
			#{name},
			#{path},
			#{version},
			#{imgPath},
			#{category.id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag}
		)
	</insert>

	<update id="update">
		UPDATE plugin_doc_template SET
			`name` = #{name},
			path = #{path},
			version = #{version},
			img_path = #{imgPath},
			category_id = #{category.id},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM plugin_doc_template
		<choose>
			<when test="id !=null and id != ''">
				WHERE id = #{id}
			</when>
			<otherwise>
				WHERE category_id = #{category.id}
			</otherwise>
		</choose>
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE plugin_doc_template SET
			del_flag = #{DEL_FLAG_DELETE}
		<choose>
			<when test="id !=null and id != ''">
				WHERE id = #{id}
			</when>
			<otherwise>
				WHERE category_id = #{category.id}
			</otherwise>
		</choose>
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="DocTemplate" statementType="STATEMENT">
		select * FROM plugin_doc_template  where ${propertyName} = '${value}'
	</select>

</mapper>
