<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.office.mapper.DocCategoryMapper">

    <resultMap id="docCategoryResult" type="DocCategory">
   		<result property="id" column="id" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<result property="parentIds" column="parentIds" />
    </resultMap>

	<sql id="docCategoryColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.name AS "name",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.parent_id AS "parent.id",
		a.parent_ids AS "parentIds",
		a.sort AS "sort"
	</sql>




	<sql id="docCategoryJoins">

	</sql>



	<select id="get" resultType="DocCategory">
		SELECT
			<include refid="docCategoryColumns"/>
		FROM plugin_doc_category a
		<include refid="docCategoryJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="DocCategory">
		SELECT
			<include refid="docCategoryColumns"/>
		FROM plugin_doc_category a
		<include refid="docCategoryJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%') 
			</if>
			<if test="parent != null and parent.id != null and parent.id != ''">
				AND a.parent_id = #{parent.id}
			</if>
			<if test="parentIds != null and parentIds != ''">
				AND a.parent_ids LIKE concat('%',#{parentIds},'%') 
			</if>
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="findAllList" resultType="DocCategory">
		SELECT
			<include refid="docCategoryColumns"/>
		FROM plugin_doc_category a
		<include refid="docCategoryJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="getChildren" parameterType="String" resultMap="docCategoryResult">
        select * from plugin_doc_category where parent_id = #{id} ORDER BY sort
    </select>

	<select id="findByParentIdsLike" resultType="DocCategory">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM plugin_doc_category a
		<include refid="docCategoryJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			AND a.parent_ids LIKE #{parentIds}
		</where>
		ORDER BY a.sort ASC
	</select>

	<insert id="insert">
		INSERT INTO plugin_doc_category(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   name,
			   remarks,
			   del_flag,
			   parent_id,
			   parent_ids,
			   sort
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{name},
			#{remarks},
			#{delFlag},
			#{parent.id},
			#{parentIds},
			#{sort}
		)
	</insert>

	<update id="update">
		UPDATE plugin_doc_category SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			name = #{name},
			remarks = #{remarks},
			parent_id = #{parent.id},
			parent_ids = #{parentIds},
			sort = #{sort}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE plugin_doc_category SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM plugin_doc_category
		WHERE id = #{id} OR parent_ids LIKE CONCAT('%,', #{id}, ',%') 
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE plugin_doc_category SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE CONCAT('%,', #{id}, ',%') 
	</update>

</mapper>
