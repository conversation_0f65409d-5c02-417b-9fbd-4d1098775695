/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.office.service;


import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.modules.office.entity.DocCategory;
import com.jeeplus.modules.office.entity.DocTemplate;
import com.jeeplus.modules.office.mapper.DocTemplateMapper;
import com.jeeplus.modules.sys.utils.FileKit;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 文书模板Service
 * <AUTHOR>
 * @version 2020-06-23
 */
@Service
@Transactional(readOnly = true)
public class DocTemplateService extends CrudService<DocTemplateMapper, DocTemplate> {

	public DocTemplate get(String id) {
		return super.get(id);
	}

	public List<DocTemplate> findList(DocTemplate docTemplate) {
		return super.findList(docTemplate);
	}

	public Page<DocTemplate> findPage(Page<DocTemplate> page, DocTemplate docTemplate) {
		return super.findPage(page, docTemplate);
	}

	@Transactional(readOnly = false)
	public void save(DocTemplate docTemplate) {
		super.save(docTemplate);
	}

	/**
	 * 根据 分类删除模版信息 及其文件
	 * @param docCategory
	 */
	@Transactional(readOnly = false)
	public void deleteByCategory(DocCategory docCategory) {
		DocTemplate queryTemplate = new DocTemplate();
		queryTemplate.setCategory(docCategory);
		List<DocTemplate> templateList = this.findList(queryTemplate);
		if(templateList != null && templateList.size() > 0){
			for (DocTemplate template : templateList) {
				this.delete(template);
			}
		}
	}

	@Transactional(readOnly = false)
	public void delete(DocTemplate docTemplate) {
		super.delete(docTemplate);
		if(StringUtils.isNotBlank(docTemplate.getPath())){
			System.out.println("=======");
			FileKit.deleteFileByUrl(docTemplate.getPath());
		}
	}

}
