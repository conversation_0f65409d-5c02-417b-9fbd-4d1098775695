<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseUserMapper">


	<sql id="caseUserColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.remarks AS "remarks",
		a.case_id AS "lawCase.id",
		a.user_id AS "user.id",
		su.name AS "user.name"
	</sql>

	<sql id="caseUserJoins">
		LEFT JOIN sys_user su ON a.user_id = su.id
	</sql>


	<select id="get" resultType="CaseUser" >
		SELECT
			<include refid="caseUserColumns"/>
		FROM law_case_user a
		<include refid="caseUserJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="getCountByCase" resultType="Integer" >
		SELECT COUNT(*) FROM law_case_user WHERE case_id = #{caseId}
	</select>

	<select id="findList" resultType="CaseUser" >
		SELECT
			<include refid="caseUserColumns"/>
		FROM law_case_user a
		<include refid="caseUserJoins"/>
		WHERE a.case_id = #{lawCase.id}
		<if test="user != null and user.id != null and user.id != ''">
			AND a.user_id = #{user.id}
		</if>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findUserList" resultType="User" >
		SELECT a.*
		FROM(
			SELECT a.id, a.name
			FROM sys_user a
			WHERE NOT FIND_IN_SET(a.id, (SELECT CONCAT_WS(',', '1', (SELECT host_user_id FROM law_case WHERE id = #{caseId}) )) )
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%')
			</if>
		) a
		LEFT JOIN law_case_user cu ON a.id = cu.user_id AND cu.case_id = #{caseId}
		WHERE cu.case_id IS NULL OR cu.case_id = ''
		ORDER BY convert(a.name USING gbk)
	</select>

	<select id="findAllList" resultType="CaseUser" >
		SELECT
			<include refid="caseUserColumns"/>
		FROM law_case_user a
		<include refid="caseUserJoins"/>
	  
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_user(
			id,
			create_by,
			create_date,
			remarks,
			case_id,
			user_id
		)VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{remarks},
			#{lawCase.id},
			#{user.id}
		)
	</insert>

	<update id="update">
		UPDATE law_case_user SET
			remarks = #{remarks},
			case_id = #{lawCase.id},
			user_id = #{user.id}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_user WHERE id = #{id}
	</update>

	<update id="deleteByCase">
		DELETE FROM law_case_user WHERE case_id = #{lawCaseId}
	</update>


	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_user SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseUser">
		select * FROM law_case_user  where ${propertyName} = #{value}
	</select>

</mapper>