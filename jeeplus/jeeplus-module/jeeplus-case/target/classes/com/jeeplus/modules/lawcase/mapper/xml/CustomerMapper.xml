<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CustomerMapper">

	<sql id="customerColumns">
		a.id AS "id",
		a.create_date AS "createDate",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.number AS "number",
		a.type AS "type",
		a.contract_start_date AS "contractStartDate",
		a.contract_end_date AS "contractEndDate",
		a.industry_id AS "industry.id",
		a.source AS "source",
		a.status AS "status",
		a.importance AS "importance",
		a.name AS "name",
		a.nation AS "nation",
		a.sex AS "sex",
		a.phone AS "phone",
		a.id_number AS "idNumber",
		a.address AS "address",
		a.legal_representative AS "legalRepresentative",
		a.unified_social_credit_code AS "unifiedSocialCreditCode",

		ind.name AS "industry.name",
		ind.parent_ids AS "industry.parentIds"
	</sql>

	<sql id="customerListColumns">
		a.id AS "id",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.number AS "number",
		a.type AS "type",
		a.contract_start_date AS "contractStartDate",
		a.contract_end_date AS "contractEndDate",
		a.industry_id AS "industry.id",
		a.source AS "source",
		a.status AS "status",
		a.importance AS "importance",
		a.name AS "name",
		a.phone AS "phone",
		a.id_number AS "idNumber",
		a.address AS "address",

		ind.name AS "industry.name"
	</sql>

	<sql id="customerJoins">
		LEFT JOIN law_industry ind ON a.industry_id = ind.id
	</sql>


	<select id="get" resultType="Customer" >
		SELECT
			<include refid="customerColumns"/>
		FROM law_customer a
		<include refid="customerJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="Customer" >
		SELECT
			<include refid="customerColumns"/>
		FROM law_customer a
		<include refid="customerJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="createBy != null and createBy.id != null and createBy.id != ''">
				AND a.create_by = #{createBy.id}
			</if>
			<if test="number != null and number != ''">
				AND a.number LIKE concat('%',#{number},'%') 
			</if>
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="contractEndDate != null and contractEndDate != ''">
				AND a.contract_end_date = #{contractEndDate}
			</if>
			<if test="industry != null and industry.id != null and industry.id != ''">
				AND (a.industry_id = #{industry.id} OR CONCAT(',', ind.parent_ids) LIKE CONCAT('%,', #{industry.id}, ',%') )
			</if>
			<if test="source != null and source != ''">
				AND a.source = #{source}
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="importance != null and importance != ''">
				AND a.importance = #{importance}
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%') 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findOverallList" resultType="Customer" >
		SELECT
			<include refid="customerListColumns"/>
			,(SELECT GROUP_CONCAT(su.name) FROM law_customer_follow_up fu INNER JOIN sys_user su ON fu.user_id = su.id
					WHERE fu.customer_id = a.id GROUP BY fu.customer_id  ORDER BY fu.create_date ) AS "followUpNames"
		FROM law_customer a
		<include refid="customerJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="createBy != null and createBy.id != null and createBy.id != ''">
				AND a.create_by = #{createBy.id}
			</if>
			<if test="number != null and number != ''">
				AND a.number LIKE concat('%',#{number},'%') 
			</if>
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="contractEndDate != null and contractEndDate != ''">
				AND a.contract_end_date = #{contractEndDate}
			</if>
			<if test="industry != null and industry.id != null and industry.id != ''">
				AND (a.industry_id = #{industry.id} OR CONCAT(',', ind.parent_ids) LIKE CONCAT('%,', #{industry.id}, ',%') )
			</if>
			<if test="source != null and source != ''">
				AND a.source = #{source}
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="importance != null and importance != ''">
				AND a.importance = #{importance}
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%') 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="Customer" >
		SELECT
			<include refid="customerColumns"/>
		FROM law_customer a
		<include refid="customerJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_customer(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			`number`,
			`type`,
			contract_start_date,
			contract_end_date,
			industry_id,
			source,
			status,
			importance,
			`name`,
			nation,
			sex,
			phone,
			id_number,
			address,
			legal_representative,
			unified_social_credit_code
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{number},
			#{type},
			#{contractStartDate},
			#{contractEndDate},
			#{industry.id},
			#{source},
			#{status},
			#{importance},
			#{name},
			#{nation},
			#{sex},
			#{phone},
			#{idNumber},
			#{address},
			#{legalRepresentative},
			#{unifiedSocialCreditCode}
		)
	</insert>

	<update id="update">
		UPDATE law_customer SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			`number` = #{number},
			`type` = #{type},
			contract_start_date = #{contractStartDate},
			contract_end_date = #{contractEndDate},
			industry_id = #{industry.id},
			source = #{source},
			status = #{status},
			importance = #{importance},
			`name` = #{name},
			nation = #{nation},
			sex = #{sex},
			phone = #{phone},
			id_number = #{idNumber},
			address = #{address},
			legal_representative = #{legalRepresentative},
			unified_social_credit_code = #{unifiedSocialCreditCode}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_customer
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_customer SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="Customer">
		select * FROM law_customer  where ${propertyName} = #{value}
	</select>

	<select id="findFollowUpList" resultType="CustomerFollowUp" >
		SELECT a.id, a.customer_id AS "customer.id", a.user_id AS "user.id", su.name AS "user.name"
		FROM law_customer_follow_up a
		LEFT JOIN sys_user su ON a.user_id = su.id
		WHERE a.customer_id = #{customer.id}
		ORDER BY a.create_date
	</select>

	<insert id="insertFollowUp">
		INSERT INTO law_customer_follow_up(
			id,
			create_by,
			create_date,
			remarks,
			customer_id,
			user_id
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.remarks},
			#{item.customer.id},
			#{item.user.id}
			)
		</foreach>

	</insert>

	<update id="deleteFollowUpByCustomer">
		DELETE FROM law_customer_follow_up WHERE customer_id = #{customerId}
	</update>


</mapper>