<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.StageRecordFileMapper">
    <resultMap id="BaseResultMap" type="com.jeeplus.modules.lawcase.entity.StageRecordFile">
        <!--@mbg.generated-->
        <!--@Table law_stage_record_file-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="del_flag" jdbcType="VARCHAR" property="delFlag"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
             <result column="sort" jdbcType="INTEGER" property="sort"/>
         <result column="doc_type" jdbcType="INTEGER" property="docType"/>
        <result column="record_id" jdbcType="VARCHAR" property="recordId"/>
          <result column="template_id" jdbcType="VARCHAR" property="templateId"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        remarks,
        del_flag,
        `name`,
        sort,
        record_id,
        template_id,
        doc_type,
        `path`
    </sql>
    
    
	<select id="get" resultType="StageRecordFile">
		SELECT
			<include refid="Base_Column_List"/>
		FROM law_stage_record_file a
		
		WHERE a.id = #{id}
	</select>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update law_stage_record_file
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createBy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.updateBy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.updateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remarks = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remarks,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="del_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.delFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="record_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.recordId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`path` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.path,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update law_stage_record_file
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createBy != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.createBy,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createDate != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.createDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateBy != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.updateBy,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateDate != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.updateDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remarks = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remarks != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.remarks,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="del_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.delFlag != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.delFlag,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="record_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.recordId != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.recordId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`path` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.path != null">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.path,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into law_stage_record_file
        (id, create_by, create_date, update_by, update_date, remarks, del_flag, `name`, record_id,
         `path`,template_id,doc_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
             #{item.updateBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP}, #{item.remarks,jdbcType=VARCHAR},
             #{item.delFlag,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.recordId,jdbcType=VARCHAR},
             #{item.path,jdbcType=VARCHAR},#{item.templateId,jdbcType=VARCHAR},#{item.docType})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.jeeplus.modules.lawcase.entity.StageRecordFile">
        <!--@mbg.generated-->
        insert into law_stage_record_file
        (id, create_by, create_date, update_by, update_date, remarks, del_flag, `name`, record_id,
         `path`,template_id,sort,doc_type)
        values (#{id,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{remarks,jdbcType=VARCHAR},
                #{delFlag,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{recordId,jdbcType=VARCHAR},
                #{path,jdbcType=VARCHAR},#{templateId,jdbcType=VARCHAR},#{sort},#{docType}) on duplicate key
        update
            id = #{id,jdbcType=VARCHAR},
            create_by = #{createBy,jdbcType=VARCHAR},
            create_date = #{createDate,jdbcType=TIMESTAMP},
            update_by = #{updateBy,jdbcType=VARCHAR},
            update_date = #{updateDate,jdbcType=TIMESTAMP},
            remarks = #{remarks,jdbcType=VARCHAR},
            del_flag = #{delFlag,jdbcType=VARCHAR},
            `name` = #{name,jdbcType=VARCHAR},
             `doc_type` = #{docType},
            record_id = #{recordId,jdbcType=VARCHAR},
              template_id = #{templateId,jdbcType=VARCHAR},
            `path` = #{path,jdbcType=VARCHAR},
            `sort` = #{sort}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.jeeplus.modules.lawcase.entity.StageRecordFile">
        <!--@mbg.generated-->
        insert into law_stage_record_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateDate != null">
                update_date,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="recordId != null">
                record_id,
            </if>
                <if test="templateId != null">
                template_id,
            </if>
            <if test="path != null">
                `path`,
            </if>
              <if test="docType != null">
                `doc_type`,
            </if>
              <if test="sort != null">
                `sort`,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null">
                #{updateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="recordId != null">
                #{recordId,jdbcType=VARCHAR},
            </if>
             <if test="templateId != null">
                #{templateId,jdbcType=VARCHAR},
            </if>
            
            <if test="path != null">
                #{path,jdbcType=VARCHAR},
            </if>
             <if test="docType != null">
                #{docType},
            </if>
               <if test="sort != null">
                #{sort},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="recordId != null">
                record_id = #{recordId,jdbcType=VARCHAR},
            </if>
              <if test="templateId != null">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
             <if test="docType != null">
                `doc_type` = #{docType,jdbcType=VARCHAR},
            </if>
            
            <if test="path != null">
                `path` = #{path,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectListByRecordId" resultMap="BaseResultMap">
        select * from law_stage_record_file where record_id = #{id,jdbcType=VARCHAR} and del_flag=0 order by sort asc 
    </select>
    
    
    <select id="selectListByTemplateId" resultMap="BaseResultMap">
        select * from law_stage_record_file where template_id = #{id,jdbcType=VARCHAR} and del_flag=0
    </select>
    
    
    	<update id="delete">
		update law_stage_record_file  set del_flag=1     WHERE id = #{id}
	</update>
	
		<select id="getMaxSortByRid" resultType="Integer">
		select max(sort) FROM law_stage_record_file where  del_flag=0 and record_id = #{id}
	</select>
		<update id="updateSort">
		UPDATE law_stage_record_file SET
			sort = #{sort}
		WHERE id = #{id}
	</update>
		<update id="updateByDocType">
		UPDATE law_stage_record_file SET
			doc_type = #{docType}
		WHERE id = #{id}
	</update>
	
	
</mapper>