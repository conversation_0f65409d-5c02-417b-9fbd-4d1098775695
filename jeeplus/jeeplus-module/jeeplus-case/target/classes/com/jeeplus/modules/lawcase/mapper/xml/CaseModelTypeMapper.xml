<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseModelTypeMapper">

    <resultMap id="caseModelTypeResult" type="CaseModelType">
   		<result property="id" column="id" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<result property="flag" column="flag" />
		<result property="parentIds" column="parentIds" />
    </resultMap>

	<sql id="caseModelTypeColumns">
		a.id AS "id",
		a.del_flag AS "delFlag",
		a.parent_id AS "parent.id",
		a.parent_ids AS "parentIds",
		a.name AS "name",
		a.pname AS "pname",
		a.flag AS "flag",
		a.sort AS "sort"
	</sql>




	<sql id="caseModelTypeJoins">

	</sql>



	<select id="get" resultType="CaseModelType">
		SELECT
			<include refid="caseModelTypeColumns"/>
		FROM law_case_modeltype a
		<include refid="caseModelTypeJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseModelType">
		SELECT
			<include refid="caseModelTypeColumns"/>
		FROM law_case_modeltype a
		<include refid="caseModelTypeJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="parent != null and parent.id != null and parent.id != ''">
				AND a.parent_id = #{parent.id}
			</if>
			<if test="parentIds != null and parentIds != ''">
				AND a.parent_ids LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{parentIds}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{parentIds}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{parentIds}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{parentIds},'%')</if>
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
			</if>
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="findAllList" resultType="CaseModelType">
		SELECT
			<include refid="caseModelTypeColumns"/>
		FROM law_case_modeltype a
		<include refid="caseModelTypeJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="getChildren" parameterType="String" resultMap="caseModelTypeResult">
        select * from law_case_modeltype where parent_id = #{id} ORDER BY sort
    </select>

	<select id="findByParentIdsLike" resultType="CaseModelType">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM law_case_modeltype a
		<include refid="caseModelTypeJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			AND a.parent_ids LIKE #{parentIds}
		</where>
		ORDER BY a.sort ASC
	</select>

	<insert id="insert">
		INSERT INTO law_case_modeltype(
			   id,
			   del_flag,
			   parent_id,
			   parent_ids,
			   name,
			   sort
		) VALUES (
			#{id},
			#{delFlag},
			#{parent.id},
			#{parentIds},
			#{name},
			#{sort}
		)
	</insert>

	<update id="update">
		UPDATE law_case_modeltype SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds},
			name = #{name},
			sort = #{sort}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE law_case_modeltype SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_modeltype
		WHERE id = #{id} OR parent_ids LIKE
		<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'mssql'">'%,'+#{id}+',%'</if>
        <if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_modeltype SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE
		<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'mssql'">'%,'+#{id}+',%'</if>
        <if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>

</mapper>