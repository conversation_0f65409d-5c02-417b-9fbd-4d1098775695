<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseUndertakePersonMapper">

	<sql id="caseUndertakePersonColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.case_id AS "lawCase.id",
		a.name AS "name",
		a.phone AS "phone",
		a.department AS "department",
		a.post AS "post",
		a.address AS "address"
	</sql>

	<sql id="caseUndertakePersonJoins">

	</sql>


	<select id="get" resultType="CaseUndertakePerson" >
		SELECT
			<include refid="caseUndertakePersonColumns"/>
		FROM law_case_undertake_person a
		<include refid="caseUndertakePersonJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseUndertakePerson" >
		SELECT
			<include refid="caseUndertakePersonColumns"/>
		FROM law_case_undertake_person a
		<include refid="caseUndertakePersonJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CaseUndertakePerson" >
		SELECT
			<include refid="caseUndertakePersonColumns"/>
		FROM law_case_undertake_person a
		<include refid="caseUndertakePersonJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_undertake_person(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			case_id,
			`name`,
			phone,
			department,
			post,
			address
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{lawCase.id},
			#{name},
			#{phone},
			#{department},
			#{post},
			#{address}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_case_undertake_person(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			case_id,
			`name`,
			phone,
			department,
			post,
			address
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.lawCase.id},
			#{item.name},
			#{item.phone},
			#{item.department},
			#{item.post},
			#{item.address}
			)
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_case_undertake_person SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			case_id = #{lawCase.id},
			`name` = #{name},
			phone = #{phone},
			department = #{department},
			post = #{post},
			address = #{address}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_undertake_person
		WHERE id = #{id}
	</update>

	<update id="deleteByCase">
		DELETE FROM law_case_undertake_person
		WHERE case_id = #{lawCaseId}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_undertake_person SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseUndertakePerson">
		select * FROM law_case_undertake_person  where ${propertyName} = #{value}
	</select>

</mapper>