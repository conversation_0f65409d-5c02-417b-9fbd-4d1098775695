<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.TodoInfoFileMapper">

	<sql id="todoInfoFileColumns">
		a.id AS "id",
		a.fileversion AS "fileversion",
		a.filesizes AS "filesizes",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.name AS "name",
		a.path AS "path",
		a.sort as sort,
		a.doc_type as docType,
	    a.edit_flag as editFlag,
		
 
		a.todo_id AS "todoInfo.id"
	</sql>




	<sql id="todoInfoFileJoins">

	</sql>



	<select id="get" resultType="TodoInfoFile">
		SELECT
			<include refid="todoInfoFileColumns"/>
		FROM law_todo_info_file a
		<include refid="todoInfoFileJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="TodoInfoFile">
		SELECT
			<include refid="todoInfoFileColumns"/>
		FROM law_todo_info_file a
		<include refid="todoInfoFileJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="todoInfo != null and todoInfo.id != null and todoInfo.id != ''">
				AND a.todo_id = #{todoInfo.id}
			</if>
		</where>
		ORDER BY a.sort asc, a.create_date
	</select>

	<select id="findListByTodo" resultType="TodoInfoFile">
		SELECT
		<include refid="todoInfoFileColumns"/>
		FROM law_todo_info_file a
		RIGHT JOIN law_todo_info ti ON a.todo_id = ti.id
		WHERE ti.id = #{todoId} OR CONCAT(',', ti.parent_ids) LIKE CONCAT('%,',#{todoId},',%')
		ORDER BY a.create_date
	</select>

	<select id="findListByTodoRelevance" resultType="TodoInfoFile">
		SELECT
		<include refid="todoInfoFileColumns"/>
		FROM law_todo_info_file a
		RIGHT JOIN law_todo_info ti ON a.todo_id = ti.id
		<where> ti.relevance_type = #{relevanceType}
			<if test="relevanceId != null and relevanceId != ''">
				AND ti.relevance_id = #{relevanceId}
			</if>
		</where>
		ORDER BY a.create_date
	</select>

	<select id="findFileListByCase" resultType="TodoInfoFile">
		SELECT a.filesizes,a.fileversion,a.id, a.name, a.path, a.todo_id AS "todoInfo.id", ti.name AS "todoInfo.name", ti.parent_ids AS "todoInfo.parentIds"
		FROM law_todo_info_file a
		INNER JOIN law_todo_info ti ON a.todo_id = ti.id AND ti.relevance_type = '2' AND ti.relevance_id = #{caseId}
	</select>

	<select id="findAllList" resultType="TodoInfoFile">
		SELECT
			<include refid="todoInfoFileColumns"/>
		FROM law_todo_info_file a
		<include refid="todoInfoFileJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.create_date ASC
	</select>



	<insert id="insert">
		INSERT INTO law_todo_info_file(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
			   `name`,
			   path,
			   todo_id,
			   fileversion,
			   filesizes,
			   sort,
			   doc_type,
			   edit_flag
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{name},
			#{path},
			#{todoInfo.id},
			#{fileversion},
			#{filesizes},
			#{sort},
			#{docType},
			#{editFlag}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_todo_info_file(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
				`name`,
				path,
			   fileversion,
			   filesizes,
				todo_id,
				sort,
			   doc_type
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.name},
			#{item.path},
			#{item.fileversion},
			#{item.filesizes},
			#{item.todoInfo.id},
			#{item.sort} ,
			#{item.docType} 
			)
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_todo_info_file SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			`name` = #{name},
			path = #{path},
			doc_type = #{docType},
			 filesizes = #{filesizes},
			 fileversion = #{fileversion},
			todo_id = #{todoInfo.id}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_todo_info_file WHERE id = #{id}
	</update>

	<update id="deleteByTodo">
		DELETE FROM law_todo_info_file WHERE todo_id = #{todoInfoId}
	</update>

	<update id="deleteByTodoRelevance">
		DELETE tif FROM law_todo_info_file tif
		RIGHT JOIN law_todo_info ti ON tif.todo_id = ti.id
		WHERE ti.relevance_type = #{relevanceType}
		<if test="relevanceId != null and relevanceId != ''">
			AND ti.relevance_id = #{relevanceId}
		</if>
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_todo_info_file SET del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>
	
    <select id="getMaxSortByTid" resultType="Integer">
		select max(sort) FROM law_todo_info_file where  del_flag=0 and todo_id = #{id}
	</select>
	
		<update id="updateInfoDate">
		UPDATE law_todo_info_file SET
			update_date = #{updateDate},
			edit_flag=1 
		WHERE id = #{id}
	</update>

</mapper>