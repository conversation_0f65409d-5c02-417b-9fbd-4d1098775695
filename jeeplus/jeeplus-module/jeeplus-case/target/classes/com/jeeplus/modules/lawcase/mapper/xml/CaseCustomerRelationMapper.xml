<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseCustomerRelationMapper">


	<sql id="caseCustomerRelationColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.remarks AS "remarks",
		a.case_id AS "lawCase.id",
		a.customer_id AS "customer.id",

		cus.name AS "customer.name",
		lc.name AS "lawCase.name",
		lc.number AS "lawCase.number",
		lc.case_program_id AS "lawCase.caseProgram.id",
		lc.case_program_name AS "lawCase.caseProgram.name",
		lc.trial_result AS "lawCase.trialResult",
		lc.record_date AS "lawCase.recordDate",
		lc.win_money AS "lawCase.winMoney",
		lc.host_user_id AS "lawCase.hostUser.id",
		hostUser.name AS "lawCase.hostUser.name"
	</sql>

	<sql id="caseCustomerRelationJoins">
		LEFT JOIN law_case lc ON a.case_id = lc.id
		LEFT JOIN sys_user hostUser ON hostUser.id = lc.host_user_id
		LEFT JOIN law_customer cus ON a.customer_id = cus.id
	</sql>


	<select id="get" resultType="CaseCustomerRelation" >
		SELECT
			<include refid="caseCustomerRelationColumns"/>
		FROM law_case_customer_relation a
		<include refid="caseCustomerRelationJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="getCount" resultType="int" >
		SELECT COUNT(1) FROM law_case_customer_relation a
		WHERE a.case_id = #{lawCase.id} AND a.customer_id = #{customer.id}
	</select>

	<select id="findList" resultType="CaseCustomerRelation" >
		SELECT
			<include refid="caseCustomerRelationColumns"/>
		FROM law_case_customer_relation a
		<include refid="caseCustomerRelationJoins"/>
		<where>
			<if test="customer != null and customer.id != null and customer.id != ''">
				AND a.customer_id = #{customer.id}
			</if>
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findNotRelationCaseList" resultType="Case" >
		SELECT a.*
		FROM(
			SELECT a.id, a.name
			FROM law_case a
			WHERE a.del_flag = '0'
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%')
			</if>
			<if test="hostUserId != null and hostUserId != ''">
				AND a.host_user_id = #{hostUserId}
			</if>
		) a
		LEFT JOIN law_case_customer_relation cr ON a.id = cr.case_id AND cr.customer_id = #{customerId}
		WHERE cr.customer_id IS NULL OR cr.customer_id = ''
		ORDER BY convert(a.name USING gbk)
	</select>

	<select id="findAllList" resultType="CaseCustomerRelation" >
		SELECT
			<include refid="caseCustomerRelationColumns"/>
		FROM law_case_customer_relation a
		<include refid="caseCustomerRelationJoins"/>
	  
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_customer_relation(
			id,
			create_by,
			create_date,
			remarks,
			case_id,
			customer_id
		)VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{remarks},
			#{lawCase.id},
			#{customer.id}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_case_customer_relation(
			id,
			create_by,
			create_date,
			remarks,
			case_id,
			customer_id
		)VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.remarks},
			#{item.lawCase.id},
			#{item.customer.id}
			)
		</foreach>
	</insert>

	<update id="update">
		UPDATE law_case_customer_relation SET
			remarks = #{remarks},
			case_id = #{lawCase.id},
			customer_id = #{customer.id}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_customer_relation WHERE id = #{id}
	</update>

	<update id="deleteByCase">
		DELETE FROM law_case_customer_relation WHERE case_id = #{lawCaseId}
	</update>


	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_customer_relation SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseCustomerRelation">
		select * FROM law_case_customer_relation  where ${propertyName} = #{value}
	</select>

</mapper>