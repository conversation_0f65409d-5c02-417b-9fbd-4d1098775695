/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseTrialRecord.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CasePropertyPreservationService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/StageTemplateSortVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CustomerController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/StageTemplateController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CustomerFollowUp.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseModelType.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseUserMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseUser.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseCauseController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/common/LawCommonService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseModelTypeService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CasePropertyPreservationMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseCustomerRelationService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseExecuteSituationController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/constant/QiNiuCnst.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/VersionController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseFileService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseUserController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/StageController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseUndertakePerson.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/util/PackToZipUtils.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseFileDirectory.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseFileDirectoryController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/VersionMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/IndustryService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/OnlyofficeVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/StageRecordFile.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/StageMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/DeepseekVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CasePropertyPreservation.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/TodoInfoFileMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/WpsWebOfficeController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/StageTemplateService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseCustomerRelation.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/StageRecordMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseTrialRecordController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/StageTemplateVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/StageTemplate.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/util/DeepseekUtil.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/CustomerVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/CaseFinanceVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseCaseRelation.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseProgramController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/IndustryMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/BaseTreeVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/Version.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseUndertakePersonMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/task/TodoInfoTask.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseFileMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/StageService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/OnlyOfficeController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/constant/CaseConstant.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/Customer.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CustomerContacts.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseConcernPersonController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/util/TreeUtils.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseExecuteSituation.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseUndertakePersonController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseHandleStrategyMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseHandleStrategyService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/StageRecordVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/Industry.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/Case.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CustomerContactsController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/TestController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/TodoInfo.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseCaseRelationMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseProgramService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CustomerService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseModelTypeMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseFile.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/CaseModelTypeVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/VersionService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/FinanceFlowFileMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseProgram.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CasePropertyPreservationController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseConcernPersonService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/util/QiNiuUtil.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseUndertakePersonService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseConcernPerson.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseFileController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/task/TodoInfoTomorrowTask.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseHandleStrategyController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseCauseService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/TodoInfoRelevanceVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/FinanceFlowFile.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseExecuteSituationService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/FinanceFlowRecordMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/dto/CaseConcernPersonDto.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseProgramMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseCaseRelationService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/FinanceFlowRecord.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseCauseMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CustomerContactsMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/CaseModeTreeVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseHandleStrategy.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseCustomerRelationMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/FinanceFlowRecordService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/DeepSeekController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/FinanceFlowRecordController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/CaseVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/StageRecordService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/StageRecordController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseStage.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseExecuteSituationMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/StageTemplateMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/StageRecordFileMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseStageService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseStageController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/TodoInfoController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseFileDirectoryMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/TodoInfoFile.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseUserService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseCaseRelationController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseFileDirectoryService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/FinanceAccountVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CustomerMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/TodoInfoMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CaseTrialRecordService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/Stage.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/CustomerContactsService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/vo/StageVO.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseConcernPersonMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/service/TodoInfoService.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseTrialRecordMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/CaseCause.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/CaseController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/mapper/CaseStageMapper.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/web/IndustryController.java
/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus/jeeplus-module/jeeplus-case/src/main/java/com/jeeplus/modules/lawcase/entity/StageRecord.java
