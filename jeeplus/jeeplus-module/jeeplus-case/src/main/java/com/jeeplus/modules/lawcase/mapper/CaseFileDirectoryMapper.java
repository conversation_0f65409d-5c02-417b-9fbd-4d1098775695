/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.TreeMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jeeplus.modules.lawcase.entity.CaseFileDirectory;

/**
 * 案件文档目录MAPPER接口
 * <AUTHOR>
 * @version 2021-08-12
 */
@Mapper
@Repository
public interface CaseFileDirectoryMapper extends TreeMapper<CaseFileDirectory> {
    /**
     * 根据案件id 查询获取根目录
     * @param caseFileDirectory
     * @return
     */
    CaseFileDirectory getRootByCase(CaseFileDirectory caseFileDirectory);
}