package com.jeeplus.modules.lawcase.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.FinanceFlowRecord;

import lombok.Data;

@Data
public class OnlyofficeVO implements Serializable{

	private String fileId;
	private String fileStream;
	private String type;
	
}
