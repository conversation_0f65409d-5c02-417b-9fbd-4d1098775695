/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.CasePropertyPreservation;
import com.jeeplus.modules.lawcase.mapper.CasePropertyPreservationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 财产保全Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CasePropertyPreservationService extends CrudService<CasePropertyPreservationMapper, CasePropertyPreservation> {

	public CasePropertyPreservation get(String id) {
		return super.get(id);
	}
	
	public List<CasePropertyPreservation> findList(CasePropertyPreservation casePropertyPreservation) {
		return super.findList(casePropertyPreservation);
	}
	
	public Page<CasePropertyPreservation> findPage(Page<CasePropertyPreservation> page, CasePropertyPreservation casePropertyPreservation) {
		return super.findPage(page, casePropertyPreservation);
	}
	
	@Transactional(readOnly = false)
	public void save(CasePropertyPreservation casePropertyPreservation) {
		super.save(casePropertyPreservation);
	}
	
	@Transactional(readOnly = false)
	public void delete(CasePropertyPreservation casePropertyPreservation) {
		super.delete(casePropertyPreservation);
	}
	
}