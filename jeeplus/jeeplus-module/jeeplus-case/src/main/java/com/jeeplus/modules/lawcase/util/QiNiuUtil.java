package com.jeeplus.modules.lawcase.util;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jeeplus.modules.lawcase.constant.QiNiuCnst;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class QiNiuUtil {
	
	public static void main(String[] args) {
		System.out.println(
				getContent("sk-b6f96665b92a524bf149ff53d74e074b3ca38b56b278c0544ae82b8174995a1f"
				,"案件类型有哪些？",true));
	}
	
	//
	public static String getContent(String key,String content,boolean stream) {
		OkHttpClient client = new OkHttpClient().newBuilder()
		        .readTimeout(60, TimeUnit.SECONDS) // 设置读取超时
		        .connectTimeout(60, TimeUnit.SECONDS) // 设置连接超时 
		        .build();;
		    MediaType JSON = MediaType.get("application/json; charset=utf-8"); // 在JSON数据中使用
		  
		    JSONObject message=new JSONObject();
		    message.put("role", "user");
		    message.put("content", content);
		    JSONArray ms=new JSONArray();
		    ms.add(message);
		    String param="{\"model\":\"deepseek-v3\",\"stream\":"+stream+",\"messages\":"+ms.toJSONString()+"}";
		    RequestBody body = RequestBody.create(JSON, param
		    		); // 示例JSON数据
		    Request request = new Request.Builder()
		        .url(QiNiuCnst.completions_url) // 目标URL
		        .post(body) // 使用POST方法并设置请求体
		        .addHeader("Authorization", key)
		        // 添加请求头，例如认证令牌
		        .build();
		    try (Response response = client.newCall(request).execute()) { // 使用try-with-resources确保响应被正确关闭
	            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
	            // 处理响应，例如读取响应体内容：
	            JSONObject  rs= JSONObject.parseObject(response.body().string());
	            return rs.getJSONArray("choices").getJSONObject(0)
	            		.getJSONObject("message").getString("content");
	        } catch (IOException e) {
	            e.printStackTrace(); // 处理异常，例如网络错误等。
	        }
		    
		return "无对应应答";
	}
 
	//获取apkey sk-b6f96665b92a524bf149ff53d74e074b3ca38b56b278c0544ae82b8174995a1f
	public static String getAppKey(){
		OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(QiNiuCnst.app_key_url).header("Authorization", QiNiuCnst.ak)
                .build();
 
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
            	return JSONObject.parseObject(response.body().string()).getString("api_key");
            } else {
                System.err.println("Request failed: " + response);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
	}
	
	

 
}
