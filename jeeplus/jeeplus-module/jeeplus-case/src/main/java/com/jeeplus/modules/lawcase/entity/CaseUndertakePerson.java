/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.modules.lawcase.entity.Case;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 案件承办人员Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseUndertakePerson extends DataEntity<CaseUndertakePerson> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="案件信息", align=2, sort=7)
	private Case lawCase;		// 案件信息
	@NotBlank(message = "姓名不能为空")
	@ExcelField(title="姓名", align=2, sort=8)
	private String name;		// 姓名
	@ExcelField(title="联系方式", align=2, sort=9)
	private String phone;		// 联系方式
	@ExcelField(title="科室", align=2, sort=10)
	private String department;		// 科室
	@ExcelField(title="职务", align=2, sort=11)
	private String post;		// 职务
	@ExcelField(title="联系地址", align=2, sort=12)
	private String address;		// 联系地址
	
	public CaseUndertakePerson() {
		super();
	}

	public CaseUndertakePerson(Case lawCase) {
		this.lawCase = lawCase;
	}

	public CaseUndertakePerson(String id){
		super(id);
	}
}