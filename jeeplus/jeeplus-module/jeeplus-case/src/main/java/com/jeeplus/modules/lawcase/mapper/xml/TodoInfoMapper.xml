<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.TodoInfoMapper">

	<resultMap id="todoInfoRelevanceResult" type="com.jeeplus.modules.lawcase.vo.TodoInfoRelevanceVO">
		<result property="relevanceType" column="relevanceType" />
		<result property="relevanceId" column="relevanceId" />

		<collection property="todoInfoList" ofType="com.jeeplus.modules.lawcase.entity.TodoInfo">
			<id property="id" column="todo.id" />
			<result property="name" column="todo.name" />
			<result property="content" column="todo.content" />
			<result property="status" column="todo.status" />
		</collection>
	</resultMap>

    <resultMap id="todoInfoResult" type="TodoInfo">
   		<result property="id" column="id" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<result property="parentIds" column="parentIds" />
		<result property="type" column="type" />
		<result property="content" column="content" />
		<result property="money" column="money" />
		<result property="status" column="status" />
		<result property="startDate" column="startDate" />
		<result property="endDate" column="endDate" />
		<result property="remindDate" column="remindDate" />
		<result property="completeDate" column="completeDate" />
		<result property="consumeTime" column="consumeTime" />
		<result property="hostUser.id" column="hostUser.id" />
		<result property="hostUser.name" column="hostUser.name" />
		<result property="relevanceType" column="relevanceType" />
		<result property="relevanceId" column="relevanceId" />
		<result property="stage.id" column="stage.id" />
		<result property="stage.name" column="stage.name" />
		<result property="isNotAuditProhibit" column="isNotAuditProhibit" />
    </resultMap>

	<sql id="todoInfoColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.type AS "type",
		a.name AS "name",
		a.content AS "content",
		a.money AS "money",
		a.status AS "status",
		a.start_date AS "startDate",
		a.end_date AS "endDate",
		a.remind_date AS "remindDate",
		a.complete_date AS "completeDate",
		a.consume_time AS "consumeTime",
		a.host_user_id AS "hostUser.id",
		a.relevance_type AS "relevanceType",
		a.relevance_id AS "relevanceId",
		a.stage_id AS "stage.id",
		a.stage_name AS "stage.name",
		a.parent_id AS "parent.id",
		a.parent_ids AS "parentIds",
		a.sort AS "sort", 
		a.is_not_audit_prohibit AS "isNotAuditProhibit",

		hostUser.name AS "hostUser.name"
	</sql>




	<sql id="todoInfoJoins">

		LEFT JOIN sys_user hostUser ON hostUser.id = a.host_user_id
	</sql>



	<select id="get" resultType="TodoInfo">
		SELECT
			<include refid="todoInfoColumns"/>
		FROM law_todo_info a
		<include refid="todoInfoJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="getCountByParentId" resultType="Integer">
		SELECT COUNT(*) FROM law_todo_info a WHERE a.parent_id = #{id} AND a.status = #{status}
	</select>

	<select id="findList" resultType="TodoInfo">
		SELECT
			<include refid="todoInfoColumns"/>
			<if test="checkWord != null and checkWord != '' ">
				,(SELECT (CASE WHEN  COUNT(1) > 0 THEN "1" ELSE "0" END) FROM law_todo_info_file WHERE todo_id = a.id and edit_flag in(1,2) ) AS "isEditFile"
			</if>
			<if test="checkWord != null and checkWord != '' and checkWord == 'checkFile'">
				,(SELECT (CASE WHEN  COUNT(1) > 0 THEN "1" ELSE "0" END) FROM law_todo_info_file WHERE todo_id = a.id) AS "isHaveFile"
			</if>
			<if test="checkWord != null and checkWord != '' and checkWord == 'checkDoc'">
				,(SELECT (CASE WHEN  COUNT(1) > 0 THEN "1" ELSE "0" END) FROM law_todo_info_file WHERE todo_id = a.id and (UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'PNG' or UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'PDF' or  UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'GIF' or UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'JPEG' or UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'JPG' )) AS "isHaveDoc"
			</if>
			 <if test="checkWord != null and checkWord != '' and checkWord == 'checkDoc'">
				,(SELECT (CASE WHEN  COUNT(1) > 0 THEN "1" ELSE "0" END) FROM law_todo_info_file WHERE todo_id = a.id and (UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'DOCX' or UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'DOC'  or  UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'XLSX' or  UPPER(SUBSTRING_INDEX(path, '.', -1)) = 'XLS' )) AS "isHaveFile"
			</if>
		FROM law_todo_info a
		<include refid="todoInfoJoins"/>
		<if test="relevanceType != null and relevanceType != '' and relevanceType eq '2'.toString() ">
			LEFT JOIN law_case_stage cs ON a.stage_id = cs.id AND cs.case_id = a.relevance_id
		</if>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="relevanceType != null and relevanceType != ''">
				AND a.relevance_type = #{relevanceType}
			</if>
			<if test="relevanceId != null and relevanceId != ''">
				AND a.relevance_id = #{relevanceId}
			</if>
			<if test="stage != null and stage.id != null and stage.id != ''">
				AND a.stage_id = #{stage.id}
			</if>
			<if test="hostUser != null and hostUser.id != null and hostUser.id != ''">
				AND a.host_user_id = #{hostUser.id}
			</if>
			<if test="parent != null and parent.id != null and parent.id != ''">
				AND a.parent_id = #{parent.id}
			</if>
			<if test="parentIds != null and parentIds != ''">
				AND a.parent_ids LIKE concat('%',#{parentIds},'%') 
			</if>
			<if test="checkWord != null and checkWord != '' and checkWord == 'overdue'">
				AND a.remind_date IS NOT NULL
				AND DATE(a.remind_date)  <![CDATA[  <  ]]> CURDATE()
			</if>
			<if test="checkWord != null and checkWord != '' and checkWord == 'plan'">
				AND a.remind_date IS NOT NULL
				AND DATE(a.remind_date)  <![CDATA[  >=  ]]> CURDATE()
			</if>
		</where>
		ORDER BY
			<if test="relevanceType != null and relevanceType != '' and relevanceType eq '2'.toString() ">
				cs.sort,
			</if>
			a.start_date, a.sort, a.create_date
	</select>

	<select id="findTodoStageList" resultType="TodoInfo">
		SELECT a.id, a.name, a.stage_id AS "stage.id", cs.name AS "stage.name"
		FROM (
			SELECT a.id, a.name, a.stage_id
			FROM law_todo_info a
			WHERE a.del_flag = #{DEL_FLAG_NORMAL}
				AND a.relevance_type = #{relevanceType}
				AND a.relevance_id = #{relevanceId}
		) a
		INNER JOIN law_case_stage cs ON a.stage_id = cs.id AND cs.case_id = #{relevanceId}
	</select>

	<select id="findHostUserStatisticList" resultType="Map">
		SELECT a.host_user_id AS "hostUserId", hu.login_name AS "hostUserLoginName", COUNT(a.id) AS "amount"
		FROM law_todo_info a
		LEFT JOIN sys_user hu ON hu.id = a.host_user_id
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			AND a.host_user_id IS NOT NULL AND a.host_user_id != ''
			AND a.remind_date IS NOT NULL
			AND DATE(a.remind_date) <![CDATA[ = ]]> DATE(#{queryEndDate})
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="relevanceType != null and relevanceType != ''">
				AND a.relevance_type = #{relevanceType}
			</if>
			<if test="relevanceId != null and relevanceId != ''">
				AND a.relevance_id = #{relevanceId}
			</if>
			<if test="stage != null and stage.id != null and stage.id != ''">
				AND a.stage_id = #{stage.id}
			</if>
			<if test="hostUser != null and hostUser.id != null and hostUser.id != ''">
				AND a.host_user_id = #{hostUser.id}
			</if>
		</where>
		GROUP BY a.host_user_id
	</select>

	<select id="findListByMonth" resultType="TodoInfo">
		SELECT a.id AS "id",
		a.name AS "name",
		a.content AS "content",
		a.status AS "status",
		a.start_date AS "startDate",
		IFNULL(a.end_date, a.start_date)  AS "endDate",
		a.remind_date AS "remindDate",
		ls.name AS "lawCase.name",
		ls.type AS "lawCase.type",
		ls.case_program_name AS "lawCase.caseProgram.name",
		ls.case_cause_name AS "lawCase.caseCause.name",
		ls.entrust_date AS "lawCase.entrustDate",
		cs.name AS "stage.name"
		FROM law_todo_info a
		LEFT JOIN law_case ls ON a.relevance_id = ls.id
		LEFT JOIN law_case_stage cs ON a.stage_id = cs.id AND cs.case_id = a.relevance_id
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			AND a.remind_date IS NOT NULL
			AND DATE(a.remind_date) BETWEEN DATE(#{queryStartDate}) AND DATE(#{queryEndDate})
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="relevanceType != null and relevanceType != ''">
				AND a.relevance_type = #{relevanceType}
			</if>
			<if test="relevanceId != null and relevanceId != ''">
				AND a.relevance_id = #{relevanceId}
			</if>
			<if test="stage != null and stage.id != null and stage.id != ''">
				AND a.stage_id = #{stage.id}
			</if>
			<if test="hostUser != null and hostUser.id != null and hostUser.id != ''">
				AND a.host_user_id = #{hostUser.id}
			</if>
		</where>
		ORDER BY a.remind_date ASC
	</select>

	<select id="findGroupList" resultMap="todoInfoRelevanceResult" >
		SELECT a.relevance_id AS "relevanceId", a.id AS "todo.id", a.name AS "todo.name", a.content AS "todo.content", a.status AS "todo.status"
		FROM law_todo_info a
		INNER JOIN(
			SELECT a.relevance_id, SUBSTRING_INDEX( GROUP_CONCAT(a.id ORDER BY start_date), ',', 2) AS "ids"
			FROM law_todo_info a
			WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND a.remind_date IS NOT NULL AND a.status = #{status} AND a.relevance_type = #{relevanceType}
				AND FIND_IN_SET(a.relevance_id, #{queryRelevanceIds})
			GROUP BY a.relevance_id
		) b ON a.relevance_id = b.relevance_id AND FIND_IN_SET(a.id, b.ids)
		ORDER BY a.start_date, a.create_date
	</select>

	<select id="findAllList" resultType="TodoInfo">
		SELECT
			<include refid="todoInfoColumns"/>
		FROM law_todo_info a
		<include refid="todoInfoJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="getChildren" parameterType="String" resultMap="todoInfoResult">
        select * from law_todo_info where parent_id = #{id} ORDER BY sort
    </select>

	<select id="findByParentIdsLike" resultType="TodoInfo">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM law_todo_info a
		<include refid="todoInfoJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			AND a.parent_ids LIKE #{parentIds}
		</where>
		ORDER BY a.sort ASC
	</select>

	<insert id="insert">
		INSERT INTO law_todo_info(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
			   `type`,
			   `name`,
			   content,
			   money,
			   status,
			   start_date,
			   end_date,
			   remind_date,
			   complete_date,
			   consume_time,
			   host_user_id,
			   relevance_type,
			   relevance_id,
			   stage_id,
			   stage_name,
			   parent_id,
			   parent_ids,
			   sort,
			   is_not_audit_prohibit
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{type},
			#{name},
			#{content},
			#{money},
			#{status},
			#{startDate},
			#{endDate},
			#{remindDate},
			#{completeDate},
			#{consumeTime},
			#{hostUser.id},
			#{relevanceType},
			#{relevanceId},
			#{stage.id},
			#{stage.name},
			#{parent.id},
			#{parentIds},
			#{sort},
			#{isNotAuditProhibit}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_todo_info(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
			   `type`,
			   `name`,
			   content,
				money,
			   status,
			   start_date,
			   end_date,
				remind_date,
				complete_date,
			   consume_time,
			   host_user_id,
			   relevance_type,
			   relevance_id,
			   stage_id,
			   stage_name,
			   parent_id,
			   parent_ids,
			   sort,
				is_not_audit_prohibit
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.type},
			#{item.name},
			#{item.content},
			#{item.money},
			#{item.status},
			#{item.startDate},
			#{item.endDate},
			#{item.remindDate},
			#{item.completeDate},
			#{item.consumeTime},
			#{item.hostUser.id},
			#{item.relevanceType},
			#{item.relevanceId},
			#{item.stage.id},
			#{item.stage.name},
			#{item.parent.id},
			#{item.parentIds},
			#{item.sort},
			#{item.isNotAuditProhibit}
			)
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_todo_info SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			`type` = #{type},
			`name` = #{name},
			content = #{content},
			money = #{money},
			status = #{status},
			start_date = #{startDate},
			end_date = #{endDate},
			remind_date = #{remindDate},
			consume_time = #{consumeTime},
			host_user_id = #{hostUser.id},
			relevance_type = #{relevanceType},
			relevance_id = #{relevanceId},
			stage_id = #{stage.id},
			stage_name = #{stage.name},
			parent_id = #{parent.id},
			parent_ids = #{parentIds},
			sort = #{sort},
			is_not_audit_prohibit = #{isNotAuditProhibit}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE law_todo_info SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<update id="updateCurrentAndChildrenStatus">
		UPDATE law_todo_info SET status = #{status}, complete_date = #{completeDate}
		WHERE id = #{id} OR parent_ids LIKE CONCAT('%,', #{id}, ',%') 
	</update>

	<update id="updateCurrentStatus">
		UPDATE law_todo_info SET status = #{status}, complete_date = #{completeDate}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_todo_info
		WHERE id = #{id} OR parent_ids LIKE CONCAT('%,', #{id}, ',%') 
	</update>

	<update id="deleteByRelevance">
		DELETE FROM law_todo_info
		WHERE relevance_type = #{relevanceType} AND relevance_id = #{relevanceId}
		<if test="stage != null and stage.id != null and stage.id != ''">
			AND stage_id = #{stage.id}
		</if>
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_todo_info SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE CONCAT('%,', #{id}, ',%') 
	</update>

</mapper>