/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.entity.CaseStage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 案件阶段MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CaseStageMapper extends BaseMapper<CaseStage> {
    /**
     * 根据 案件id 查询阶段排序最大值
     * @param lawCaseId
     * @return
     */
    int getMaxSortByCase(String lawCaseId);

    /**
     * 根据排序范围 更新排序值
     * @param difference    排序差值
     * @param lawCaseId     案件id
     * @param startSort     范围起始值
     * @param endSort       范围结束值
     * @return
     */
    int updateSortByRange(@Param("difference") Integer difference, @Param("lawCaseId") String lawCaseId
            , @Param("startSort") Integer startSort, @Param("endSort") Integer endSort);

    /**
     * 根据id 更新排序值
     * @param sort
     * @param id
     * @return
     */
    int updateSortById(@Param("sort") Integer sort, @Param("id") String id);

    /**
     * 更新当前状态
     * @param isCurrent     新状态值
     * @param lawCaseId     案件id
     * @param id            当前阶段id
     * @param oldIsCurrent  原状态值
     * @return
     */
   int updateCurrent(@Param("isCurrent") String isCurrent, @Param("lawCaseId") String lawCaseId
           , @Param("id") String id, @Param("oldIsCurrent") String oldIsCurrent);

    /**
     * 根据案件id 删除案件阶段信息
     * @param lawCaseId
     * @return
     */
   int deleteByCase(String lawCaseId);
}