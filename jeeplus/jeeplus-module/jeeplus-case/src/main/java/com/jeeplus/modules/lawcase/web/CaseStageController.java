/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseStage;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.entity.StageTemplate;
import com.jeeplus.modules.lawcase.service.CaseService;
import com.jeeplus.modules.lawcase.service.CaseStageService;
import com.jeeplus.modules.lawcase.service.StageService;
import com.jeeplus.modules.lawcase.service.StageTemplateService;
import com.jeeplus.modules.lawcase.vo.StageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 案件阶段Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-阶段")
@RequestMapping(value = "/lawcase/caseStage")
public class CaseStageController extends BaseController {

	@Autowired
	private CaseService caseService;
	@Autowired
	private CaseStageService caseStageService;
	@Autowired
	private StageTemplateService stageTemplateService;
	@Autowired
	private StageService stageService;

	@ModelAttribute
	public CaseStage get(@RequestParam(required=false) String id) {
		CaseStage entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseStageService.get(id);
		}
		if (entity == null){
			entity = new CaseStage();
		}
		return entity;
	}

	/**
	 * 案件阶段列表数据
	 */
//	@RequiresPermissions("lawcase:caseStage:list")
	@ApiOperation(value = "阶段信息列表", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = true)
	})
	@PostMapping("list")
	public AjaxJson list(CaseStage caseStage, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseStage> page = new Page<CaseStage>(request, response, -1);
		if(caseStage.getLawCase() != null && StringUtils.isNotBlank(caseStage.getLawCase().getId())){
			// 检测字段 统计待办事项数量
			caseStage.setCheckWord( CaseStage.CHECK_WORD_STATISTIC_TODO );
			page = caseStageService.findPage(page, caseStage);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取案件阶段数据
	 */
//	@RequiresPermissions(value={"lawcase:caseStage:view","lawcase:caseStage:add","lawcase:caseStage:edit"},logical=Logical.OR)
	@ApiOperation(value = "详情", consumes = "application/form-data")
	@PostMapping("queryById")
	public AjaxJson queryById(CaseStage caseStage) {
		return AjaxJson.success().put("caseStage", caseStage);
	}

	/**
	 * 保存案件阶段
	 */
	@RequiresPermissions(value={"lawcase:caseStage:add","lawcase:caseStage:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存阶段信息", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id", name = "id"),
			@ApiImplicitParam(value = "案件信息id", name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "阶段名称", name = "name", required = true)
	})
	@PostMapping("save")
	public AjaxJson save(CaseStage caseStage) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseStage);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseStage.getLawCase() == null || StringUtils.isBlank(caseStage.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		if(StringUtils.isBlank(caseStage.getIsCurrent())){
			caseStage.setIsCurrent(JeePlusProperites.NO);
		}

		try {
			caseStageService.save(caseStage);//保存
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}

	}

	@ApiOperation(value = "设置当前阶段", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "阶段id", name = "id", required = true)
	})
	@PostMapping("setCurrent")
	public AjaxJson setCurrent(CaseStage caseStage) throws Exception{
		if(StringUtils.isBlank(caseStage.getId())){
			return AjaxJson.error("阶段信息有误");
		}
		if(caseStage.getLawCase() == null || StringUtils.isBlank(caseStage.getLawCase().getId())){
			return AjaxJson.error("关联案件信息有误");
		}
		if(JeePlusProperites.YES.equals(caseStage.getIsCurrent())){
			return AjaxJson.success("设置成功");
		}
		try{
			caseStageService.updateCurrent(caseStage);
			return AjaxJson.success("设置成功");
		}catch (Exception e){
			return AjaxJson.error("阶段信息有误");
		}
	}

	@ApiOperation(value = "更新排序", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "阶段id", name = "id", required = true),
			@ApiImplicitParam(value = "新排序值", name = "newSort", required = true)
	})
	@PostMapping("changeSort")
	public AjaxJson changeSort(CaseStage caseStage, Integer newSort ) throws Exception{
		if(StringUtils.isBlank(caseStage.getId()) || StringUtils.isBlank(caseStage.getName()) || caseStage.getSort() == null){
			return AjaxJson.error("阶段信息有误");
		}
		if(newSort == null || newSort < 1){
			return AjaxJson.error("排序值信息有误");
		}
		if( newSort.equals(caseStage.getSort())){
			return AjaxJson.success("排序成功");
		}
		try{
			caseStageService.updateSort(caseStage, newSort);
			return AjaxJson.success("排序成功");
		}catch (Exception e){
			return AjaxJson.error("排序失败");
		}
	}

	@RequiresPermissions(value={"lawcase:caseStage:add", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存阶段信息-根据阶段模版", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "lawCaseId", required = true),
			@ApiImplicitParam(value = "阶段模版id", name = "templateId", required = true)
	})
	@PostMapping("saveStage")
	public AjaxJson saveStage(String lawCaseId, String templateId ) throws Exception{
		if(StringUtils.isBlank(lawCaseId) || StringUtils.isBlank(templateId)){
			return AjaxJson.error("参数错误");
		}
		Case lawCase = caseService.get(lawCaseId);
		if(lawCase == null || StringUtils.isBlank(lawCase.getId())){
			return AjaxJson.error("案件信息有误");
		}
		StageTemplate stageTemplate = stageTemplateService.get(templateId);
		if(stageTemplate == null || StringUtils.isBlank(stageTemplate.getId())){
			return AjaxJson.error("模版信息有误");
		}
		Stage stage = new Stage();
		stage.setStageTemplate(stageTemplate);
		// 获取阶段、阶段待办事项信息
		List<StageVO> stageList = stageService.findListByTemplate(stage);
		if(stageList == null || stageList.size() == 0){
			return AjaxJson.error("模版阶段信息为空");
		}
		try{
			caseStageService.saveBatchByTemplate(lawCase, stageTemplate, stageList);
			return AjaxJson.success("保存成功");
		}catch (Exception e){
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}

	/**
	 * 批量删除案件阶段
	 */
//	@RequiresPermissions("lawcase:caseStage:del")
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值拼接以,拼接字符串", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseStageService.delete(caseStageService.get(id));
		}
		return AjaxJson.success("删除案件阶段成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("lawcase:caseStage:export")
    @GetMapping("export")
    public AjaxJson exportFile(CaseStage caseStage, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "案件阶段"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CaseStage> page = caseStageService.findPage(new Page<CaseStage>(request, response, -1), caseStage);
    		new ExportExcel("案件阶段", CaseStage.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出案件阶段记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("lawcase:caseStage:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CaseStage> list = ei.getDataList(CaseStage.class);
			for (CaseStage caseStage : list){
				try{
					caseStageService.save(caseStage);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条案件阶段记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条案件阶段记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入案件阶段失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入案件阶段数据模板
	 */
	@RequiresPermissions("lawcase:caseStage:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "案件阶段数据导入模板.xlsx";
    		List<CaseStage> list = Lists.newArrayList();
    		new ExportExcel("案件阶段数据", CaseStage.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}