/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.CaseStage;
import com.jeeplus.modules.lawcase.entity.TodoInfo;
import com.jeeplus.modules.lawcase.entity.TodoInfoFile;
import com.jeeplus.modules.lawcase.service.TodoInfoService;
import com.jeeplus.modules.office.entity.DocTemplate;
import com.jeeplus.modules.office.service.DocTemplateService;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.FileKit;
import com.jeeplus.modules.sys.utils.UserUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 待办事项Controller
 * <AUTHOR>
 * @version 2021-08-12
 */
@RestController
@Api(tags = "待办事项")
@RequestMapping(value = "/lawcase/todoInfo")
public class TodoInfoController extends BaseController {

	@Autowired
	private TodoInfoService todoInfoService;
	@Autowired
	private DocTemplateService docTemplateService;

	@ModelAttribute
	public TodoInfo get(@RequestParam(required=false) String id) {
		TodoInfo entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = todoInfoService.get(id);
		}
		if (entity == null){
			entity = new TodoInfo();
		}
		return entity;
	}


	/**
	 * 待办事项树表数据
	 */
	@RequiresPermissions("lawcase:todoInfo:list")
	@GetMapping("list")
	public AjaxJson list(TodoInfo todoInfo) {
		return AjaxJson.success().put("list", todoInfoService.findList(todoInfo));
	}

	@ApiOperation(value = "月视图数据", consumes = "application/form-data")
	@RequiresPermissions(value = {"lawcase:todoInfo:list","user"}, logical = Logical.OR)
	@ApiImplicitParams({
			@ApiImplicitParam(value = "开始日期", name = "queryStartDate", required = true),
			@ApiImplicitParam(value = "结束日期", name = "queryEndDate", required = true)
	})
	@PostMapping("monthList")
	public AjaxJson monthList(TodoInfo todoInfo) {
		List<TodoInfo> todoInfoList = new ArrayList<>();
		if(todoInfo.getQueryStartDate() != null && todoInfo.getQueryEndDate() != null
				&& todoInfo.getQueryEndDate().after(todoInfo.getQueryStartDate())){
			// 未完成的任务
			todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
//			todoInfo.setType(CaseConstant.TODO_TYPE_TASK);
			todoInfo.setHostUser(UserUtils.getUser());
			todoInfoList = todoInfoService.findListByMonth(todoInfo);
		}
		return AjaxJson.success().put("data", todoInfoList);
	}

	@ApiOperation("统计列表，已过期、计划中")
	@RequiresPermissions(value = {"lawcase:todoInfo:list","user"}, logical = Logical.OR)
	@GetMapping("statisticList")
	public AjaxJson statisticList() {
		// 未完成的任务
		TodoInfo todoInfo = new TodoInfo();
		todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
//		todoInfo.setType(CaseConstant.TODO_TYPE_TASK);
		todoInfo.setHostUser(UserUtils.getUser());
		// 过期列表
		todoInfo.setCheckWord( TodoInfo.CHECK_WORD_OVERDUE );
		List<TodoInfo> overdueList = todoInfoService.findList(todoInfo);
		// 待计划列表
		todoInfo.setCheckWord( TodoInfo.CHECK_WORD_PLAN );
		List<TodoInfo> planList = todoInfoService.findList(todoInfo);
		Map<String, List<TodoInfo>> map = new HashMap<String, List<TodoInfo>>(2);
		map.put("overdueData", overdueList);
		map.put("planData", planList);
		return AjaxJson.success().putMap(map);
	}

	@RequiresPermissions(value = {"lawcase:todoInfo:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "待办事项列表-案件", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "relevanceId", required = true),
			@ApiImplicitParam(value = "阶段id", name = "stage.id")
	})
	@PostMapping("caseList")
	public AjaxJson caseList(TodoInfo todoInfo) {
		todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CASE);
		return AjaxJson.success().put("list", todoInfoService.findTreeList(todoInfo));
	}

	@RequiresPermissions(value = {"lawcase:todoInfo:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "待办事项列表-客户", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "客户id", name = "relevanceId", required = true)
	})
	@PostMapping("customerList")
	public AjaxJson customerList(TodoInfo todoInfo) {
		todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CUSTOMER);
		return AjaxJson.success().put("list", todoInfoService.findTreeList(todoInfo));
	}

	/**
	 * 根据Id获取待办事项数据
	 */
	@RequiresPermissions(value={"lawcase:todoInfo:view","lawcase:todoInfo:add","lawcase:todoInfo:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "详情信息", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(TodoInfo todoInfo) {
		if(StringUtils.isNotBlank(todoInfo.getId())){
			List<TodoInfoFile> fileList = todoInfoService.findFileList(new TodoInfoFile(todoInfo));
			todoInfo.setFileList(fileList);
		}
		return AjaxJson.success().put("todoInfo", todoInfo);
	}
	
	@RequiresPermissions(value={"lawcase:todoInfo:view","lawcase:todoInfo:add","lawcase:todoInfo:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "详情信息", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById2")
	public AjaxJson queryById2(TodoInfo todoInfo) {
		if(StringUtils.isNotBlank(todoInfo.getId())){
			List<TodoInfoFile> fileList = todoInfoService.findFileList(new TodoInfoFile(todoInfo));
			List<TodoInfoFile> fileList1=new ArrayList<>();
			List<TodoInfoFile> fileList2=new ArrayList<>();
			User user=UserUtils.getUser();
			todoInfo.setDataCode(user.getDataCode());
			for(TodoInfoFile fileInfo:fileList) {
				
				/*if(fileInfo.getFileType()!=null && 
						
						("PDF".equals(fileInfo.getFileType().toUpperCase())
								|| "JPG".equals(fileInfo.getFileType().toUpperCase())
								||"JPEG".equals(fileInfo.getFileType().toUpperCase())
								||"PNG".equals(fileInfo.getFileType().toUpperCase()))){
					fileList2.add(fileInfo);
				}else {*/
					fileList1.add(fileInfo);
				//}
			}
			
			todoInfo.setFileList(fileList1);
			todoInfo.setFileList2(fileList2);
		}
		return AjaxJson.success().put("todoInfo", todoInfo);
	}
	

	/**
	 * 保存待办事项
	 */
	@RequiresPermissions(value={"lawcase:todoInfo:add","lawcase:todoInfo:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存待办事项")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id", required = false)
	})
	@PostMapping("save")
	public AjaxJson save(@RequestBody TodoInfo todoInfo) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(todoInfo);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		List<String> relevanceTypeList = Arrays.asList(CaseConstant.TODO_RELEVANCE_TYPE_NOT, CaseConstant.TODO_RELEVANCE_TYPE_CUSTOMER, CaseConstant.TODO_RELEVANCE_TYPE_CASE);
		if(!relevanceTypeList.contains(todoInfo.getRelevanceType())){
			return AjaxJson.error("参数错误");
		}
		if(StringUtils.isBlank(todoInfo.getName())){
			return AjaxJson.error("请输入工作摘要");
		}
		// 判断待办事项级别 TODO 2021-09-08 废弃 变更为多级待办事项
//		try{
//			AjaxJson j = todoInfoService.checkTodoLevel(todoInfo);
//			if(!j.isSuccess()){
//				return j;
//			}
//			// 获取处理后的待办事项 进行后续处理
//			todoInfo = (TodoInfo)j.get("todoInfo");
//		}catch (Exception e){
//			return AjaxJson.success("保存失败");
//		}

		// 校验关联信息、关联类型  默认 不关联类型为任务类别
		if(CaseConstant.TODO_RELEVANCE_TYPE_NOT.equals(todoInfo.getRelevanceType())){
			todoInfo.setRelevanceId("");
//			todoInfo.setType(CaseConstant.TODO_TYPE_TASK);
			todoInfo.setStage(new CaseStage());
		}else if(CaseConstant.TODO_RELEVANCE_TYPE_CUSTOMER.equals(todoInfo.getRelevanceType())){
			if(StringUtils.isBlank(todoInfo.getRelevanceId())){
				return AjaxJson.error("请选择客户信息");
			}
			todoInfo.setStage(new CaseStage());
		}else if(CaseConstant.TODO_RELEVANCE_TYPE_CASE.equals(todoInfo.getRelevanceType())){
			if(StringUtils.isBlank(todoInfo.getRelevanceId())){
				return AjaxJson.error("请选择案件信息");
			}
			if(todoInfo.getStage() == null || StringUtils.isBlank(todoInfo.getStage().getId())){
				return AjaxJson.error("请选择案件阶段信息");
			}
		}

		// 查询原有数据 进行替换
		if(StringUtils.isNotBlank(todoInfo.getId())){
			TodoInfo oldTodoInfo = todoInfoService.get(todoInfo.getId());
			Double money = todoInfo.getMoney();
			// 属性替换  对为null的属性进行 赋值
			todoInfo = CaseConstant.classCopyProperties(todoInfo, oldTodoInfo);

			todoInfo.setMoney(money);
		}
		if(StringUtils.isBlank(todoInfo.getIsNotAuditProhibit())){
			todoInfo.setIsNotAuditProhibit(JeePlusProperites.NO);
		}
		// 根据 内容详情判断状态（已办、未办）
		todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
		if(StringUtils.isNotBlank( StringUtils.trim(todoInfo.getContent()) )){
			todoInfo.setStatus(CaseConstant.TODO_STATUS_COMPLETE);
		}
		try {
			todoInfoService.saveInfo(todoInfo);//保存
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.success("保存失败");
		}
	}

	/**
	 * 删除待办事项
	 */
	@RequiresPermissions(value = {"lawcase:todoInfo:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除待办事项", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值, 多个以,拼接", name = "ids", required = true)
	})
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			todoInfoService.delete(todoInfoService.get(id));
		}
		return AjaxJson.success("删除待办事项成功");
	}

	@RequiresPermissions(value = "user")
	@ApiOperation(value = "待办事项-附件列表记录", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "待办事项id值", name = "todoId", required = true)
	})
	@PostMapping("fileList")
	public AjaxJson fileList(String todoId) {
		List<TodoInfoFile> todoFileList = new ArrayList<>();
		if(StringUtils.isNotBlank(todoId)){
			TodoInfoFile todoInfoFile = new TodoInfoFile(new TodoInfo(todoId));
			todoFileList = todoInfoService.findFileList(todoInfoFile);
		}
		return AjaxJson.success().put("list", todoFileList);
	}

	@RequiresPermissions(value="user")
	@ApiOperation(value = "模版使用", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "待办事项id",name = "todoInfoId", required = true),
			@ApiImplicitParam(value = "模版id值", name = "templateId", required = true)
	})
	@PostMapping("templateCopy")
	public AjaxJson templateCopy(String todoInfoId, String templateId) throws Exception{
		if(StringUtils.isBlank(todoInfoId) || StringUtils.isBlank(templateId)){
			return AjaxJson.error("参数错误");
		}
		DocTemplate docTemplate = docTemplateService.get(templateId);
		if(docTemplate == null || StringUtils.isBlank(docTemplate.getId())){
			return AjaxJson.error("模版信息有误");
		}
		if(StringUtils.isBlank(docTemplate.getPath())){
			return AjaxJson.error("模版文件不存在！");
		}
		try {
			AjaxJson j = FileKit.fileCopy(FileKit.getFileDir(docTemplate.getPath()), TodoInfoFile.FILE_PATH);
			if(!j.isSuccess()){
				return j;
			}
			TodoInfoFile todoInfoFile = new TodoInfoFile();
			todoInfoFile.setTodoInfo(new TodoInfo(todoInfoId));
			todoInfoFile.setPath( j.get("url").toString());
			todoInfoFile.setName( String.valueOf(j.get("name")) );
			todoInfoFile.setFileversion(1);
			todoInfoFile.setFilesizes(100);
			Integer tsort=todoInfoService.getMaxSortByTid(todoInfoFile.getId());
			if(tsort==null) {
				tsort=0;
			}
			todoInfoFile.setSort(tsort+1);
			todoInfoService.saveFile(todoInfoFile);
			j.setMsg("使用成功");
			return j;
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("使用失败");
		}
	}

	@RequiresPermissions(value = "user")
	@ApiOperation(value = "待办事项-附件上传", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "待办事项id值", name = "todoInfo.id", required = true),
			@ApiImplicitParam(value = "文件", name = "file", required = true)
	})
	@PostMapping("fileUpload")
	public AjaxJson fileUpload(TodoInfoFile todoInfoFile, MultipartFile file) {
		if(todoInfoFile.getTodoInfo() == null || StringUtils.isBlank(todoInfoFile.getTodoInfo().getId())){
			return AjaxJson.error("待办事项信息有误");
		}
		try {
			AjaxJson j = FileKit.fileUpload(file, TodoInfoFile.FILE_PATH);
			if(!j.isSuccess()){
				return j;
			}
			todoInfoFile.setName(String.valueOf(j.get("name")));
			todoInfoFile.setPath(String.valueOf(j.get("url")));
			todoInfoFile.setFileversion(1);
			todoInfoFile.setFilesizes(Long.valueOf(file.getSize()).intValue());
			Integer tsort=todoInfoService.getMaxSortByTid(todoInfoFile.getId());
			if(tsort==null) {
				tsort=0;
			}
			todoInfoFile.setDocType(3);//上传的是案件文档
			todoInfoFile.setSort(tsort+1);
			todoInfoFile.setEditFlag(2);
			todoInfoService.saveFile(todoInfoFile);
			return AjaxJson.success("上传成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("上传失败");
		}
	}
	
	@ApiOperation(value = "覆盖上传", consumes = "application/form-data")
	@PostMapping("fileUploadOverWrite")
	public AjaxJson fileUploadOverWrite(String  path, MultipartFile file) {
		try {
		   FileKit.fileUpload2(file, path);
			return AjaxJson.success("上传成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("上传失败");
		}
	}
	

	@RequiresPermissions(value = "user")
	@ApiOperation(value = "删除待办事项-附件记录", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值, 多个以,拼接", name = "ids", required = true)
	})
	@PostMapping("deleteFile")
	public AjaxJson deleteFile(String ids) {
		if(StringUtils.isBlank(ids)){
			return AjaxJson.error("参数错误");
		}
		String idArray[] =ids.split(",");
		for(String id : idArray){
			todoInfoService.deleteFile(id);
		}
		return AjaxJson.success("删除成功");
	}

	/**
	 * 更新待办事项状态
	 * @param id
	 * @param status
	 * @return
	 */
	@RequiresPermissions(value = "user")
	@ApiOperation(value = "更新待办事项状态", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id", required = true),
			@ApiImplicitParam(value = "状态值  1.未办  2.已办", name = "status", required = true)
	})
	@PostMapping("changeStatus")
	public AjaxJson changeStatus(String id, String status) {
		if(StringUtils.isBlank(id) || StringUtils.isBlank(status)){
			return AjaxJson.error("参数有误");
		}
		List<String> statusList = Arrays.asList(CaseConstant.TODO_STATUS_WAIT, CaseConstant.TODO_STATUS_COMPLETE);
		if(!statusList.contains(status)){
			return AjaxJson.error("参数有误");
		}
		TodoInfo todoInfo = todoInfoService.get(id);
		if(todoInfo == null || StringUtils.isBlank(todoInfo.getId())){
			return AjaxJson.error("案件信息有误");
		}

		AjaxJson j = AjaxJson.success("操作成功");
		if(!status.equals(todoInfo.getStatus())){
			// 设置完成时间 与 状态
			if(CaseConstant.TODO_STATUS_COMPLETE.equals(status)){
				todoInfo.setCompleteDate(new Date());
			}else {
				todoInfo.setCompleteDate(null);
			}
			todoInfo.setStatus(status);
			try {
				todoInfoService.updateStatus(todoInfo);
			}catch (Exception e){
				return AjaxJson.error("操作失败");
			}
		}
		j.put("data", todoInfo);
		return j;
	}

	/**
     * 获取JSON树形数据。
     * @param extId 排除的ID
     * @return
	*/
	@RequiresPermissions("user")
	@GetMapping("treeData")
	public AjaxJson treeData(@RequestParam(required = false) String extId) {
		List<TodoInfo> list = todoInfoService.findList(new TodoInfo());
		List rootTree =  todoInfoService.formatListToTree (new TodoInfo ("0"),list, extId );
		return AjaxJson.success().put("treeData", rootTree);
	}

}