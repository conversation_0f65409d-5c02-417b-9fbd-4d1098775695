/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;


import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 阶段信息 VO
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class StageVO implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;		// id
	private String stageTemplateId;		// 模版id
	private String name;		// 名称
	private Integer sort;		// 排序

	private List<StageRecordVO> stageRecordList = Lists.newArrayList();	// 阶段记录列表

	public StageVO() {
		super();
	}

}