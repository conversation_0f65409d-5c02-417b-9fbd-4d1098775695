/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.sys.utils.FileKit;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 财务流水记录-附件 Entity
 * <AUTHOR>
 * @version 2021-08-12
 */
@Data 
public class FinanceFlowFile extends DataEntity<FinanceFlowFile> {

	private static final long serialVersionUID = 1L;

	private FinanceFlowRecord financeFlowRecord;		// 财务流水
	@NotNull
	private String name;			// 文件名称
	@NotNull
	private String path;			// 文件路径


	// 虚拟
	private String fullPath;	// 完整文件路径
	private String fileType;	// 文件类型

	public FinanceFlowFile() {
		super();
	}

	public FinanceFlowFile(String id){
		super(id);
	}

	public FinanceFlowFile(FinanceFlowRecord financeFlowRecord){
		this.financeFlowRecord = financeFlowRecord;
	}

	public String getFullPath() {
		if(StringUtils.isNotBlank(this.path)){
			this.fullPath = (JeePlusProperites.domainNameValue() + this.path);
		}
		return fullPath;
	}

	public String getFileType() {
		this.fileType = FileKit.getFileSuffix(this.path);
		return fileType;
	}
}