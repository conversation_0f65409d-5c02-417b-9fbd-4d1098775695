package com.jeeplus.modules.lawcase.entity;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.sys.utils.FileKit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 待办事项附件
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StageRecordFile {
    /**
     * 主键
     */
    private String id;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 文件名
     */
    private String name;

    /**
     * 所属模板记录id
     */
    private String recordId;
    
    /**
     * 模板记录id
     */
    private String templateId;
    
    private Integer sort;

    /**
     * 上传路径
     */
    private String path;

    public static final String COL_ID = "id";

    public static final String COL_CREATE_BY = "create_by";

    public static final String COL_CREATE_DATE = "create_date";

    public static final String COL_UPDATE_BY = "update_by";

    public static final String COL_UPDATE_DATE = "update_date";

    public static final String COL_REMARKS = "remarks";

    public static final String COL_DEL_FLAG = "del_flag";

    public static final String COL_NAME = "name";

    public static final String COL_RECORD_ID = "record_id";

    public static final String COL_PATH = "path";
  //1文档模板 2法律法规3案件资料
  	private Integer docType;
 // 虚拟
 	private String fullPath;	// 完整文件路径
 	private String fileType;	// 文件类型
 	public String getFullPath() {
 		if(StringUtils.isNotBlank(this.path)){
 			this.fullPath = (JeePlusProperites.domainNameValue() + this.path);
 		}
 		return fullPath;
 	}

 	public String getFileType() {
 		this.fileType = FileKit.getFileSuffix(this.path);
 		return fileType;
 	}
}