/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 *  客户信息Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class Customer extends DataEntity<Customer> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="客户编号", align=2, sort=7)
	private String number;		// 客户编号
	@NotBlank(message = "客户标识不能为空")
	@ExcelField(title="客户标识", dictType="customer_type", align=2, sort=8)
	private String type;		// 客户标识(个人、单位)
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ExcelField(title="合同起始时间", align=2, sort=9)
	private Date contractStartDate;		// 合同起始时间
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ExcelField(title="合同结束时间", align=2, sort=10)
	private Date contractEndDate;		// 合同结束时间
	@ExcelField(title="所属行业", align=2, sort=11)
	private Industry industry;		// 所属行业
	@ExcelField(title="客户来源", dictType="customer_source", align=2, sort=12)
	private String source;		// 客户来源
	@NotBlank(message = "合作状态不能为空")
	@ExcelField(title="合作状态", dictType="cooperate_status", align=2, sort=13)
	private String status;		// 合作状态
	@ExcelField(title="客户重要性", dictType="customer_importance", align=2, sort=14)
	private String importance;		// 客户重要性
	@NotBlank(message = "名称不能为空")
	@ExcelField(title="姓名/单位名称", align=2, sort=15)
	private String name;		// 姓名/单位名称
	@ExcelField(title="民族", align=2, sort=16)
	private String nation;		// 民族
	@ExcelField(title="性别", dictType = "sex", align=2, sort=17)
	private String sex;			// 性别
	@ExcelField(title="联系电话", align=2, sort=18)
	private String phone;		// 联系电话
	@ExcelField(title="证件号码", align=2, sort=20)
	private String idNumber;		// 证件号码
	@ExcelField(title="住所地/单位地址", align=2, sort=21)
	private String address;		// 住所地/单位地址
	@ExcelField(title="法定代表人", align=2, sort=22)
	private String legalRepresentative;		// 法定代表人
	@ExcelField(title="统一社会信用代码", align=2, sort=25)
	private String unifiedSocialCreditCode;		// 统一社会信用代码


	// 虚拟
	private String followUpNames;	// 跟进人姓名

	public Customer() {
		super();
	}
	
	public Customer(String id){
		super(id);
	}
}