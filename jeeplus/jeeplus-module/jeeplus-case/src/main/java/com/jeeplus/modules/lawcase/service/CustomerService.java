/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.Customer;
import com.jeeplus.modules.lawcase.entity.CustomerContacts;
import com.jeeplus.modules.lawcase.entity.CustomerFollowUp;
import com.jeeplus.modules.lawcase.mapper.CustomerMapper;
import com.jeeplus.modules.lawcase.vo.CustomerVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 *  客户信息Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CustomerService extends CrudService<CustomerMapper, Customer> {

	@Autowired
	private CustomerContactsService customerContactsService;

	public Customer get(String id) {
		return super.get(id);
	}
	
	public List<Customer> findList(Customer customer) {
		return super.findList(customer);
	}
	
	public Page<Customer> findPage(Page<Customer> page, Customer customer) {
		return super.findPage(page, customer);
	}

	/**
	 * 获取完整信息 客户列表
	 * @param page
	 * @param customer
	 * @return
	 */
	public Page<Customer> findOverallPage(Page<Customer> page, Customer customer) {
		dataRuleFilter(customer);
		customer.setPage(page);
		page.setList(mapper.findOverallList(customer));
		return page;
	}

	/**
	 * 查询 获取客户跟进人信息
	 * @param customerFollowUp
	 * @return
	 */
	public List<CustomerFollowUp> findFollowUpList(CustomerFollowUp customerFollowUp){
		List<CustomerFollowUp> followUpList = mapper.findFollowUpList(customerFollowUp);
		if(followUpList == null){
			followUpList = new ArrayList<CustomerFollowUp>();
		}
		return followUpList;
	}

	@Transactional(readOnly = false)
	public void save(Customer customer) {
		super.save(customer);
	}

	/**
	 * 保存客户信息、跟进人信息
	 * @param customer
	 * @param list
	 */
	@Transactional(readOnly = false)
	public Customer save(Customer customer, List<CustomerFollowUp> list) {
		// 是否首次
		boolean isFirst = StringUtils.isBlank(customer.getId());
		super.save(customer);

		// 保存跟进人信息， 先删除在添加
		if (!isFirst){
			mapper.deleteFollowUpByCustomer(customer.getId());
		}
		if(list.size() > 0){
			for (CustomerFollowUp customerFollowUp : list) {
				customerFollowUp.setCustomer(customer);
				if(StringUtils.isBlank(customerFollowUp.getId())){
					customerFollowUp.preInsert();
				}else {
					customerFollowUp.preUpdate();
					customerFollowUp.setCreateBy(customerFollowUp.getUpdateBy());
					customerFollowUp.setCreateDate(customerFollowUp.getUpdateDate());
				}
			}
			mapper.insertFollowUp(list);
		}
		return customer;
	}

	/**
	 * 保存客户信息、跟进人信息、客户联系人信息
	 * @param customerVO
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveInfo(CustomerVO customerVO) {
		// 保存客户信息、跟进人信息
		Customer customer = this.save(customerVO.getCustomer(), customerVO.getCustomerFollowUpList());
		// 保存联系人信息
		List<CustomerContacts> contactsList = Lists.newArrayList();
		for (CustomerContacts contacts : customerVO.getCustomerContactsList()) {
			if(StringUtils.isNotBlank(contacts.getName())){
				contacts.setCustomer(customer);
				contacts.preInsert();

				contactsList.add(contacts);
			}
		}
		if(contactsList.size() > 0){
			customerContactsService.saveBatch(customerVO.getCustomerContactsList());
		}
	}

	@Transactional(readOnly = false)
	public void delete(Customer customer) {
		super.delete(customer);
		// 删除跟进人信息
		mapper.deleteFollowUpByCustomer(customer.getId());
		// 删除客户联系人
		customerContactsService.deleteByCustomer(customer.getId());
	}
	
}