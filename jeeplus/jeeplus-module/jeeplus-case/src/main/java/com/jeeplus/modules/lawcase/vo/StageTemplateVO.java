/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;


import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 阶段模版 VO
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class StageTemplateVO implements Serializable {

	private static final long serialVersionUID = 1L;
	private String id;		// id值
	private String type;		// 模版类型   系统、自定义
	private String name;	// 名称
	private String isSystem;	// 是否系统模版
	private String caseModelType;	
	private String caseModelName;	
	private Integer sort;
	private Integer isFee;
	private List<StageVO> stageList = Lists.newArrayList();	// 阶段信息列表

	public StageTemplateVO() {
		super();
	}

}