/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;

/**
 * 案件办案策略Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseHandleStrategy extends DataEntity<CaseHandleStrategy> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="案件信息", align=2, sort=7)
	private Case lawCase;		// 案件信息
	@ExcelField(title="问题描述", align=2, sort=8)
	private String question;		// 问题描述
	@ExcelField(title="解决方案", align=2, sort=9)
	private String solution;		// 解决方案
	@ExcelField(title="处理结果", align=2, sort=10)
	private String result;		// 处理结果
	
	public CaseHandleStrategy() {
		super();
	}
	
	public CaseHandleStrategy(String id){
		super(id);
	}
}