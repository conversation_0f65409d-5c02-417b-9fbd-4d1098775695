/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.service.TreeService;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.CaseCause;
import com.jeeplus.modules.lawcase.mapper.CaseCauseMapper;
import com.jeeplus.database.datasource.annotation.DS;

/**
 * 案由Service
 * <AUTHOR>
 * @version 2021-09-04
 */
@DS("law_case")
@Service
@Transactional(readOnly = true)
public class CaseCauseService extends TreeService<CaseCauseMapper, CaseCause> {

	public CaseCause get(String id) {
		return super.get(id);
	}

	public List<CaseCause> findList(CaseCause caseCause) {
		if (StringUtils.isNotBlank(caseCause.getParentIds())){
			caseCause.setParentIds(","+caseCause.getParentIds()+",");
		}
		return super.findList(caseCause);
	}

	@Transactional(readOnly = false)
	public void save(CaseCause caseCause) {
		super.save(caseCause);
	}

	@Transactional(readOnly = false)
	public void delete(CaseCause caseCause) {
		super.delete(caseCause);
	}

}