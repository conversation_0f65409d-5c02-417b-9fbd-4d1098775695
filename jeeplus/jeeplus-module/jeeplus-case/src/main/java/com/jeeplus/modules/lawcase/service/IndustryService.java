/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.Industry;
import com.jeeplus.modules.lawcase.mapper.IndustryMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.service.TreeService;
import com.jeeplus.common.utils.StringUtils;

/**
 * 行业信息Service
 * <AUTHOR>
 * @version 2021-08-01
 */
@Service
@Transactional(readOnly = true)
public class IndustryService extends TreeService<IndustryMapper, Industry> {

	public Industry get(String id) {
		return super.get(id);
	}

	public List<Industry> findList(Industry industry) {
		if (StringUtils.isNotBlank(industry.getParentIds())){
			industry.setParentIds(","+industry.getParentIds()+",");
		}
		return super.findList(industry);
	}

	@Transactional(readOnly = false)
	public void save(Industry industry) {
		super.save(industry);
	}

	@Transactional(readOnly = false)
	public void delete(Industry industry) {
		super.delete(industry);
	}

}