/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;


import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.lawcase.entity.Customer;
import com.jeeplus.modules.lawcase.entity.CustomerContacts;
import com.jeeplus.modules.lawcase.entity.CustomerFollowUp;
import com.jeeplus.modules.lawcase.entity.TodoInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  客户信息 Vo
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CustomerVO implements Serializable {

	private static final long serialVersionUID = 1L;

	private Customer customer;		// 客户信息
	private List<CustomerFollowUp> customerFollowUpList; 	// 客户跟进人
	private List<CustomerContacts> customerContactsList;	// 客户联系人

	private List<TodoInfo> todoInfoList;	// 客户待办事项列表

	public CustomerVO() {
		super();
	}

	public CustomerVO(Customer customer){
		this.customer = customer;
	}
}