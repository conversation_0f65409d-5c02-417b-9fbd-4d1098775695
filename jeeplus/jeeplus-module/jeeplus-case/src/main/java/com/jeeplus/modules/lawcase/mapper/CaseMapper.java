/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseCaseRelation;
import com.jeeplus.modules.lawcase.entity.CaseCustomerRelation;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 案件信息MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CaseMapper extends BaseMapper<Case> {
    /**
     * 获取最大案号 值
     * @return
     */
    String getMaxNumber();
    /**
     * 获取完整信息 案件列表
     * @param lawCase
     * @return
     */
    List<Case> findOverallList(Case lawCase);

    /**
     * 案件结案 更新状态信息
     * @param lawCase
     * @return
     */
    int updateSettle(Case lawCase);

    /**
     * 更新受理单位
     * @param lawCase
     * @return
     */
    int updateAccept(Case lawCase);

    /**
     * 更新受理单位、立案日期、裁决日期、审理结果
     * @param lawCase
     * @return
     */
    int updateAcceptAndResult(Case lawCase);

    /**
     * 案件归档 更新状态信息
     * @param lawCase
     * @return
     */
    int updateArchive(Case lawCase);

    /**
     * 更新案件状态
     * @param lawCase
     * @return
     */
    int updateStatus(Case lawCase);

    /**
     * 更新审核状态、案件是否需审核
     * @param lawCase
     * @return
     */
    int updateAuditStatus(Case lawCase);

    /**
     * 更新共享状态
     * @param lawCase
     * @return
     */
    int updateIsShare(Case lawCase);

    /**
     * 根据案件信息 删除所有关联信息
     * 案件当事人、案件阶段、待办事项、办案策略、承办人员。。。
     * @param caseId
     * @return
     */
    int deleteAllRelation(String caseId);

}