/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 案件当事人Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseConcernPerson extends DataEntity<CaseConcernPerson> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="案件信息", align=2, sort=7)
	private Case lawCase;		// 案件信息
	@ExcelField(title="类型（个人、单位）", dictType="customer_type", align=2, sort=8)
	private String type;		// 类型（个人、单位）
	@ExcelField(title="委托方（是/否）", align=2, sort=9)
	private String isEntrust;		// 委托方（是/否）
	@ExcelField(title="姓名/单位名称", align=2, sort=10)
	private String name;		// 姓名/单位名称
	@ExcelField(title="属性", align=2, sort=11)
	private String attribute;		// 属性
	@ExcelField(title="民族", align=2, sort=12)
	private String nation;		// 民族
	@ExcelField(title="性别", align=2, sort=13)
	private String sex;		// 性别
	@ExcelField(title="联系方式", align=2, sort=14)
	private String phone;		// 联系方式
	@Length(max = 18)
	@ExcelField(title="证件号码", align=2, sort=15)
	private String idNumber;		// 证件号码
	@ExcelField(title="住所地/单位地址", align=2, sort=16)
	private String address;		// 住所地/单位地址
	@ExcelField(title="法定代表人", align=2, sort=17)
	private String legalRepresentative;		// 法定代表人
	@ExcelField(title="统一社会信用代码", align=2, sort=18)
	private String unifiedSocialCreditCode;		// 统一社会信用代码
	
	public CaseConcernPerson() {
		super();
	}

	public CaseConcernPerson(Case lawCase) {
		this.lawCase = lawCase;
	}

	public CaseConcernPerson(String id){
		super(id);
	}
}