/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;

import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.lawcase.entity.*;
import lombok.Data;

import java.util.List;

/**
 * 案件信息 VO
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseVO extends DataEntity<CaseVO> {

	private static final long serialVersionUID = 1L;

	private Case lawCase;	// 案件信息

	private List<CaseConcernPerson> concernPersonList;	// 案件当事人信息

	private List<CaseUndertakePerson> undertakePersonList;	// 案件承办人员信息

	private List<CaseCaseRelation> caseRelationsList;	// 案件与案件关联 信息

	private List<CaseCustomerRelation> customerRelationList;	// 案件与客户关联 信息

	private List<TodoInfo> todoInfoList;		// 待办事项 信息列表

	private String watermarkStr;
	public CaseVO() {
		super();
	}

	public CaseVO(Case lawCase) {
		this.lawCase = lawCase;
	}

	public CaseVO(String id){
		super(id);
	}
}