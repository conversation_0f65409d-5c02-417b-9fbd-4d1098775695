/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.TreeEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 案件程序Entity
 * <AUTHOR>
 * @version 2021-09-04
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class CaseProgram extends TreeEntity<CaseProgram> {
	
	private static final long serialVersionUID = 1L;
	@NotBlank(message = "案件类型不能为空")
	@ExcelField(title="案件类型", dictType="case_type", align=2, sort=7)
	private String type;		// 案件类型
	
	
	public CaseProgram() {
		super();
	}

	public CaseProgram(String id){
		super(id);
	}

	public  CaseProgram getParent() {
			return parent;
	}
	
	@Override
	public void setParent(CaseProgram parent) {
		this.parent = parent;
		
	}
	
	public String getParentId() {
		return parent != null && parent.getId() != null ? parent.getId() : "0";
	}
}