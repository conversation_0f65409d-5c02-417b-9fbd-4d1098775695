package com.jeeplus.modules.lawcase.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeeplus.modules.lawcase.util.DeepseekUtil;
import com.jeeplus.modules.lawcase.vo.DeepseekVO;

import io.swagger.annotations.Api;

@Api(tags = "deepsekk接口")
@Controller
public class DeepSeekController {
	
 
	/**
	 * 日志对象
	 */
	protected Logger logger = LoggerFactory.getLogger(getClass());
    
   @ResponseBody
   @RequestMapping(value = "deepseek/getContent")
    public DeepseekVO getContent(  
    		  String content,boolean stream,Integer type) throws Exception{
	   
	   logger.info ("【deepsekk】====content:"+content);
	  
        return DeepseekUtil.getContent(content,stream,type);
	   
   }
 
 
}
