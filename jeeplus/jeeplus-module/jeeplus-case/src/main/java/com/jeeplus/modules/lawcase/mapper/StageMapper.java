/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.vo.StageVO;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 阶段信息MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface StageMapper extends BaseMapper<Stage> {
    /**
     * 根据模版id 查询阶段信息
     * @return
     */
    List<StageVO> findListByTemplate(Stage stage);

    /**
     * 根据模版id 删除阶段信息
     * @param templateId
     * @return
     */
    int deleteByTemplate(String templateId);
}