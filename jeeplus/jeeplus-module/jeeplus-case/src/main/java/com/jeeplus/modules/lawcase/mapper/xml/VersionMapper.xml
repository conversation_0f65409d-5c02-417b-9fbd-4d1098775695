<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.VersionMapper">
    
	<sql id="versionColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.number AS "number",
		a.path AS "path",
		a.title AS "title",
		a.publish_date AS "publishDate",
		a.content AS "content",
		a.is_enable AS "isEnable"
	</sql>
	
	<sql id="versionJoins">
		
	</sql>
	
	<select id="get" resultType="Version" >
		SELECT 
			<include refid="versionColumns"/>
		FROM law_version a
		<include refid="versionJoins"/>
		WHERE a.id = #{id}
	</select>
	
	<select id="findList" resultType="Version" >
		SELECT 
			<include refid="versionColumns"/>
		FROM law_version a
		<include refid="versionJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="number != null and number != ''">
				AND a.number LIKE concat('%',#{number},'%') 
			</if>
			<if test="title != null and title != ''">
				AND a.title LIKE concat('%',#{title},'%') 
			</if>
			<if test="isEnable != null and isEnable != ''">
				AND a.is_enable = #{isEnable}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.is_enable DESC, a.publish_date DESC
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="Version" >
		SELECT 
			<include refid="versionColumns"/>
		FROM law_version a
		<include refid="versionJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO law_version(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			`number`,
			path,
			title,
			publish_date,
			content,
			is_enable
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{number},
			#{path},
			#{title},
			#{publishDate},
			#{content},
			#{isEnable}
		)
	</insert>
	
	<update id="update">
		UPDATE law_version SET 	
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			`number` = #{number},
			path = #{path},
			title = #{title},
			publish_date = #{publishDate},
			content = #{content},
			is_enable = #{isEnable}
		WHERE id = #{id}
	</update>

	<update id="updateEnable">
		UPDATE law_version SET is_enable = #{isEnable} WHERE FIND_IN_SET(id, #{ids})
	</update>
	
	
	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_version
		WHERE id = #{id}
	</update>
	
	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_version SET 
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>
	
	
	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="Version" statementType="STATEMENT">
		select * FROM law_version  where ${propertyName} = '${value}'
	</select>
	
</mapper>