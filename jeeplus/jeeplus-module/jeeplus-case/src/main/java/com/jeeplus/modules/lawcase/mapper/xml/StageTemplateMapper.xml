<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.StageTemplateMapper">

	<sql id="stageTemplateColumns">
		a.id AS "id",
		a.sort AS "sort",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.is_fee AS "isFee",
		a.name AS "name",
		a.type AS "type",
		a.case_mtype_id AS "caseModelType",
		a.case_mtype_name AS "caseModelName",
		a.is_system AS "isSystem"
	</sql>

	<sql id="stageTemplateJoins">

	</sql>


	<select id="get" resultType="StageTemplate" >
		SELECT
			<include refid="stageTemplateColumns"/>
		FROM law_stage_template a
		<include refid="stageTemplateJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="StageTemplate" >
		SELECT
			<include refid="stageTemplateColumns"/>
			,(SELECT GROUP_CONCAT(`name` ORDER BY sort) FROM law_stage WHERE stage_template_id = a.id GROUP BY stage_template_id) AS "stageNames"
		FROM law_stage_template a
		<include refid="stageTemplateJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%') 
			</if>
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="caseModelType != null and caseModelType != ''">
				AND a.case_mtype_id = #{caseModelType}
			</if>
			<if test="isSystem != null and isSystem != ''">
				AND a.is_system = #{isSystem}
			</if>
			<if test="createBy != null and createBy.id != null and createBy.id != ''">
				AND a.create_by = #{createBy.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.sort asc,a.is_system DESC, a.update_date DESC
			</otherwise>
		</choose>
	</select>
	
	
	<select id="findList2" resultType="StageTemplate" >
		SELECT
		    *
		FROM law_stage_template a
		<where>
			a.del_flag =2
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="isSystem != null and isSystem != ''">
				AND a.is_system = #{isSystem}
			</if>
		</where>
	</select>

	<select id="findAllList" resultType="StageTemplate" >
		SELECT
			<include refid="stageTemplateColumns"/>
		FROM law_stage_template a
		<include refid="stageTemplateJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.sort asc, a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_stage_template(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			`name`,
			`type`,
			case_mtype_id,
			sort,
			case_mtype_name,
			is_system
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{name},
			#{type},
			#{caseModelType}, 
			#{sort}, 
			#{caseModelName}, 
			#{isSystem}
		)
	</insert>

	<update id="update">
		UPDATE law_stage_template SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			del_flag = #{delFlag},
			remarks = #{remarks},
			`name` = #{name},
			`type` = #{type},
			`sort` = #{sort},
			case_mtype_id = #{caseModelType},
			case_mtype_name = #{caseModelName},
			is_system = #{isSystem}
		WHERE id = #{id}
	</update>
	
	<update id="updateDelFlag">
		UPDATE law_stage_template SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			del_flag = #{delFlag} 
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_stage_template
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_stage_template SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="StageTemplate">
		select * FROM law_stage_template  where ${propertyName} = #{value}
	</select>
	
		
	<select id="getMaxSort" resultType="Integer">
		select max(sort) FROM law_stage_template where  del_flag=0
	</select>
	
		<update id="updateSort">
		UPDATE law_stage_template SET
			sort = #{sort}
		WHERE id = #{id}
	</update>
	
		<update id="updateIsFee">
		UPDATE law_stage_template SET
			is_fee = #{isFee}
		WHERE id = #{id}
	</update>
	
	
	

</mapper>