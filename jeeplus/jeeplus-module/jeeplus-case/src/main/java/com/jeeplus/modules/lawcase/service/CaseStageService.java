/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.Date;
import java.util.List;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.IdGen;
import com.jeeplus.common.utils.SpringContextHolder;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.*;
import com.jeeplus.modules.lawcase.mapper.CaseStageMapper;
import com.jeeplus.modules.lawcase.mapper.StageRecordFileMapper;
import com.jeeplus.modules.lawcase.util.TreeUtils;
import com.jeeplus.modules.lawcase.vo.StageVO;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

import javax.annotation.Resource;
import javax.xml.crypto.Data;

/**
 * 案件阶段Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseStageService extends CrudService<CaseStageMapper, CaseStage> {

	@Autowired
	private CaseService caseService;
	@Autowired
	private TodoInfoService todoInfoService;

	@Resource
	private StageRecordFileMapper stageRecordFileMapper;

	public CaseStage get(String id) {
		return super.get(id);
	}

	/**
	 * 根据 案件id 查询阶段排序最大值
	 * @param lawCaseId
	 * @return
	 */
	public int getMaxSortByCase(String lawCaseId) {
		return mapper.getMaxSortByCase(lawCaseId);
	}

	public List<CaseStage> findList(CaseStage caseStage) {
		return super.findList(caseStage);
	}
	
	public Page<CaseStage> findPage(Page<CaseStage> page, CaseStage caseStage) {
		return super.findPage(page, caseStage);
	}
	
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void save(CaseStage caseStage) {
		if(caseStage.getSort() == null){
			int maxSort = getMaxSortByCase(caseStage.getLawCase().getId());
			caseStage.setSort( maxSort + 1 );
		}
		super.save(caseStage);
	}

	/**
	 * 通过模版批量保存阶段信息
	 * 1、判断模版是否是 系统定义 若是系统定义 则该案件需审核  若不是则不需要审核
	 * 2、处理 stageVoList 数据转为 CaseStageList   TodoInfoList
	 * 3、进行批量保存
	 * @param lawCase
	 * @param list
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveBatchByTemplate(Case lawCase, StageTemplate stageTemplate, List<StageVO> list) {
		// 根据模版类型 判断案件是否需审核  系统定义模版 需审核，非系统定义 需判断案件审核类型 将其改为不需审核
		if(StageTemplate.TYPE_SYSTEM.equals(stageTemplate.getType())){
			lawCase.setIsAudit(JeePlusProperites.YES);
			lawCase.setAuditStatus(CaseConstant.CASE_AUDIT_STATUS_WAIT_SUBMIT);
			lawCase.setAuditReason("");
			caseService.updateAuditStatus(lawCase);
		}else {
			if(JeePlusProperites.YES.equals(lawCase.getIsAudit())){
				lawCase.setIsAudit(JeePlusProperites.NO);
				lawCase.setAuditStatus(CaseConstant.CASE_AUDIT_STATUS_PASS);
				lawCase.setAuditReason("");
				caseService.updateAuditStatus(lawCase);
			}
		}

		// 保存模版数据 清空之前的待办事项、阶段信息
		todoInfoService.deleteByRelevance(CaseConstant.TODO_RELEVANCE_TYPE_CASE, lawCase.getId(), null);
		this.deleteByCase(lawCase.getId());

		// 保存 案件阶段、待办事项列表(先处理 数据结构 最后统一保存)
		User currUser = UserUtils.getUser();	// 当前用户
		List<CaseStage> caseStageList = Lists.newArrayList();
		List<TodoInfo> todoInfoList = Lists.newArrayList();
		List<TodoInfoFile> todoInfoFiles = Lists.newArrayList();
		JeePlusProperites jeePlusProperites = SpringContextHolder.getBean(JeePlusProperites.class);
		for (StageVO stageVO : list) {
			CaseStage caseStage = new CaseStage();
			caseStage.setName(stageVO.getName());
			caseStage.preInsert();
			caseStageList.add(caseStage);

			List<TodoInfo> todoList = TreeUtils.treeToList(new TodoInfo(), stageVO.getStageRecordList()
					, recordVo -> {
						// 创建 案件阶段待办事项
						TodoInfo todoInfo = CaseConstant.getCaseTodo(recordVo, lawCase.getId(), caseStage);
						todoInfo.setHostUser(currUser);
						todoInfo.setId(IdGen.uuid());

						// 查询当前 record 是否有关联的文件
						List<StageRecordFile> fileList = stageRecordFileMapper.selectListByRecordId(recordVo.getId());
						if (!CollectionUtils.isEmpty(fileList)){
							for (StageRecordFile file : fileList){
								if (file == null){
									continue;
								}
								String srcUrl=file.getPath();
								TodoInfoFile todoInfoFile = new TodoInfoFile();
								todoInfoFile.setName(file.getName());
								todoInfoFile.setPath(file.getPath());
								todoInfoFile.setDelFlag("0");
								todoInfoFile.setSort(file.getSort());
								todoInfoFile.setFilesizes(100);
								todoInfoFile.setDocType(file.getDocType());
								todoInfoFile.setFileversion(1);
								todoInfoFile.setPath(file.getPath().replaceAll(file.getName(), "")+lawCase.getId()+"/"+file.getName());
								todoInfoFile.setDelFlag("0");
								com.jeeplus.common.utils.FileUtils.copyFile(jeePlusProperites.getUserfilesBaseDir()+srcUrl, jeePlusProperites.getUserfilesBaseDir()+todoInfoFile.getPath());					
								todoInfoFile.setTodoInfo(todoInfo);
								todoInfoFile.preInsert();
								todoInfoFiles.add(todoInfoFile);
							}
						}
						return todoInfo;
					});
			todoInfoList.addAll(todoList);
		}
		// 批量插入 案件阶段
		this.saveBatch(lawCase, caseStageList);
		// 批量插入 案件阶段-待办事项
		todoInfoService.saveBatch(todoInfoList);
		// 批量插入 附件
		if (!CollectionUtils.isEmpty(todoInfoFiles)) {
			todoInfoService.saveBatchFiles(todoInfoFiles);
		}
	}

	/**
	 * 批量插入 案件阶段信息
	 * @param lawCase
	 * @param list
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveBatch(Case lawCase, List<CaseStage> list) {
		int maxSort = getMaxSortByCase(lawCase.getId());
		for (CaseStage caseStage : list) {
			caseStage.setLawCase(lawCase);
			caseStage.setIsCurrent(JeePlusProperites.NO);
			caseStage.setSort( ++maxSort );
			if(StringUtils.isBlank(caseStage.getId())){
				caseStage.preInsert();
			}else {
				caseStage.preUpdate();
				caseStage.setCreateBy(caseStage.getUpdateBy());
				caseStage.setCreateDate(caseStage.getUpdateDate());
			}
		}
		super.saveBatch(list);
	}

	/**
	 * 更新当前状态
	 * 1、将之前当前状态数据 设为 否
	 * 2、将该阶段 当前状态  设为 是
	 * @param caseStage
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateCurrent(CaseStage caseStage) {
		String lawCaseId = caseStage.getLawCase().getId();
		// 将之前当前状态数据 设为 否
		mapper.updateCurrent(JeePlusProperites.NO, lawCaseId, "", JeePlusProperites.YES);
		// 将该阶段 当前状态  设为 是
		mapper.updateCurrent(JeePlusProperites.YES, lawCaseId, caseStage.getId(), JeePlusProperites.NO);
	}

	/**
	 * 删除阶段信息 并更新排序  同时删除待办事项
	 * @param caseStage
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void delete(CaseStage caseStage) {
		String lawCaseId =caseStage.getLawCase().getId();
		if(StringUtils.isNotBlank(lawCaseId)){
			Integer maxSort = mapper.getMaxSortByCase(lawCaseId);
			// 删除当前阶段
			super.delete(caseStage);
			// 当前排序值 判断当前排序值是否大于 最大排序值   若小于则将大于当前值的记录排序值sort减1
			Integer curSort = caseStage.getSort();
			if(curSort != null && maxSort > curSort){
				mapper.updateSortByRange(-1, lawCaseId, curSort, null);
			}

			// 删除待办事项
			todoInfoService.deleteByRelevance(CaseConstant.TODO_RELEVANCE_TYPE_CASE, lawCaseId, caseStage);
		}
	}

	/**
	 * 更新排序值
	 * 1、案件id、新排序值 <= 0  则忽略
	 * 2、查询最大排序值  若新值 > 最大值   则设置新值为最大值
	 * 3、根据阶段当前排序值做对比
	 * 		当前小于新值  则将区间的排序值 -1
	 * 		当前大于新值  则将区间的排序值 +1
	 * 4、更新当前记录排序值为新值
	 * @param caseStage
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateSort(CaseStage caseStage, Integer newSort) {
		String lawCaseId =caseStage.getLawCase().getId();
		if(StringUtils.isNotBlank(lawCaseId) && newSort > 0){
			Integer maxSort = mapper.getMaxSortByCase(lawCaseId);
			if(newSort > maxSort){
				newSort = maxSort;
			}
			// 当前排序值
			Integer currSort = caseStage.getSort();
			if(currSort < newSort){
				// 区间范围的排序值 -1
				mapper.updateSortByRange(-1, lawCaseId, currSort, newSort);
			}else if(currSort > newSort){
				// 区间范围的排序值 +1
				mapper.updateSortByRange(1, lawCaseId, newSort, currSort);
			}
			// 更新当前记录 排序值
			mapper.updateSortById(newSort, caseStage.getId());
		}
	}

	/**
	 * 根据案件id  删除案件阶段信息
	 * @param lawCaseId
	 */
	@Transactional(readOnly = false)
	public void deleteByCase(String lawCaseId) {
		mapper.deleteByCase(lawCaseId);
	}
}