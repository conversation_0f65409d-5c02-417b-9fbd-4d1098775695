/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.dto.CaseConcernPersonDto;
import com.jeeplus.modules.lawcase.entity.CaseConcernPerson;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jeeplus.modules.lawcase.entity.CaseConcernPerson;

import java.util.List;

/**
 * 案件当事人MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CaseConcernPersonMapper extends BaseMapper<CaseConcernPerson> {

    List<CaseConcernPersonDto> findOverallList(CaseConcernPersonDto caseConcernPersonDto);
    /**
     * 通过 案件信息 删除当事人信息
     * @param lawCaseId
     * @return
     */
    int deleteByCase(String lawCaseId);
}