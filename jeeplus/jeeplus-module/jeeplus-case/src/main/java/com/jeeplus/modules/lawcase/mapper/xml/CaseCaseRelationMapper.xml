<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseCaseRelationMapper">


	<sql id="caseCaseRelationColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.remarks AS "remarks",
		a.case_id AS "lawCase.id",
		a.relation_case_id AS "relationCase.id",

		lc.name AS "relationCase.name",
		lc.case_program_id AS "relationCase.caseProgram.id",
		lc.case_program_name AS "relationCase.caseProgram.name",
		lc.trial_result AS "relationCase.trialResult",
		lc.host_user_id AS "relationCase.hostUser.id",
		hostUser.name AS "relationCase.hostUser.name"
	</sql>

	<sql id="caseCaseRelationJoins">
		LEFT JOIN law_case lc ON a.relation_case_id = lc.id
		LEFT JOIN sys_user hostUser ON hostUser.id = lc.host_user_id
	</sql>


	<select id="get" resultType="CaseCaseRelation" >
		SELECT
			<include refid="caseCaseRelationColumns"/>
		FROM law_case_case_relation a
		<include refid="caseCaseRelationJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseCaseRelation" >
		SELECT
			<include refid="caseCaseRelationColumns"/>
		FROM law_case_case_relation a
		<include refid="caseCaseRelationJoins"/>
		WHERE a.case_id = #{lawCase.id}
		<if test="relationCase != null and relationCase.id != null and relationCase.id != ''">
			AND a.relation_case_id = #{relationCase.id}
		</if>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findRelationCaseList" resultType="Case" >
		SELECT a.*
		FROM(
			SELECT a.id, a.name
			FROM law_case a
			WHERE a.del_flag = '0' AND a.id != #{caseId}
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%')
			</if>
			<if test="hostUserId != null and hostUserId != ''">
				AND a.host_user_id = #{hostUserId}
			</if>
		) a
		LEFT JOIN law_case_case_relation cr ON a.id = cr.relation_case_id AND cr.case_id = #{caseId}
		WHERE cr.case_id IS NULL OR cr.case_id = ''
		ORDER BY convert(a.name USING gbk)
	</select>

	<select id="findAllList" resultType="CaseCaseRelation" >
		SELECT
			<include refid="caseCaseRelationColumns"/>
		FROM law_case_case_relation a
		<include refid="caseCaseRelationJoins"/>
	  
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_case_relation(
			id,
			create_by,
			create_date,
			remarks,
			case_id,
			relation_case_id
		)VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{remarks},
			#{lawCase.id},
			#{relationCase.id}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_case_case_relation(
			id,
			create_by,
			create_date,
			remarks,
			case_id,
			relation_case_id
		)VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.remarks},
			#{item.lawCase.id},
			#{item.relationCase.id}
			)
		</foreach>
	</insert>

	<update id="update">
		UPDATE law_case_case_relation SET
			remarks = #{remarks},
			case_id = #{lawCase.id},
			relation_case_id = #{relationCase.id}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_case_relation WHERE id = #{id}
	</update>

	<update id="deleteByCase">
		DELETE FROM law_case_case_relation WHERE case_id = #{lawCaseId}
	</update>


	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_case_relation SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseCaseRelation">
		select * FROM law_case_case_relation  where ${propertyName} = #{value}
	</select>

</mapper>