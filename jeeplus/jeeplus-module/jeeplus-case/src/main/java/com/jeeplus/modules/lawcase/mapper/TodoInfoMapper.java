/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.vo.TodoInfoRelevanceVO;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.TreeMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jeeplus.modules.lawcase.entity.TodoInfo;

import java.util.List;
import java.util.Map;

/**
 * 待办事项MAPPER接口
 * <AUTHOR>
 * @version 2021-08-12
 */
@Mapper
@Repository
public interface TodoInfoMapper extends TreeMapper<TodoInfo> {
    /**
     * 根据父级id、状态 查询数量
     * @param todoInfo
     * @return
     */
    Integer getCountByParentId(TodoInfo todoInfo);

    /**
     * 根据 关联类型、关联id  分组查询 对应的待办事项
     * @param todoInfo
     * @return
     */
    List<TodoInfoRelevanceVO> findGroupList(TodoInfo todoInfo);

    /**
     * 根据关联类型、关联id  查询待办事项、所属的流程阶段
     * @param todoInfo
     * @return
     */
    List<TodoInfo> findTodoStageList(TodoInfo todoInfo);

    /**
     * 查询 主办人 待办事项 的数量统计
     * @param todoInfo
     * @return
     */
    List<Map<String, Object>> findHostUserStatisticList(TodoInfo todoInfo);

    /**
     * 根据 查询起始、查询结束日期 获取月信息数据
     * @param todoInfo
     * @return
     */
    List<TodoInfo> findListByMonth(TodoInfo todoInfo);

    /**
     * 更新当前级别的待办事项状态
     * @param todoInfo
     * @return
     */
    int updateCurrentStatus(TodoInfo todoInfo);

    /**
     * 更新当前级别、当前级的所有子级  的待办事项状态
     * @param todoInfo
     * @return
     */
    int updateCurrentAndChildrenStatus(TodoInfo todoInfo);

    /**
     * 根据关联关系删除 待办事项
     * @param todoInfo
     * @return
     */
    int deleteByRelevance(TodoInfo todoInfo);
}