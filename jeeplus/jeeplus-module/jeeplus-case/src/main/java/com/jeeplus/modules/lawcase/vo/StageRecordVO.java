/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.TreeEntity;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.entity.StageRecord;
import com.jeeplus.modules.lawcase.entity.StageTemplate;
import lombok.Data;

/**
 * 阶段记录 VO
 * <AUTHOR>
 * @version 2021-08-12
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class StageRecordVO extends BaseTreeVO<StageRecordVO> {

	private static final long serialVersionUID = 1L;
	private String stageTemplateId;		// 模版id
	private String stageId;		// 阶段id
	private String type;		// 类型
	private String content;		// 内容/详情
	private String isNotAuditProhibit; // 是否未审核期间禁止编辑 默认：否	2022-03-05 新增

	private Integer nums;
	
	public StageRecordVO() {
		super();
	}

	@Override
	public String getParentId() {
		String parentId = super.getParentId();
		return StringUtils.isBlank(parentId) ? "0" : parentId;
	}
}