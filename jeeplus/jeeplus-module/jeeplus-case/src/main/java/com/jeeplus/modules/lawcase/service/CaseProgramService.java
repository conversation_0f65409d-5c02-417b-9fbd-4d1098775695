/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.service.TreeService;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.CaseProgram;
import com.jeeplus.modules.lawcase.mapper.CaseProgramMapper;
import com.jeeplus.database.datasource.annotation.DS;

/**
 * 案件程序Service
 * <AUTHOR>
 * @version 2021-09-04
 */
@DS("law_case")
@Service
@Transactional(readOnly = true)
public class CaseProgramService extends TreeService<CaseProgramMapper, CaseProgram> {

	public CaseProgram get(String id) {
		return super.get(id);
	}

	public List<CaseProgram> findList(CaseProgram caseProgram) {
		if (StringUtils.isNotBlank(caseProgram.getParentIds())){
			caseProgram.setParentIds(","+caseProgram.getParentIds()+",");
		}
		return super.findList(caseProgram);
	}

	@Transactional(readOnly = false)
	public void save(CaseProgram caseProgram) {
		super.save(caseProgram);
	}

	@Transactional(readOnly = false)
	public void delete(CaseProgram caseProgram) {
		super.delete(caseProgram);
	}

}