package com.jeeplus.modules.lawcase.constant;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.lawcase.entity.*;
import com.jeeplus.modules.lawcase.vo.StageRecordVO;
import com.jeeplus.modules.lawcase.vo.StageTemplateVO;
import com.jeeplus.modules.lawcase.vo.StageVO;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.FileKit;
import com.jeeplus.modules.sys.utils.UserUtils;
import com.jeeplus.modules.wps.model.ConstantModel;
import org.apache.poi.xslf.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.function.BiFunction;

/**
 * 案件信息 常量类
 * <AUTHOR>
 * @date 2021-08-08
 */
public class CaseConstant {

    public static final String PASS = "1";    // 通过
    public static final String REJECT = "2";    // 拒绝

    public static final String CASE_AUDIT_STATUS_REJECT = "0";    // 审核拒绝
    public static final String CASE_AUDIT_STATUS_WAIT = "1";      // 待审核
    public static final String CASE_AUDIT_STATUS_PASS = "2";    // 审核通过
    public static final String CASE_AUDIT_STATUS_WAIT_SUBMIT = "3";    // 待提交

    public static final String CASE_STATUS_CANCEL = "0";    // 取消
    public static final String CASE_STATUS_PROCESS = "1";   // 在办
    public static final String CASE_STATUS_SETTLE = "2";    // 结案
    public static final String CASE_STATUS_ARCHIVE = "3";   // 归档

    public static final String CUSTOMER_TYPE_PERSONAL = "1";    // 客户类型 个人
    public static final String CUSTOMER_TYPE_COMPANY = "2";    // 客户类型 单位

    public static final String TODO_RELEVANCE_TYPE_NOT = "0";       // 待办事项 关联类型 不关联
    public static final String TODO_RELEVANCE_TYPE_CUSTOMER = "1"; // 待办事项 关联类型 客户
    public static final String TODO_RELEVANCE_TYPE_CASE = "2";     // 待办事项 关联类型 案件

    public static final String TODO_STATUS_WAIT = "1";    // 待办事项 未办
    public static final String TODO_STATUS_COMPLETE = "2";    // 待办事项 已办

    public static final String TODO_TYPE_CATALOG = "1";        // 待办事项 类型  目录
    public static final String TODO_TYPE_TASK = "2";    // 待办事项 类型  任务

    public static final String  FINANCE_FLOW_INCOME  = "1";  // 财务流水 类型  收入
    public static final String FINANCE_FLOW_EXPEND = "2";    // 财务流水 类型  支出

    /**
     * 创建 案件阶段的待办事项
     * @param recordVo
     * @param lawCaseId
     * @param caseStage
     * @return
     */
    public static TodoInfo getCaseTodo(StageRecordVO recordVo, String lawCaseId, CaseStage caseStage){
        TodoInfo todoInfo = new TodoInfo();
        todoInfo.setName(recordVo.getName());
        todoInfo.setContent(recordVo.getContent());
        todoInfo.setType(recordVo.getType());
        todoInfo.setSort(recordVo.getSort());
        todoInfo.setIsNotAuditProhibit(recordVo.getIsNotAuditProhibit());

        todoInfo.setStartDate(new Date());
        todoInfo.setStatus(TODO_STATUS_WAIT);
        todoInfo.setRelevanceType(TODO_RELEVANCE_TYPE_CASE);
        todoInfo.setRelevanceId(lawCaseId);
        todoInfo.setStage(caseStage);
        return todoInfo;
    }

    /**
     * Stage 模版阶段  VO类转实体类
     * @param vo
     * @return
     */
    public static Stage voToStage(StageVO vo){
        Stage stage = new Stage();
        stage.setId(vo.getId());
        stage.setName(vo.getName());
        stage.setSort(vo.getSort());
        return stage;
    }

    /**
     * StageTemplate  阶段模版 VO类转实体类
     * @param vo
     * @return
     */
    public static StageTemplate voToStageTemplate(StageTemplateVO vo){
        StageTemplate template = new StageTemplate();
        template.setId(vo.getId());
        template.setName(vo.getName());
        template.setType(vo.getType());
        template.setIsSystem(vo.getIsSystem());
        template.setSort(vo.getSort());
        template.setCaseModelType(vo.getCaseModelType());
        template.setCaseModelName(vo.getCaseModelName());
        return template;
    }

    /**
     * StageRecord  阶段事项内容 VO类转实体类
     * @param vo
     * @return
     */
    public static StageRecord voToStageRecord(StageRecordVO vo){
        StageRecord stageRecord = new StageRecord();
        stageRecord.setId(vo.getId());
        stageRecord.setType(vo.getType());
        stageRecord.setName(vo.getName());
        stageRecord.setContent(vo.getContent());
        stageRecord.setSort(vo.getSort());
        stageRecord.setNums(vo.getNums()==null?0:vo.getNums());
        // 若 isNotAuditProhibit 存在并且 等于1时赋值1，否则赋值为0
        String isNotAuditProhibit = (StringUtils.isNotBlank(vo.getIsNotAuditProhibit()) && JeePlusProperites.YES.equals(vo.getIsNotAuditProhibit()) ? vo.getIsNotAuditProhibit() : JeePlusProperites.NO);
        stageRecord.setIsNotAuditProhibit( isNotAuditProhibit );
        return stageRecord;
    }

    /**
     * 获取 拼接 审核结果信息
     * 示例： 张三于2021-08-28 19:00:00 审批通过，审批意见：同意
     * @param reason
     * @return
     */
    public static String getCaseAuditReason(String reason){
        return (UserUtils.getUser().getName() +"于"+ DateUtils.formatDateTime(new Date()) + reason);
    }

    /**
     * 数据过滤验证
     * 管理员、主任看所有信息
     * 律师看 自己相关的信息
     * @param entity
     * @param function
     * @param <T>
     * @return
     */
    public static <T> T dataFilterVerify(T entity, BiFunction<T , User, T> function) {
        User user = UserUtils.getUser();
        if(StringUtils.isNotBlank(user.getType()) && user.getType().contains(User.TYPE_LAWYER)){
            entity = function.apply(entity, user);
        }
        return entity;
    }

    /**
     * 类 属性替换 对为null的属性进行 赋值
     * @param source
     * @param target
     * @param <T>
     * @return
     */
    public static <T> T classCopyProperties(T source, T target){
        // 属性替换  对为null的属性进行 赋值
        BeanUtil.copyProperties(source, target, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return target;
    }

    /**
     * 文件创建
     * @param type
     * @param uploadPath
     * @param fileName
     * @return
     */
    public static AjaxJson fileNew(String type, String uploadPath, String fileName){
        AjaxJson j = new AjaxJson();

        if(StringUtils.isBlank(fileName)){
            fileName = "未命名";
            if(ConstantModel.TYPE_EXCEL.equals(type)){
                fileName += ".xlsx";
            }else if(ConstantModel.TYPE_PPT.equals(type)){
                fileName += ".pptx";
            }else {
                fileName += ".docx";
            }
        }

        // 新文件 保存路径
        String fileUrl = FileKit.getAttachmentUrl()+uploadPath;
        String fileDir = FileKit.getAttachmentDir()+uploadPath;
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH )+1;
        fileUrl = (fileUrl +"/"+year+"/"+month+"/");
        fileDir = (fileDir +"/"+year+"/"+month+"/");
        // 创建文件保存路径
        FileUtils.createDirectory(fileDir);
        // 创建新文件 并处理同名问题
        File newFile = FileUtils.getAvailableFile ((fileDir + fileName), 0);
        try {
            // 文件输出流
            FileOutputStream out = new FileOutputStream(newFile);
            // 分类创建Excel、PPT、Word
            if(ConstantModel.TYPE_EXCEL.equals(type)){
                SXSSFWorkbook wb = new SXSSFWorkbook();
                wb.write(out);
            }else if(ConstantModel.TYPE_PPT.equals(type)){
                XMLSlideShow ppt = new XMLSlideShow();
                // 使用第一个（默认）幻灯片母版
                XSLFSlideMaster defaultMaster = ppt.getSlideMasters().get(0);
                // 标题幻灯片
                XSLFSlideLayout titleLayout = defaultMaster.getLayout(SlideLayout.TITLE);
                //根据样式创建幻灯片 并取消占位符 使用默认样式
                XSLFSlide slide = ppt.createSlide(titleLayout);
                slide.getPlaceholder(0).clearText();
                slide.getPlaceholder(1).clearText();
                ppt.write(out);
            }else {
                // 创建Word 文件并保存至指定目录
                XWPFDocument xd = new XWPFDocument();
                xd.write(out);
            }
            out.flush();
            out.close();

            j.put("url", fileUrl + newFile.getName());
            j.put ("name", newFile.getName ());
        } catch (IOException e) {
            e.printStackTrace();
            j = AjaxJson.error("文件创建失败");
        }

        return j;
    }


}
