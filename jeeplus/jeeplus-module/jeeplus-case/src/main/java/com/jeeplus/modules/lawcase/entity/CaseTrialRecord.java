/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.modules.sys.entity.User;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 庭审记录Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseTrialRecord extends DataEntity<CaseTrialRecord> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="案件信息", align=2, sort=1)
	private Case lawCase;		// 案件信息
	@NotNull(message = "开庭日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="开庭日期", align=2, sort=2)
	private Date openCourtDate;		// 开庭日期
	@ExcelField(title="开庭类型", dictType="open_court_type", align=2, sort=3)
	private String openCourtType;		// 开庭类型
	@NotNull(message = "出庭律师不能为空")
	@ExcelField(title="出庭律师", align=2, sort=4)
	private User barrister;		// 出庭律师
	@ExcelField(title="开庭地址", align=2, sort=5)
	private String openCourtAddress;		// 开庭地址
	@ExcelField(title="争议焦点", align=2, sort=6)
	private String controversyFocus;		// 争议焦点
	@ExcelField(title="我方意见", align=2, sort=7)
	private String ourOpinion;		// 我方意见
	@ExcelField(title="对方意见", align=2, sort=8)
	private String otherOpinion;		// 对方意见
	@ExcelField(title="法官态度", align=2, sort=9)
	private String judgeAttitude;		// 法官态度
	@ExcelField(title="庭审总结", align=2, sort=10)
	private String trialSummary;		// 庭审总结
	
	public CaseTrialRecord() {
		super();
	}
	
	public CaseTrialRecord(String id){
		super(id);
	}
}