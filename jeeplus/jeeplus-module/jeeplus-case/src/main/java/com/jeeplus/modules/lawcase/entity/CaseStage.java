/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.modules.lawcase.entity.Case;
import lombok.Data;

/**
 * 案件阶段Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseStage extends DataEntity<CaseStage> {
	
	private static final long serialVersionUID = 1L;
	/* 检测字段  statistic_todo 统计待办事项 */
	public static final String CHECK_WORD_STATISTIC_TODO = "statistic_todo";

	@ExcelField(title="案件信息", align=2, sort=7)
	private Case lawCase;		// 案件信息
	@ExcelField(title="名称", align=2, sort=8)
	private String name;		// 名称
	@ExcelField(title="排序", align=2, sort=9)
	private Integer sort;		// 排序
	@ExcelField(title="是否当前阶段", align=2, sort=10)
	private String isCurrent;		// 是否当前阶段


	// 虚拟
	private String checkWord;			// 检测字段 statistic_todo	统计待办事项
	private Integer todoTotalAmount;	// 待办事项 总数量
	private Integer todoCompleteAmount;	// 待办事项 完成数量

	public CaseStage() {
		super();
	}
	
	public CaseStage(String id){
		super(id);
	}
}