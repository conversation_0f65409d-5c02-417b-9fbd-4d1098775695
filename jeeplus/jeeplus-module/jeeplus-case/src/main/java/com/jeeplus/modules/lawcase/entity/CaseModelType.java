 package com.jeeplus.modules.lawcase.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.jeeplus.core.persistence.TreeEntity;

import lombok.Data;

/**
 * 案件类型模板
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class CaseModelType extends TreeEntity<CaseModelType> {
	
	private static final long serialVersionUID = 1L;
	
	public CaseModelType() {
		super();
	}

	public CaseModelType(String id){
		super(id);
	}

	public  CaseModelType getParent() {
			return parent;
	}
	
	@Override
	public void setParent(CaseModelType parent) {
		this.parent = parent;
		
	}
	
	public String getParentId() {
		return parent != null && parent.getId() != null ? parent.getId() : "0";
	}
}