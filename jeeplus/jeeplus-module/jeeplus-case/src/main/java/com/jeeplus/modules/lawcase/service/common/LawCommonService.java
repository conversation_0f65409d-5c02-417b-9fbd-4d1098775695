/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service.common;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.*;
import com.jeeplus.modules.lawcase.mapper.CaseMapper;
import com.jeeplus.modules.lawcase.service.*;
import com.jeeplus.modules.lawcase.vo.CaseVO;
import com.jeeplus.modules.lawcase.vo.CustomerVO;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 案件公共 Service
 * <AUTHOR>
 * @version 2021-10-21
 */
@Service
@Transactional(readOnly = true)
public class LawCommonService extends CrudService<CaseMapper, Case> {

	@Autowired
	private CaseService caseService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private TodoInfoService todoInfoService;

	/**
	 * 获取案件 分页的 数据
	 * @param page
	 * @param lawCase
	 * @return
	 */
	public Page<CaseVO> findCaseVoPage(Page<Case> page, Case lawCase){
		// 数据权限过滤
		lawCase = CaseConstant.dataFilterVerify(lawCase, (lc, user) -> {
			lc.setHostUser(user);
			return lc;
		});
		/*
			检测字段  他人共享 看到别人共享给自己的案件信息
				共享他人 	查询本人已共享的案件信息
		 */
		if(Case.CHECK_WORD_OTHER_SHARE.equals(lawCase.getCheckWord())){
			lawCase.setHostUser(null);
		} else if(Case.CHECK_WORD_SHARE.equals(lawCase.getCheckWord())){
			lawCase.setIsShare(JeePlusProperites.YES);
		}

		// 查询数据
		page = caseService.findOverallPage(page, lawCase);
		/**
		 * 数据封装 根据案件id  查询对应的待办事项并进行封装
		 */
		List<CaseVO> caseVoList = new ArrayList<>();
		List<Case> caseList = page.getList();
		if(caseList != null && caseList.size() > 0){
			// 查询对应的待办事项信息
			TodoInfo todoInfo = new TodoInfo();
			String relevanceIds = caseList.stream().map(Case::getId).collect(Collectors.joining(","));
			todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CASE);
			todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
			todoInfo.setQueryRelevanceIds(relevanceIds);

			Map<String, List<TodoInfo>> todoMap = todoInfoService.findGroupMap(todoInfo);
			for (Case ca : page.getList()) {
				CaseVO caseVO = new CaseVO(ca);
				List<TodoInfo> todoList = todoMap.get(ca.getId());
				if(todoList == null){ todoList = new ArrayList<>(); }
				caseVO.setTodoInfoList(todoList);
				caseVoList.add(caseVO);
			}
		}

		Page<CaseVO> pageVo = new Page<CaseVO>();
		pageVo.setPageNo(page.getPageNo());
		pageVo.setPageSize(page.getPageSize());
		pageVo.setCount(page.getCount());
		pageVo.setList(caseVoList);
		return pageVo;
	}

	/**
	 * 获取 客户 分页数据
	 * @param page
	 * @param customer
	 * @return
	 */
	public Page<CustomerVO> findCustomerVoPage(Page<Customer> page, Customer customer){
		// 数据过滤验证 仅 律师权限的用户只能看到 创建人是自己 的数据
		customer = CaseConstant.dataFilterVerify(customer, (cus, user) ->{
			cus.setCreateBy(user);
			return cus;
		});
		page = customerService.findOverallPage(page, customer);

		/* 对客户信息进行封装
			1、根据客户id 查询待办事项列表
			2、进行封装
		 */
		List<CustomerVO> customerVoList = new ArrayList<>();
		List<Customer> customerList = page.getList();
		// TODO 2021-09-08 变更，去掉客户待办事项数据展示
		/*if(customerList != null && customerList.size() > 0){
			// 待办事项
			TodoInfo todoInfo = new TodoInfo();
			String customerIds = customerList.stream().map(Customer::getId).collect(Collectors.joining());
			todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CUSTOMER);
			todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
			todoInfo.setType(CaseConstant.TODO_TYPE_TASK);
			todoInfo.setQueryRelevanceIds(customerIds);

			Map<String, List<TodoInfo>> todoMap = todoInfoService.findGroupMap(todoInfo);

			for (Customer ct : customerList) {
				CustomerVO customerVO = new CustomerVO(ct);
				List<TodoInfo> todoList = todoMap.get(ct.getId());
				if(todoList == null){ todoList = new ArrayList<>(); }
				customerVO.setTodoInfoList(todoList);
				customerVoList.add(customerVO);
			}
		}*/
		List<TodoInfo> todoList = new ArrayList<>();
		for (Customer ct : customerList) {
			CustomerVO customerVO = new CustomerVO(ct);
			customerVO.setTodoInfoList(todoList);
			customerVoList.add(customerVO);
		}

		Page<CustomerVO> pageVo = new Page<CustomerVO>();
		pageVo.setCount(page.getCount());
		pageVo.setPageNo(page.getPageNo());
		pageVo.setPageSize(page.getPageSize());
		pageVo.setList(customerVoList);
		return pageVo;
	}

}