package com.jeeplus.modules.lawcase.task;

import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.TodoInfo;
import com.jeeplus.modules.lawcase.service.TodoInfoService;
import com.jeeplus.modules.monitor.entity.Task;
import org.quartz.DisallowConcurrentExecution;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * 待办事项 定时消息推送
 * 每天14:00  20:00 执行推送第二天的待办事项，查询之前的未完成的待办事项。对用户进行消息推送
 * <AUTHOR>
 * @date 2022-04-06
 */
@DisallowConcurrentExecution
public class TodoInfoTomorrowTask extends Task {

    @Autowired
    private TodoInfoService todoInfoService;

    @Override
    public void run() {
//        System.out.println(DateUtils.getDate("yyyy-MM-dd HH:mm:ss") +"这是测试任务TodoInfoTomorrowTask。");
        TodoInfo todoInfo = new TodoInfo();
        todoInfo.setQueryEndDate(DateUtils.addDays(new Date(), 1) );
        todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
        todoInfoService.hostUserMsgPush(todoInfo);
    }

}
