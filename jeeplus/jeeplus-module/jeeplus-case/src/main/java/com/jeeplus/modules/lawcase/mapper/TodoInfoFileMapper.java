/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.lawcase.entity.TodoInfoFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 待办事项 - 附件 MAPPER接口
 * <AUTHOR>
 * @version 2021-08-12
 */
@Mapper
@Repository
public interface TodoInfoFileMapper extends BaseMapper<TodoInfoFile> {
    /**
     * 根据待办事项id 查询 当前级别及其子级 所有的附件信息
     * @param todoId
     * @return
     */
    List<TodoInfoFile> findListByTodo(String todoId);
    /**
     * 根据待办事项 关联类型 关联id 查询所有附件信息
     * @param relevanceType
     * @param relevanceId
     * @return
     */
    List<TodoInfoFile> findListByTodoRelevance(@Param("relevanceType") String relevanceType, @Param("relevanceId") String relevanceId);

    /**
     * 根据案件id、查询待办附件数据
     * @param caseId
     * @return
     */
    List<TodoInfoFile> findFileListByCase(String caseId);

    /**
     * 根据 待办事项id 删除事项附件
     * @param todoInfoId
     * @return
     */
    int deleteByTodo(String todoInfoId);

    /**
     * 根据待办事项 关联类型 关联id 删除附件记录
     * @param relevanceType
     * @param relevanceId
     * @return
     */
    int deleteByTodoRelevance(@Param("relevanceType") String relevanceType, @Param("relevanceId") String relevanceId);
	
    Integer getMaxSortByTid(String id);
    
	void updateInfoDate(TodoInfoFile info);
}