/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseUser;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.service.CaseService;
import com.jeeplus.modules.lawcase.service.CaseUserService;
import com.jeeplus.modules.lawcase.service.StageService;
import com.jeeplus.modules.sys.entity.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import java.util.ArrayList;
import java.util.List;

/**
 * 案件成员 Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@Api(tags = "案件-关联用户信息")
@RestController
@RequestMapping(value = "/case/caseUser")
public class CaseUserController extends BaseController {

	@Autowired
	private CaseUserService caseUserService;
	@Autowired
	private CaseService caseService;

	/**
	 * 案件成员 信息列表数据
	 */
	@RequiresPermissions(value = {"case:caseUser:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "成员信息列表", consumes = "application/form-data")
	@ApiImplicitParam(value = "案件id", name = "lawCase.id", required = true)
	@PostMapping("list")
	public AjaxJson list(CaseUser caseUser, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseUser> page = new Page<CaseUser>(request, response, -1);
		if(caseUser.getLawCase() != null && StringUtils.isNotBlank(caseUser.getLawCase().getId())){
			page = caseUserService.findPage(page, caseUser);
		}
		return AjaxJson.success().put("page",page);
	}

	@RequiresPermissions(value = {"case:caseUser:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "成员信息(便于查询)", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "caseId", required = true),
			@ApiImplicitParam(value = "用户名称", name = "name")
	})
	@PostMapping("userList")
	public AjaxJson userList(String name, String caseId, HttpServletRequest request, HttpServletResponse response) {
		return AjaxJson.success().put("data", caseUserService.findUserList(caseId, name));
	}

	/**
	 * 根据Id获取阶段信息数据
	 */
	@RequiresPermissions(value={"case:caseUser:view","case:caseUser:add","case:caseUser:edit", "user"},logical=Logical.OR)
	@GetMapping("queryById")
	public AjaxJson queryById(CaseUser caseUser) {
		return AjaxJson.success().put("caseUser", caseUser);
	}

	/**
	 * 保存阶段信息
	 */
	@RequiresPermissions(value={"case:caseUser:add","case:caseUser:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "用户id", name = "user.id", required = true)
	})
	@PostMapping("save")
	public AjaxJson save(CaseUser caseUser) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseUser);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseUser.getLawCase() == null || StringUtils.isBlank(caseUser.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		if(caseUser.getUser() == null || StringUtils.isBlank(caseUser.getUser().getId())){
			return AjaxJson.error("成员信息有误");
		}
		Case lawCase = caseService.get(caseUser.getLawCase());
		if(lawCase == null || StringUtils.isBlank(lawCase.getId())){
			return AjaxJson.error("案件信息有误");
		}
		caseUser.setLawCase(lawCase);

		/* 验证是否已关联 */
		List<CaseUser> list = caseUserService.findList(caseUser);
		if(list != null && list.size() > 0){
			return AjaxJson.success("保存成功");
		}

		try {
			caseUserService.save(caseUser);
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}


	/**
	 * 批量删除阶段信息
	 */
	@RequiresPermissions(value = {"case:caseUser:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "记录id 多个以 , 拼接", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseUserService.delete(caseUserService.get(id));
		}
		return AjaxJson.success("移除成功");
	}



}