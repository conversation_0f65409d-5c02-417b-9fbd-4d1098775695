/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.CaseProgram;
import com.jeeplus.modules.lawcase.service.CaseProgramService;

/**
 * 案件程序Controller
 * <AUTHOR>
 * @version 2021-09-04
 */
@Api(tags = "案件程序")
@RestController
@RequestMapping(value = "/lawcase/caseProgram")
public class CaseProgramController extends BaseController {

	@Autowired
	private CaseProgramService caseProgramService;

	@ModelAttribute
	public CaseProgram get(@RequestParam(required=false) String id) {
		CaseProgram entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseProgramService.get(id);
		}
		if (entity == null){
			entity = new CaseProgram();
		}
		return entity;
	}


	/**
	 * 案件程序树表数据
	 */
	@RequiresPermissions("lawcase:caseProgram:list")
	@GetMapping("list")
	public AjaxJson list(CaseProgram caseProgram) {
		return AjaxJson.success().put("list", caseProgramService.findList(caseProgram));
	}

	/**
	 * 根据Id获取案件程序数据
	 */
	@ApiOperation(value = "根据id获取案件程序详情数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@RequiresPermissions(value={"lawcase:caseProgram:view","lawcase:caseProgram:add","lawcase:caseProgram:edit", "user"},logical=Logical.OR)
	@PostMapping("queryById")
	public AjaxJson queryById(CaseProgram caseProgram) {
		return AjaxJson.success().put("caseProgram", caseProgram);
	}

	/**
	 * 保存案件程序
	 */
	@ApiOperation(value = "保存案件程序数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id"),
			@ApiImplicitParam(value = "名称", name = "name", required = true),
			@ApiImplicitParam(value = "排序", name = "sort", required = true),
			@ApiImplicitParam(value = "案件类型 字典：case_type", name = "type", required = true),
			@ApiImplicitParam(value = "上级id", name = "parent.id"),
			@ApiImplicitParam(value = "上级名称", name = "parent.name")
	})
	@RequiresPermissions(value={"lawcase:caseProgram:add","lawcase:caseProgram:edit", "user"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(CaseProgram caseProgram) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseProgram);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		//新增或编辑表单保存
		caseProgramService.save(caseProgram);//保存
		return AjaxJson.success("保存案件程序成功");
	}

	/**
	 * 删除案件程序
	 */
	@ApiOperation(value = "删除案件程序数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值，多个以,拼接", name = "ids", required = true)
	@RequiresPermissions(value = {"lawcase:caseProgram:del", "user"}, logical = Logical.OR)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseProgramService.delete(new CaseProgram(id));
		}
		return AjaxJson.success("删除案件程序成功");
	}

	/**
     * 获取JSON树形数据。
     * @param extId 排除的ID
     * @return
	*/
	@ApiOperation(value = "案件程序树结构所有数据", consumes = "application/form-data")
	@RequiresPermissions("user")
	@PostMapping("treeData")
	public AjaxJson treeData(@RequestParam(required = false) String extId) {
		List<CaseProgram> list = caseProgramService.findList(new CaseProgram());
		List rootTree =  caseProgramService.formatListToTree (new CaseProgram ("0"),list, extId );
		return AjaxJson.success().put("treeData", rootTree);
	}

	@ApiOperation(value = "根据案件类型获取数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件类型", name = "type", required = true)
	})
	@RequiresPermissions("user")
	@PostMapping("typeTreeData")
	public AjaxJson typeTreeData(@RequestParam(required = false) String type) {
		List rootTree = Lists.newArrayList();
		if(StringUtils.isNotBlank(type)){
			CaseProgram caseProgram = new CaseProgram();
			caseProgram.setType(type);
			List<CaseProgram> list = caseProgramService.findList(caseProgram);
			rootTree =  caseProgramService.formatListToTree (new CaseProgram ("0"),list, "" );
		}

		return AjaxJson.success().put("treeData", rootTree);
	}
}