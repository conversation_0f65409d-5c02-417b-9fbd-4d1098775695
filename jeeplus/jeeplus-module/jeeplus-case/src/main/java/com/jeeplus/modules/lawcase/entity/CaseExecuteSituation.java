/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 执行情况Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseExecuteSituation extends DataEntity<CaseExecuteSituation> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="案件信息", align=2, sort=1)
	private Case lawCase;		// 案件信息
    @JsonFormat(pattern = "yyyy-MM-dd")
	@ExcelField(title="申请执行日", align=2, sort=2)
	private Date applyDate;		// 申请执行日
	@ExcelField(title="受理单位", align=2, sort=3)
	private String acceptUnit;		// 受理单位
	@ExcelField(title="执行案号", align=2, sort=4)
	private String number;		// 执行案号
	@NotBlank(message = "执行状态不能为空")
	@ExcelField(title="执行状态", dictType="execute_status", align=2, sort=5)
	private String status;		// 执行状态
	@NotBlank(message = "执行措施不能为空")
	@ExcelField(title="执行措施", dictType="execute_measures", align=2, sort=6)
	private String measures;		// 执行措施
	@ExcelField(title="执行请求", align=2, sort=7)
	private String executeRequest;		// 执行请求
	@ExcelField(title="履行情况", align=2, sort=8)
	private String performance;		// 履行情况
	
	public CaseExecuteSituation() {
		super();
	}
	
	public CaseExecuteSituation(String id){
		super(id);
	}
}