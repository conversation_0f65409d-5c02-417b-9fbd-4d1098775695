/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.sys.entity.User;
import lombok.Data;

/**
 * 客户跟进人 Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CustomerFollowUp extends DataEntity<CustomerFollowUp> {

	private static final long serialVersionUID = 1L;

	private Customer customer;	// 客户信息
	private User user;			// 用户信息

	public CustomerFollowUp() {
		super();
	}

	public CustomerFollowUp(Customer customer) {
		this.customer = customer;
	}

	public CustomerFollowUp(String id){
		super(id);
	}
}