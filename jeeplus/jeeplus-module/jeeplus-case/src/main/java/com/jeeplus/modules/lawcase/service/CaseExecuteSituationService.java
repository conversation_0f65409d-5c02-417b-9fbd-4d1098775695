/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.CaseExecuteSituation;
import com.jeeplus.modules.lawcase.mapper.CaseExecuteSituationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 执行情况Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseExecuteSituationService extends CrudService<CaseExecuteSituationMapper, CaseExecuteSituation> {

	public CaseExecuteSituation get(String id) {
		return super.get(id);
	}
	
	public List<CaseExecuteSituation> findList(CaseExecuteSituation caseExecuteSituation) {
		return super.findList(caseExecuteSituation);
	}
	
	public Page<CaseExecuteSituation> findPage(Page<CaseExecuteSituation> page, CaseExecuteSituation caseExecuteSituation) {
		return super.findPage(page, caseExecuteSituation);
	}
	
	@Transactional(readOnly = false)
	public void save(CaseExecuteSituation caseExecuteSituation) {
		super.save(caseExecuteSituation);
	}
	
	@Transactional(readOnly = false)
	public void delete(CaseExecuteSituation caseExecuteSituation) {
		super.delete(caseExecuteSituation);
	}
	
}