/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import javax.validation.constraints.NotNull;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.TreeEntity;
import lombok.Data;

/**
 * 行业信息Entity
 * <AUTHOR>
 * @version 2021-08-01
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class Industry extends TreeEntity<Industry> {
	
	private static final long serialVersionUID = 1L;
	
	
	public Industry() {
		super();
	}

	public Industry(String id){
		super(id);
	}

	public  Industry getParent() {
			return parent;
	}

	@Override
	public void setParent(Industry parent) {
		this.parent = parent;
		
	}
	
	public String getParentId() {
		return parent != null && parent.getId() != null ? parent.getId() : "0";
	}
}