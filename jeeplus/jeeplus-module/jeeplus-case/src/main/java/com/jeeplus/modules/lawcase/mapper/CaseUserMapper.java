/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.lawcase.entity.CaseUser;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.vo.StageVO;
import com.jeeplus.modules.sys.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 案件成员 MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CaseUserMapper extends BaseMapper<CaseUser> {
    /**
     * 根据案件id 查询已有的 成员数量
     * @param caseId
     * @return
     */
    Integer getCountByCase(String caseId);

    /**
     * 获取用户信息  可根据案件id 排除已关联的 用户
     * @param caseId
     * @param name
     * @return
     */
    List<User> findUserList(@Param("caseId") String caseId, @Param("name") String name);

    /**
     * 根据案件id 删除成员信息
     * @param lawCaseId
     * @return
     */
    int deleteByCase(String lawCaseId);
}