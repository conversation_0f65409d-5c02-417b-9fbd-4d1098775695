/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.CaseHandleStrategy;
import com.jeeplus.modules.lawcase.mapper.CaseHandleStrategyMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 案件办案策略Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseHandleStrategyService extends CrudService<CaseHandleStrategyMapper, CaseHandleStrategy> {

	public CaseHandleStrategy get(String id) {
		return super.get(id);
	}
	
	public List<CaseHandleStrategy> findList(CaseHandleStrategy caseHandleStrategy) {
		return super.findList(caseHandleStrategy);
	}
	
	public Page<CaseHandleStrategy> findPage(Page<CaseHandleStrategy> page, CaseHandleStrategy caseHandleStrategy) {
		return super.findPage(page, caseHandleStrategy);
	}
	
	@Transactional(readOnly = false)
	public void save(CaseHandleStrategy caseHandleStrategy) {
		super.save(caseHandleStrategy);
	}
	
	@Transactional(readOnly = false)
	public void delete(CaseHandleStrategy caseHandleStrategy) {
		super.delete(caseHandleStrategy);
	}
	
}