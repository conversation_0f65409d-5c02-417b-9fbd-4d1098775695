package com.jeeplus.modules.lawcase.web;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jeeplus.modules.lawcase.entity.TodoInfoFile;
import com.jeeplus.modules.lawcase.service.TodoInfoService;

@RestController
public class WpsWebOfficeController {
    @Autowired
	private TodoInfoService todoInfoService;
    
    @RequestMapping(value = "/v3/3rd/files/{fileid}", method = RequestMethod.GET)
    public Object v3rdFiles(@PathVariable("fileid") String fileid) throws Exception {
        System.out.println("Method fileIno(" + fileid + ") is invoked");
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
        	TodoInfoFile info=todoInfoService.getByFileId(fileid);
        	if(info==null) {
        		 return data.toString();
        	}
        	jsonObject.put("id", fileid);         // 文件id,字符串长度小于40
        	jsonObject.put("name", info.getName()); 
        	jsonObject.put("version",info.getFileversion()==null?1:info.getFileversion()); 
        	jsonObject.put("size", info.getFilesizes()==null?1:info.getFilesizes()); 
        	jsonObject.put("create_time", info.getCreateDate().getTime()); 
        	jsonObject.put("modify_time", info.getUpdateDate().getTime()); 
        	jsonObject.put("creator_id", "1"); 
        	jsonObject.put("modifier_id", "1"); 
        } catch (JSONException e) {
            e.printStackTrace();
        }
        data.put("data", jsonObject);
        data.put("code", 0);
        return data.toString();

    }
    
    @RequestMapping(value = "/v3/3rd/files/{fileid}/download", method = RequestMethod.GET)
    public Object v3rdFilesdownload(@PathVariable("fileid") String fileid) throws Exception {
        System.out.println("Method download (" + fileid + ") is invoked");
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
        	TodoInfoFile info=todoInfoService.getByFileId(fileid);
        	if(info==null) {
        		 return data.toString();
        	}
        	jsonObject.put("url",info.getFullPath()); 
         
        } catch (JSONException e) {
            e.printStackTrace();
        }
        data.put("data", jsonObject);
        data.put("code", 0);
        return data.toString();

    }
    
    @RequestMapping(value = "/v3/3rd/files/{fileid}/permission", method = RequestMethod.GET)
    public Object v3rdFilespermission(@PathVariable("fileid") String fileid) throws Exception {
        System.out.println("Method permission (" + fileid + ") is invoked");
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
        	jsonObject.put("read", 1); 
        	jsonObject.put("update", 1); 
        	jsonObject.put("download", 1); 
        	jsonObject.put("print", 1); 
        	jsonObject.put("user_id", "1"); 
        } catch (JSONException e) {
            e.printStackTrace();
        }
        data.put("data", jsonObject);
        data.put("code", 0);
        return data.toString();

    }
    
    @RequestMapping(value = "/v3/3rd/users", method = RequestMethod.GET)
    public Object getUserInfo( String user_ids) {
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject user = new JSONObject();
        try {
            user.put("id", "1");
            user.put("name", "用户");
           // user.put("name", UserUtils.getByLoginName (_w_userid,null).getName ());
            user.put("avatar_url",  "");
            jsonArray.put(user);
            jsonObject.put("data", jsonArray);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        jsonObject.put("code", 0);
        return jsonObject.toString();
    }

    
    @RequestMapping(value = "/v3/3rd/files/{fileid}/upload", method = RequestMethod.POST)
    public Object v3rdUploadFiles(@PathVariable("fileid") String fileid
    		,@RequestBody MultipartFile file,Integer size) throws Exception {
        System.out.println("Method v3rdUploadFiles(" + fileid + ") is invoked");
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
        	TodoInfoFile info=todoInfoService.getByFileId(fileid);
        	if(info==null) {
        		 return data.toString();
        	}
        	
        	
        	String file1 = info.getFullFilePath();
            File f = new File(file1);
            if (!file.isEmpty()) {
                try {
                    BufferedOutputStream out = new BufferedOutputStream(
                            new FileOutputStream(f));
                    out.write(file.getBytes());
                    out.flush();
                    out.close();
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            
           	info.setFilesizes(size);
        	info.setFileversion(info.getFileversion()+1);
        	todoInfoService.updateFileInfo(info);
        	
        	jsonObject.put("id", fileid);         // 文件id,字符串长度小于40
        	jsonObject.put("name", info.getName()); 
        	jsonObject.put("version",info.getFileversion()==null?1:info.getFileversion()); 
        	jsonObject.put("size", info.getFilesizes()==null?1:info.getFilesizes()); 
        	jsonObject.put("create_time", info.getCreateDate().getTime()); 
        	jsonObject.put("modify_time", info.getUpdateDate().getTime()); 
        	jsonObject.put("creator_id", "1"); 
        	jsonObject.put("modifier_id", "1"); 
        } catch (JSONException e) {
            e.printStackTrace();
        }
        data.put("data", jsonObject);
        data.put("code", 0);
        return data.toString();

    }

}
