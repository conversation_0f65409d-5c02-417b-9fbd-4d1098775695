/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.entity.CustomerContacts;
import com.jeeplus.modules.lawcase.service.CustomerContactsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 客户联系人Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "客户联系人", description = "客户联系人")
@RequestMapping(value = "/case/customerContacts")
public class CustomerContactsController extends BaseController {

	@Autowired
	private CustomerContactsService customerContactsService;

	@ModelAttribute
	public CustomerContacts get(@RequestParam(required=false) String id) {
		CustomerContacts entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = customerContactsService.get(id);
		}
		if (entity == null){
			entity = new CustomerContacts();
		}
		return entity;
	}

	/**
	 * 客户联系人列表数据
	 */
	@RequiresPermissions(value = {"case:customerContacts:list","user"}, logical = Logical.OR)
	@ApiOperation(value = "客户联系人数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "客户信息id", name = "customer.id", paramType = "String", required = true)
	@PostMapping("list")
	public AjaxJson list(CustomerContacts customerContacts, HttpServletRequest request, HttpServletResponse response) {
		Page<CustomerContacts> page = new Page<CustomerContacts>(request, response);
		if(customerContacts.getCustomer() != null && StringUtils.isNotBlank(customerContacts.getCustomer().getId())){
			page = customerContactsService.findPage(page, customerContacts);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取客户联系人数据
	 */
	@RequiresPermissions(value={"case:customerContacts:view","case:customerContacts:add","case:customerContacts:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "详细信息", consumes = "application/form-data")
	@PostMapping("queryById")
	public AjaxJson queryById(CustomerContacts customerContacts) {
		return AjaxJson.success().put("customerContacts", customerContacts);
	}

	/**
	 * 保存客户联系人
	 */
	@RequiresPermissions(value={"case:customerContacts:add","case:customerContacts:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存客户联系人", consumes = "application/form-data")
	@PostMapping("save")
	public AjaxJson save(CustomerContacts customerContacts) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(customerContacts);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(customerContacts.getCustomer() == null || StringUtils.isBlank(customerContacts.getCustomer().getId())){
			return AjaxJson.error("客户信息有误");
		}
		//新增或编辑表单保存
		try {
			customerContactsService.save(customerContacts);
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}


	/**
	 * 批量删除客户联系人
	 */
	@RequiresPermissions(value = {"case:customerContacts:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			customerContactsService.delete(new CustomerContacts(id));
		}
		return AjaxJson.success("删除成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("case:customerContacts:export")
    @GetMapping("export")
    public AjaxJson exportFile(CustomerContacts customerContacts, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "客户联系人"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CustomerContacts> page = customerContactsService.findPage(new Page<CustomerContacts>(request, response, -1), customerContacts);
    		new ExportExcel("客户联系人", CustomerContacts.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出客户联系人记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:customerContacts:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CustomerContacts> list = ei.getDataList(CustomerContacts.class);
			for (CustomerContacts customerContacts : list){
				try{
					customerContactsService.save(customerContacts);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条客户联系人记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条客户联系人记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入客户联系人失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入客户联系人数据模板
	 */
	@RequiresPermissions("case:customerContacts:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "客户联系人数据导入模板.xlsx";
    		List<CustomerContacts> list = Lists.newArrayList();
    		new ExportExcel("客户联系人数据", CustomerContacts.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}