package com.jeeplus.modules.lawcase.task;

import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.TodoInfo;
import com.jeeplus.modules.lawcase.service.TodoInfoService;
import com.jeeplus.modules.monitor.entity.Task;
import org.quartz.DisallowConcurrentExecution;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * 待办事项 定时消息推送
 * 每天9:00 执行，查询之前的未完成的待办事项。对用户进行消息推送
 * <AUTHOR>
 * @date 2022-02-28
 */
@DisallowConcurrentExecution
public class TodoInfoTask extends Task {

    @Autowired
    private TodoInfoService todoInfoService;

    @Override
    public void run() {
//        System.out.println("这是测试任务TodoInfoTask。");
        TodoInfo todoInfo = new TodoInfo();
        todoInfo.setQueryEndDate(new Date());
        todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
        todoInfoService.hostUserMsgPush(todoInfo);
    }

}
