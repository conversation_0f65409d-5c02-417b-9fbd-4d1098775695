<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseConcernPersonMapper">

	<sql id="caseConcernPersonColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.case_id AS "lawCase.id",
		a.type AS "type",
		a.is_entrust AS "isEntrust",
		a.name AS "name",
		a.attribute AS "attribute",
		a.nation AS "nation",
		a.sex AS "sex",
		a.phone AS "phone",
		a.id_number AS "idNumber",
		a.address AS "address",
		a.legal_representative AS "legalRepresentative",
		a.unified_social_credit_code AS "unifiedSocialCreditCode"
	</sql>

	<sql id="caseConcernPersonJoins">

	</sql>


	<select id="get" resultType="CaseConcernPerson" >
		SELECT
			<include refid="caseConcernPersonColumns"/>
		FROM law_case_concern_person a
		<include refid="caseConcernPersonJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseConcernPerson" >
		SELECT
			<include refid="caseConcernPersonColumns"/>
		FROM law_case_concern_person a
		<include refid="caseConcernPersonJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findOverallList" resultType="CaseConcernPersonDto" >
		SELECT
			a.id AS "id",
			a.create_date AS "createDate",
			a.update_date AS "updateDate",
			a.remarks AS "remarks",
			a.case_id AS "lawCaseId",
			a.type AS "type",
			a.is_entrust AS "isEntrust",
			a.name AS "name",
			a.attribute AS "attribute",
			a.nation AS "nation",
			a.sex AS "sex",
			a.id_number AS "idNumber",
			a.phone AS "phone",
			a.address AS "address",
			a.legal_representative AS "legalRepresentative",
			a.unified_social_credit_code AS "unifiedSocialCreditCode",

			lc.name AS "lawCaseName",
			lc.case_cause_name AS "caseCause.name",
			lc.case_program_name AS "caseProgram.name",
			lc.host_user_id AS "hostUser.id",
			hostUser.name AS "hostUser.name",

			(CASE WHEN ( IFNULL(a.case_id, '') = '' ) THEN ''
				ELSE (SELECT GROUP_CONCAT(up.name) FROM law_case_undertake_person up WHERE up.case_id = a.case_id GROUP BY up.case_id) END) AS "undertakePersonNames"
		FROM law_case_concern_person a
		LEFT JOIN law_case lc ON a.case_id = lc.id
		LEFT JOIN sys_user hostUser ON hostUser.id = lc.host_user_id
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCaseId != null and lawCaseId != ''">
				AND a.case_id = #{lawCaseId}
			</if>
			<if test="lawCaseName != null and lawCaseName != ''">
				AND lc.name LIKE concat('%',#{lawCaseName},'%')
			</if>
			<if test="hostUser != null and hostUser.id != null and hostUser.id != ''">
				AND lc.host_user_id = #{hostUser.id}
			</if>
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="name != null and name != ''">
				AND a.name  LIKE concat('%',#{name},'%')
			</if>
			<if test="isEntrust != null and isEntrust != ''">
				AND a.is_entrust = #{isEntrust}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CaseConcernPerson" >
		SELECT
			<include refid="caseConcernPersonColumns"/>
		FROM law_case_concern_person a
		<include refid="caseConcernPersonJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_concern_person(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			case_id,
			`type`,
			is_entrust,
			`name`,
			attribute,
			nation,
			sex,
			phone,
			id_number,
			address,
			legal_representative,
			unified_social_credit_code
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{lawCase.id},
			#{type},
			#{isEntrust},
			#{name},
			#{attribute},
			#{nation},
			#{sex},
			#{phone},
			#{idNumber},
			#{address},
			#{legalRepresentative},
			#{unifiedSocialCreditCode}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_case_concern_person(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			case_id,
			`type`,
			is_entrust,
			`name`,
			attribute,
			nation,
			sex,
			phone,
			id_number,
			address,
			legal_representative,
			unified_social_credit_code
		)
		VALUES
		<foreach collection="list" item="item" separator=" , ">
		  (
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.lawCase.id},
			#{item.type},
			#{item.isEntrust},
			#{item.name},
			#{item.attribute},
			#{item.nation},
			#{item.sex},
			#{item.phone},
			#{item.idNumber},
			#{item.address},
			#{item.legalRepresentative},
			#{item.unifiedSocialCreditCode}
		  )
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_case_concern_person SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			case_id = #{lawCase.id},
			`type` = #{type},
			is_entrust = #{isEntrust},
			`name` = #{name},
			attribute = #{attribute},
			nation = #{nation},
			sex = #{sex},
			phone = #{phone},
			id_number = #{idNumber},
			address = #{address},
			legal_representative = #{legalRepresentative},
			unified_social_credit_code = #{unifiedSocialCreditCode}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_concern_person
		WHERE id = #{id}
	</update>

	<update id="deleteByCase">
		DELETE FROM law_case_concern_person
		WHERE case_id = #{lawCaseId}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_concern_person SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseConcernPerson">
		select * FROM law_case_concern_person  where ${propertyName} = #{value}
	</select>

</mapper>