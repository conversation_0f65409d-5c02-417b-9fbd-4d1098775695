package com.jeeplus.modules.lawcase.web;


import com.corundumstudio.socketio.AckRequest;
import com.corundumstudio.socketio.SocketIOClient;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.modules.socketio.service.SocketIoService;
import com.jeeplus.modules.socketio.utils.SocketIoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collection;

@Controller
@RequestMapping(value = "test")
public class TestController {

    @Autowired
    private SocketIoService socketIoService;

    @ResponseBody
    @RequestMapping(value = "send", method = RequestMethod.GET)
    public AjaxJson send(){
        Collection<SocketIOClient> allValue = SocketIoUtils.getAllValues();
        for (SocketIOClient client : allValue) {
            client.sendEvent("news", "55555555555");
        }
        return AjaxJson.success("成功");
    }

}
