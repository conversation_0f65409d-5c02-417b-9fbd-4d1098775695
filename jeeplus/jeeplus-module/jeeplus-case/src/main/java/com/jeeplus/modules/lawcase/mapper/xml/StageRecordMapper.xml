<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.StageRecordMapper">

    <resultMap id="stageRecordResult" type="StageRecord">
   		<result property="id" column="id" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<result property="parentIds" column="parentIds" />
		<result property="stageTemplate.id" column="stageTemplate.id" />
		<result property="stage.id" column="stage.id" />
		<result property="type" column="type" />
		<result property="nums" column="nums" />
		<result property="content" column="content" />
		<result property="isNotAuditProhibit" column="isNotAuditProhibit" />
    </resultMap>

	<sql id="stageRecordColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.stage_template_id AS "stageTemplate.id",
		a.stage_id AS "stage.id",
		a.type AS "type",
		a.name AS "name",
		a.content AS "content",
		a.sort AS "sort",
		a.is_not_audit_prohibit AS "isNotAuditProhibit",
		a.parent_id AS "parent.id",
		a.nums as "nums",
		a.parent_ids AS "parentIds"
		
	</sql>




	<sql id="stageRecordJoins">

	</sql>



	<select id="get" resultType="StageRecord">
		SELECT
			<include refid="stageRecordColumns"/>
		FROM law_stage_record a
		<include refid="stageRecordJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="StageRecord">
		SELECT
			<include refid="stageRecordColumns"/>
		FROM law_stage_record a
		<include refid="stageRecordJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="stageTemplate != null and stageTemplate.id != null and stageTemplate.id != ''">
				AND a.stage_template_id = #{stageTemplate.id}
			</if>
			<if test="stage != null and stage.id != null and stage.id != ''">
				AND a.stage_id = #{stage.id}
			</if>
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%') 
			</if>
			<if test="parent != null and parent.id != null and parent.id != ''">
				AND a.parent_id = #{parent.id}
			</if>
			<if test="parentIds != null and parentIds != ''">
				AND a.parent_ids LIKE concat('%',#{parentIds},'%') 
			</if>
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="findAllList" resultType="StageRecord">
		SELECT
			<include refid="stageRecordColumns"/>
		FROM law_stage_record a
		<include refid="stageRecordJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="getChildren" parameterType="String" resultMap="stageRecordResult">
        select * from law_stage_record where parent_id = #{id} ORDER BY sort
    </select>

	<select id="findByParentIdsLike" resultType="StageRecord">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM law_stage_record a
		<include refid="stageRecordJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			AND a.parent_ids LIKE #{parentIds}
		</where>
		ORDER BY a.sort ASC
	</select>

	<insert id="insert">
		INSERT INTO law_stage_record(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
			   stage_template_id,
			   stage_id,
			   `type`,
			   `name`,
			   content,
			   sort,
			   is_not_audit_prohibit,
			   parent_id,
			   parent_ids
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{stageTemplate.id},
			#{stage.id},
			#{type},
			#{name},
			#{content},
			#{sort},
			#{isNotAuditProhibit},
			#{parent.id},
			#{parentIds}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_stage_record(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
			   stage_template_id,
			   stage_id,
			   `type`,
			   `name`,
			   content,
			   sort,
				is_not_audit_prohibit,
			   parent_id,
			   parent_ids,
			   nums
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.stageTemplate.id},
			#{item.stage.id},
			#{item.type},
			#{item.name},
			#{item.content},
			#{item.sort},
			#{item.isNotAuditProhibit},
			#{item.parent.id},
			#{item.parentIds},
			#{item.nums}
			)
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_stage_record SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			stage_template_id = #{stageTemplate.id},
			stage_id = #{stage.id},
			`type` = #{type},
			`name` = #{name},
			`nums` = #{nums},
			content = #{content},
			sort = #{sort},
			is_not_audit_prohibit = #{isNotAuditProhibit},
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE law_stage_record SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_stage_record
		WHERE id = #{id} OR parent_ids LIKE CONCAT('%,', #{id}, ',%') 
	</update>

	<update id="deleteByStage">
		DELETE FROM law_stage_record WHERE stage_template_id = #{stageTemplate.id} AND stage_id = #{stage.id}
	</update>

	<update id="deleteByTemplate">
		DELETE FROM law_stage_record WHERE stage_template_id = #{templateId}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_stage_record SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE CONCAT('%,', #{id}, ',%') 
	</update>

</mapper>