/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import lombok.Data;

/**
 * 案件与客户关联 Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseCustomerRelation extends DataEntity<CaseCustomerRelation> {

	private static final long serialVersionUID = 1L;
	private Case lawCase;			// 案件信息
	private Customer customer;		// 关联客户

	public CaseCustomerRelation() {
		super();
	}

	public CaseCustomerRelation(Case lawCase) {
		this.lawCase = lawCase;
	}

	public CaseCustomerRelation(String id){
		super(id);
	}
}