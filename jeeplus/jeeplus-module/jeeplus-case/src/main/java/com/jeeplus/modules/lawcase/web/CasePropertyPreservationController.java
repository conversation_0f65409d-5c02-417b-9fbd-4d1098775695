/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.entity.CasePropertyPreservation;
import com.jeeplus.modules.lawcase.service.CasePropertyPreservationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 财产保全Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-财产保全")
@RequestMapping(value = "/case/casePropertyPreservation")
public class CasePropertyPreservationController extends BaseController {

	@Autowired
	private CasePropertyPreservationService casePropertyPreservationService;

	@ModelAttribute
	public CasePropertyPreservation get(@RequestParam(required=false) String id) {
		CasePropertyPreservation entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = casePropertyPreservationService.get(id);
		}
		if (entity == null){
			entity = new CasePropertyPreservation();
		}
		return entity;
	}

	/**
	 * 财产保全列表数据
	 */
	@RequiresPermissions(value = {"case:casePropertyPreservation:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "列表数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值",name = "lawCase.id", required = true)
	})
	@PostMapping("list")
	public AjaxJson list(CasePropertyPreservation casePropertyPreservation, HttpServletRequest request, HttpServletResponse response) {
		Page<CasePropertyPreservation> page = new Page<CasePropertyPreservation>(request, response);
		if(casePropertyPreservation.getLawCase() != null && StringUtils.isNotBlank(casePropertyPreservation.getLawCase().getId())){
			page = casePropertyPreservationService.findPage(page, casePropertyPreservation);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取财产保全数据
	 */
	@RequiresPermissions(value={"case:casePropertyPreservation:view","case:casePropertyPreservation:add","case:casePropertyPreservation:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "ID值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(CasePropertyPreservation casePropertyPreservation) {
		return AjaxJson.success().put("casePropertyPreservation", casePropertyPreservation);
	}

	/**
	 * 保存财产保全
	 */
	@RequiresPermissions(value={"case:casePropertyPreservation:add","case:casePropertyPreservation:edit", "user"},logical=Logical.OR)
	@ApiOperation("保存")
	@PostMapping("save")
	public AjaxJson save(@RequestBody CasePropertyPreservation casePropertyPreservation) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(casePropertyPreservation);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(StringUtils.isBlank(casePropertyPreservation.getApplicant())){
			return AjaxJson.error("申请人不能为空");
		}
		if(StringUtils.isBlank(casePropertyPreservation.getPropertyType())){
			return AjaxJson.error("财产类型不能为空");
		}
		if(casePropertyPreservation.getLawCase() == null || StringUtils.isBlank(casePropertyPreservation.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}

		try {
			casePropertyPreservationService.save(casePropertyPreservation);
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
		return AjaxJson.success("保存成功");
	}


	/**
	 * 批量删除财产保全
	 */
	@RequiresPermissions(value = {"case:casePropertyPreservation:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值拼接以,拼接字符串", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			casePropertyPreservationService.delete(new CasePropertyPreservation(id));
		}
		return AjaxJson.success("删除成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("case:casePropertyPreservation:export")
    @GetMapping("export")
    public AjaxJson exportFile(CasePropertyPreservation casePropertyPreservation, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "财产保全"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CasePropertyPreservation> page = casePropertyPreservationService.findPage(new Page<CasePropertyPreservation>(request, response, -1), casePropertyPreservation);
    		new ExportExcel("财产保全", CasePropertyPreservation.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出财产保全记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:casePropertyPreservation:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CasePropertyPreservation> list = ei.getDataList(CasePropertyPreservation.class);
			for (CasePropertyPreservation casePropertyPreservation : list){
				try{
					casePropertyPreservationService.save(casePropertyPreservation);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条财产保全记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条财产保全记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入财产保全失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入财产保全数据模板
	 */
	@RequiresPermissions("case:casePropertyPreservation:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "财产保全数据导入模板.xlsx";
    		List<CasePropertyPreservation> list = Lists.newArrayList();
    		new ExportExcel("财产保全数据", CasePropertyPreservation.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}