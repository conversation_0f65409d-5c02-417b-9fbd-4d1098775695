/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;


import java.io.Serializable;

import lombok.Data;

/**
 * 阶段模版 VO
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class StageTemplateSortVO implements Serializable {

	private static final long serialVersionUID = 1L;
	private String startId;
	private Integer startSort;
	private String endId;
	private Integer endSort;
	public StageTemplateSortVO() {
		super();
	}

}