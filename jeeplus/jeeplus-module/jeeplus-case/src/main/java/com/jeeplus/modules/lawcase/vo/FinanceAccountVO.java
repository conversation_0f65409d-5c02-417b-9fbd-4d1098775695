/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.FinanceFlowRecord;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 财务账目Entity
 * <AUTHOR>
 * @version 2021-10-29
 */
@Data
public class FinanceAccountVO implements Serializable {

	private static final long serialVersionUID = 1L;
	@NotBlank(message = "名称不能为空")
	private String name;		// 名称
	@NotBlank(message = "类型不能为空")
	private String type;		// 类型
	private Double receivableMoney;		// 应收金额
	@NotNull(message = "关联案件信息不能为空")
	private Case lawCase;		// 关联案件信息名称
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date agreedPaymentDate;		// 约定收款日期
	private String remarks;		// 备注

	private List<FinanceFlowRecord> flowRecordList;	// 财务流水记录列表

	public FinanceAccountVO() {
		super();
	}

}