/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseCaseRelation;
import com.jeeplus.modules.lawcase.entity.CaseUser;
import com.jeeplus.modules.sys.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 案件案件关联 MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CaseCaseRelationMapper extends BaseMapper<CaseCaseRelation> {

    /**
     * 获取案件信息  可根据案件id 排除已关联的 案件
     * @param map
     * @return
     */
    List<Case> findRelationCaseList(Map<String, String> map);

    /**
     * 根据案件id 删除成员信息
     * @param lawCaseId
     * @return
     */
    int deleteByCase(String lawCaseId);
}