/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import javax.validation.constraints.NotNull;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.jeeplus.core.persistence.TreeEntity;
import lombok.Data;

/**
 * 案由Entity
 * <AUTHOR>
 * @version 2021-09-04
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class CaseCause extends TreeEntity<CaseCause> {
	
	private static final long serialVersionUID = 1L;
	
	
	public CaseCause() {
		super();
	}

	public CaseCause(String id){
		super(id);
	}

	public  CaseCause getParent() {
			return parent;
	}
	
	@Override
	public void setParent(CaseCause parent) {
		this.parent = parent;
		
	}
	
	public String getParentId() {
		return parent != null && parent.getId() != null ? parent.getId() : "0";
	}
}