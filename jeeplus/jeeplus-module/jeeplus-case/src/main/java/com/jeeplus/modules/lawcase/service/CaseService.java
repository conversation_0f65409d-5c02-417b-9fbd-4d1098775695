/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.*;
import com.jeeplus.modules.lawcase.mapper.CaseMapper;
import com.jeeplus.modules.lawcase.vo.CaseVO;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 案件信息Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseService extends CrudService<CaseMapper, Case> {

	@Autowired
	private CaseCaseRelationService caseRelationService;
	@Autowired
	private CaseCustomerRelationService customerRelationService;
	@Autowired
	private CaseUndertakePersonService caseUndertakePersonService;
	@Autowired
	private CaseConcernPersonService caseConcernPersonService;
	@Autowired
	private CaseFileDirectoryService caseFileDirectoryService;
	@Autowired
	private TodoInfoService todoInfoService;


	public Case get(String id) {
		return super.get(id);
	}
	
	public List<Case> findList(Case lawCase) {
		return super.findList(lawCase);
	}

	public Page<Case> findPage(Page<Case> page, Case lawCase) {
		return super.findPage(page, lawCase);
	}

	public String createNumber(){
		return super.createNumber("SD", mapper.getMaxNumber());
	}

	/**
	 * 获取完整信息 案件列表
	 * @param page
	 * @param lawCase
	 * @return
	 */
	public Page<Case> findOverallPage(Page<Case> page, Case lawCase) {
		dataRuleFilter(lawCase);
		lawCase.setPage(page);
		page.setList(mapper.findOverallList(lawCase));
		return page;
	}

	/**
	 * 更新案件 结案状态信息
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateSettle(Case lawCase){
		mapper.updateSettle(lawCase);
	}

	/**
	 * 更新受理单位
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateAccept(Case lawCase){
		mapper.updateAccept(lawCase);
	}

	/**
	 * 更新受理单位、立案日期、裁决日期、审理结果
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateAcceptAndResult(Case lawCase){
		mapper.updateAcceptAndResult(lawCase);
	}

	/**
	 * 更新案件归档信息
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateArchive(Case lawCase){
		mapper.updateArchive(lawCase);
	}

	/**
	 * 更新案件状态
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateStatus(Case lawCase){
		mapper.updateStatus(lawCase);
	}

	/**
	 * 更新案件审核状态
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateAuditStatus(Case lawCase){
		mapper.updateAuditStatus(lawCase);
	}

	/**
	 * 案件终止，更新案件状态、审批状态
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void caseStop(Case lawCase){
		this.updateStatus(lawCase);
		this.updateAuditStatus(lawCase);
	}
	/**
	 * 更新是否共享状态
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateIsShare(Case lawCase){
		mapper.updateIsShare(lawCase);
	}

	@Transactional(readOnly = false)
	public void save(Case lawCase) {
		super.save(lawCase);
	}

	/**
	 * 新增、审批拒绝修改案件信息
	 * @param lawCaseVo
	 */
	@Transactional(readOnly = false)
	public Case saveInfo(CaseVO lawCaseVo) {
		Case lawCase = lawCaseVo.getLawCase();
		/* 是否首次 新纪录 */
		boolean isFirst = StringUtils.isBlank(lawCase.getId());
		if(isFirst){	// 设置基本配置信息
			lawCase.setIsShare(JeePlusProperites.NO);
			lawCase.setAuditStatus(CaseConstant.CASE_AUDIT_STATUS_PASS);
			lawCase.setIsAudit(JeePlusProperites.NO);
			lawCase.setStatus(CaseConstant.CASE_STATUS_PROCESS);
			lawCase.setNumber(this.createNumber());
		}
		// 若审核拒绝 则修改为待审核
		if(CaseConstant.CASE_AUDIT_STATUS_REJECT.equals(lawCase.getAuditStatus())){
			lawCase.setAuditStatus(CaseConstant.CASE_AUDIT_STATUS_WAIT);
		}
		if(StringUtils.isBlank(lawCase.getNumber())){
			lawCase.setNumber(this.createNumber());
		}
		super.save(lawCase);

		/* 若为首次创建  新增案件文件根目录 */
		if(isFirst){
			caseFileDirectoryService.createRoot(lawCase.getId());
		}
		/* 维护 案件当事人信息  先删除旧记录 添加新纪录 */
		if(!isFirst){
			caseConcernPersonService.deleteByCase(lawCase.getId());
		}
		List<CaseConcernPerson> concernPersonList = lawCaseVo.getConcernPersonList();
		if(concernPersonList != null && concernPersonList.size() > 0){
			for (CaseConcernPerson concernPerson : concernPersonList) {
				concernPerson.setLawCase(lawCase);
				concernPerson.preInsert();
			}
			caseConcernPersonService.saveBatch(concernPersonList);
		}

		/* 维护 案件承办人员信息 先删除旧记录 添加新纪录 */
		if(!isFirst){
			caseUndertakePersonService.deleteByCase(lawCase.getId());
		}
		List<CaseUndertakePerson> undertakePersonList = lawCaseVo.getUndertakePersonList();
		if(undertakePersonList != null && undertakePersonList.size() > 0){
			List<CaseUndertakePerson> undertakeList = undertakePersonList.stream().filter(obj -> StringUtils.isNotBlank(obj.getName())).collect(Collectors.toList());
			if (undertakeList.size() > 0) {
				for (CaseUndertakePerson undertakePerson : undertakeList) {
					undertakePerson.setLawCase(lawCase);
					undertakePerson.preInsert();
				}
				caseUndertakePersonService.saveBatch(undertakeList);
			}

		}

		/* 维护案件-案件关联信息 先删除旧记录 添加新纪录 */
		if(!isFirst){
			caseRelationService.deleteByCase(lawCase.getId());
		}
		List<CaseCaseRelation> caseRelationsList = lawCaseVo.getCaseRelationsList();
		if(caseRelationsList != null && caseRelationsList.size() > 0){
			List<CaseCaseRelation> relationsList = caseRelationsList.stream().filter(obj -> obj.getRelationCase() != null && StringUtils.isNotBlank(obj.getRelationCase().getId())).collect(Collectors.toList());
			if(relationsList.size() > 0){
				for (CaseCaseRelation caseRelation : caseRelationsList) {
					caseRelation.setLawCase(lawCase);
					caseRelation.preInsert();
				}
				caseRelationService.saveBatch(relationsList);
			}
		}

		/* 维护案件-客户关联信息 先删除旧记录 添加新纪录 */
		if(!isFirst){
			customerRelationService.deleteByCase(lawCase.getId());
		}
		List<CaseCustomerRelation> customerRelationList = lawCaseVo.getCustomerRelationList();
		if(customerRelationList != null && customerRelationList.size() > 0){
			List<CaseCustomerRelation> relationList = customerRelationList.stream().filter(obj -> obj.getCustomer() != null && StringUtils.isNotBlank(obj.getCustomer().getId())).collect(Collectors.toList());
			if (relationList.size() > 0){
				for (CaseCustomerRelation customerRelation : relationList) {
					customerRelation.setLawCase(lawCase);
					customerRelation.preInsert();
				}
				customerRelationService.saveBatch(relationList);
			}
		}

		return lawCase;
	}

	@Transactional(readOnly = false)
	public void delete(Case lawCase) {
		super.delete(lawCase);
	}

	/**
	 * 删除案件所有关联信息
	 * @param lawCase
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteAllRelation(Case lawCase) {
		if(lawCase != null && StringUtils.isNotBlank(lawCase.getId())){
			// 删除所有关联信息
			mapper.deleteAllRelation(lawCase.getId());
			// 删除剩余待办事项 及其文件
			todoInfoService.deleteAllDataByRelevance(CaseConstant.TODO_RELEVANCE_TYPE_CASE, lawCase.getId());
			// 删除 案件文档
			CaseFileDirectory rootDirectory = caseFileDirectoryService.getRootByCase(lawCase.getId());
			caseFileDirectoryService.delete(rootDirectory);
		}
	}

}