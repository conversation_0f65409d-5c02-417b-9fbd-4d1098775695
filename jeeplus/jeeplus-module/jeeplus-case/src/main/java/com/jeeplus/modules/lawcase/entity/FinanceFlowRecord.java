/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.modules.sys.entity.User;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 财务流水记录Entity
 * <AUTHOR>
 * @version 2021-10-29
 */
@Data
public class FinanceFlowRecord extends DataEntity<FinanceFlowRecord> {
	
	private static final long serialVersionUID = 1L;
	@NotBlank(message = "名称不能为空")
	@ExcelField(title="名称", align=2, sort=8)
	private String name;		// 名称
	@NotBlank(message = "类型不能为空")
	@ExcelField(title="类型", dictType = "finance_flow_type", align=2, sort=9)
	private String type;		// 类型
	@ExcelField(title="应收金额", align=2, sort=10)
	private Double receivableMoney;		// 应收金额
	@NotNull(message = "关联案件信息不能为空")
	@ExcelField(title="关联案件信息", value = "lawCase.name", align=2, sort=11)
	private Case lawCase;		// 关联案件信息名称
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="约定收款日期", align=2, sort=12)
	private Date agreedPaymentDate;		// 约定收款日期
	@NotNull(message = "请填写金额")
	@ExcelField(title="金额", align=2, sort=13)
	private Double money;		// 金额
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="收款日期/发生日期", align=2, sort=14)
	private Date happenDate;		// 收款日期/发生日期
	@ExcelField(title="开票金额", align=2, sort=15)
	private Double invoiceMoney;		// 开票金额
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="开票日期", align=2, sort=16)
	private Date invoiceDate;		// 开票日期
	@ExcelField(title="发生人", value = "happenUser.name", align=2, sort=17)
	private User happenUser;		// 发生人名称
	@ExcelField(title="报销状态", align=2, sort=18)
	private String reimbursementStatus;		// 报销状态


	// 虚拟
	private Date beginHappenDate;		// 开始 收款日期/发生日期
	private Date endHappenDate;		// 结束 收款日期/发生日期
	private List<FinanceFlowFile> fileList;	// 流水记录附件
	
	public FinanceFlowRecord() {
		super();
	}
	
	public FinanceFlowRecord(String id){
		super(id);
	}
}