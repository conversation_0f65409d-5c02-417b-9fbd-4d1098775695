/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 财产保全Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CasePropertyPreservation extends DataEntity<CasePropertyPreservation> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="案件信息", align=2, sort=1)
	private Case lawCase;		// 案件信息
	@NotNull(message = "申请人不能为空")
	@ExcelField(title="申请人", align=2, sort=2)
	private String applicant;		// 申请人
	@NotNull(message = "被申请人不能为空")
	@ExcelField(title="被申请人", align=2, sort=3)
	private String respondent;		// 被申请人
	@NotNull(message = "财产类型不能为空")
	@ExcelField(title="财产类型", dictType="property_type", align=2, sort=4)
	private String propertyType;		// 财产类型
	@ExcelField(title="裁定书编号", align=2, sort=5)
	private String rulingNumber;		// 裁定书编号
    @JsonFormat(pattern = "yyyy-MM-dd")
	@ExcelField(title="查封日期", align=2, sort=6)
	private Date seizureDate;		// 查封日期
    @JsonFormat(pattern = "yyyy-MM-dd")
	@ExcelField(title="查封到期日", align=2, sort=7)
	private Date seizureExpirationDate;		// 查封到期日
    @JsonFormat(pattern = "yyyy-MM-dd")
	@ExcelField(title="续封提醒", align=2, sort=8)
	private Date continueRemindDate;		// 续封提醒
	@ExcelField(title="提醒方式", align=2, sort=9)
	private String remindMode;		// 提醒方式
	@ExcelField(title="受理单位", align=2, sort=10)
	private String acceptUnit;		// 受理单位
	@ExcelField(title="承办人员", align=2, sort=11)
	private String undertakePerson;		// 承办人员
	@NotNull(message = "保全金额不能为空")
	@ExcelField(title="保全金额", align=2, sort=12)
	private Double preservationMoney;		// 保全金额
	@ExcelField(title="执行状态", dictType="property_execute_status", align=2, sort=13)
	private String executeStatus;		// 执行状态
	
	public CasePropertyPreservation() {
		super();
	}
	
	public CasePropertyPreservation(String id){
		super(id);
	}
}