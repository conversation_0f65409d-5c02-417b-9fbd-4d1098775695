/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.entity.StageRecord;
import com.jeeplus.modules.lawcase.mapper.StageMapper;
import com.jeeplus.modules.lawcase.util.TreeUtils;
import com.jeeplus.modules.lawcase.vo.StageRecordVO;
import com.jeeplus.modules.lawcase.vo.StageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 阶段信息Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class StageService extends CrudService<StageMapper, Stage> {

	@Autowired
	private StageRecordService stageRecordService;

	public Stage get(String id) {
		return super.get(id);
	}
	
	public List<Stage> findList(Stage stage) {
		return super.findList(stage);
	}
	
	public Page<Stage> findPage(Page<Stage> page, Stage stage) {
		return super.findPage(page, stage);
	}

	/**
	 * 根据模版id 查询阶段、阶段待办事项 信息
	 * @param stage
	 * @return
	 */
	public List<StageVO> findListByTemplate (Stage stage){
		List<StageVO> list = mapper.findListByTemplate(stage);
		for (StageVO stageVo : list) {
			List<StageRecordVO> recordList = stageVo.getStageRecordList();
			// 转为上下级结构的 List
			List<StageRecordVO> treeList = TreeUtils.getTreeData(recordList);
			stageVo.setStageRecordList(treeList);
		}
		return list;
	}

	@Transactional(readOnly = false)
	public void save(Stage stage) {
		super.save(stage);
	}

	@Transactional(readOnly = false)
	public void delete(Stage stage) {
		super.delete(stage);
		// 删除阶段记录
		if(stage.getStageTemplate() != null && StringUtils.isNotBlank(stage.getStageTemplate().getId())){
			stageRecordService.deleteByStage(new StageRecord(stage.getStageTemplate(), stage));
		}
	}

	/**
	 * 根据模版删除阶段
	 * @param templateId
	 */
	//@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByTemplate(String templateId) {
		mapper.deleteByTemplate(templateId);
	}
}