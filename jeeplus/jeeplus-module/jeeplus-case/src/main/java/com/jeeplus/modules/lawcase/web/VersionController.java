/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeesharp.org/">jeesharp</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.entity.Version;
import com.jeeplus.modules.lawcase.service.VersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 版本信息Controller
 * <AUTHOR>
 * @version 2021-10-20
 */
@RestController
@Api(tags = "版本信息")
@RequestMapping(value = "/lawcase/version")
public class VersionController extends BaseController {

	@Autowired
	private VersionService versionService;
	
	@ModelAttribute
	public Version get(@RequestParam(required=false) String id) {
		Version entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = versionService.get(id);
		}
		if (entity == null){
			entity = new Version();
		}
		return entity;
	}
	
		/**
	 * 版本信息列表数据
	 */
	@RequiresPermissions(value = {"lawcase:version:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "版本信息列表", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "当前页码", name = "pageNo", required = true),
			@ApiImplicitParam(value = "每页数量", name = "pageSize", required = true),
			@ApiImplicitParam(value = "版本号", name = "number"),
			@ApiImplicitParam(value = "是否启用  参照字典: yes_no", name = "isEnable")
	})
	@PostMapping(value = "list")
	public AjaxJson list(Version version, HttpServletRequest request, HttpServletResponse response) {
		Page<Version> page = new Page<Version>(request, response);
		page = versionService.findPage(page, version);
		return AjaxJson.success().put("page", page);
	}

	@ApiOperation(value = "版本信息详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(Version version) {
		return AjaxJson.success().put("version", version);
	}

	/**
	 * 保存版本信息
	 */
	@RequiresPermissions(value={"lawcase:version:add","lawcase:version:edit", "user"},logical= Logical.OR)
	@ApiOperation(value = "保存版本信息", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id"),
			@ApiImplicitParam(value = "版本号", name = "number", required = true),
			@ApiImplicitParam(value = "文件路径", name = "path", required = true),
			@ApiImplicitParam(value = "发布日期", name = "publishDate", required = true),
			@ApiImplicitParam(value = "是否启用 参考字典：yes_no", name = "isEnable", required = true),
			@ApiImplicitParam(value = "内容介绍", name = "content")

	})
	@PostMapping(value = "save")
	public AjaxJson save(Version version) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(version);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(StringUtils.isBlank(version.getIsEnable())){
			version.setIsEnable(JeePlusProperites.NO);
		}

		//新增或编辑表单保存
		try {
			versionService.saveInfo(version);//保存
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}

	
	/**
	 * 批量删除版本信息
	 */
	@RequiresPermissions(value = {"app:version:del","user"}, logical = Logical.OR)
	@ApiOperation(value = "删除版本信息", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值 多条记录用, 隔开", name = "ids", required = true)
	@PostMapping(value = "delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			Version version = versionService.get(id);
			if(JeePlusProperites.YES.equals(version.getIsEnable())){
				return AjaxJson.error("不可以删除使用中的版本信息");
			}
			versionService.delete(versionService.get(id));
		}
		return AjaxJson.success("删除成功");
	}
	
}