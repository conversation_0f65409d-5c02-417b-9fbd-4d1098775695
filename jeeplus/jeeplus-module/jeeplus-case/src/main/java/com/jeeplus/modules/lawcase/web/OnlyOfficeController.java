package com.jeeplus.modules.lawcase.web;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.datasource.service.DBChangeService;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.TodoInfoFile;
import com.jeeplus.modules.lawcase.service.CaseService;
import com.jeeplus.modules.lawcase.service.TodoInfoService;
import com.jeeplus.modules.lawcase.vo.OnlyofficeVO;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.service.UserService;
import com.jeeplus.modules.sys.utils.UserUtils;

import io.swagger.annotations.Api;

@Api(tags = "onlyOffice文档接口")
@Controller
public class OnlyOfficeController {
	/**
	 * 日志对象
	 */
	protected Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private DBChangeService dbChangeService;
	@Autowired
	private TodoInfoService todoInfoService;
	@Autowired
	private CaseService caseService;
	 @Autowired
	private UserService userService;
	   @ResponseBody
	   @RequestMapping(value = "onlyoffice/fileDetail")
	    public Object fileDetail( 
	    		HttpServletRequest request,  
	    		@RequestParam("fileId") String fileId,  
	    		@RequestParam("type") String type, 
	    	     String caseId, 
	    		  String utoken) throws Exception{
		   
		   logger.info ("【onlyoffice】查看 进来了===="+fileId+"&type="+type+"&utoken="+utoken+"&caseId="+caseId);
		    if(StringUtils.isBlank(type)) {
		    	User user=UserUtils.getUser();
		    	  type=user.getDataCode();
		    	 logger.info ("【onlyoffice】查看 进来了2===="+fileId+"&type="+type);
		    }
			dbChangeService.changeDb(type);
		   JSONObject jsonObject = new JSONObject();
	        jsonObject.put("status", 1);
	        JSONObject data = new JSONObject();
	     
	        TodoInfoFile info=   todoInfoService.getFileInfoByFid(fileId);
	        if(info==null) {
	        	 jsonObject.put("status", 0);
	        	 data.put("msg", "文件不存在");
	             jsonObject.put("data", data);
	             return jsonObject.toString();
	        }
	       
	        data.put("fileUrl", info.getFullPath());
	        data.put("fileName", info.getName());
	        if(info.getUpdateDate()!=null) {
	        	 data.put("fileKey", info.getId()+info.getUpdateDate().getTime());
	        }else {
	        	 data.put("fileKey", info.getId());
	        }
	        data.put("watermarkFlag", 0);
	        data.put("watermarkStr", "");
	        
	        String curDate=DateUtils.getDateTime();
	        
	        if(StringUtils.isNoneBlank(caseId)) {
	        	//查询案件
		        Case caseInfo=caseService.get(caseId);
		        if(caseInfo!=null) {
		        	if(caseInfo.getIsWatermark()==1) {
		        		//查询用户是否
		        		 if(StringUtils.isNotBlank(utoken) && !caseInfo.getCreateBy().getId().equals(utoken)) {
		        			 data.put("watermarkFlag", 1);
		   	  	             data.put("watermarkStr",userService.get(utoken).getName()+" "+curDate +" "+caseInfo.getNumber());
		        		 }
		        			
		        	} 
		        }else {
		        	  data.put("watermarkFlag", 1);
		  	          data.put("watermarkStr", "律管云 "+curDate);
		        }	
	        }else {
	        	  data.put("watermarkFlag", 1);
	  	          data.put("watermarkStr", "律管云 "+curDate);
	        }
	        
	        
	       
	        jsonObject.put("data", data);
	        
	        return jsonObject.toString();
		   
	   }
 

	// 文档修改后调用的回调接口
	@ResponseBody
    @RequestMapping(value = "onlyoffice/fileSave", method = RequestMethod.POST)
    public Object fileSave( 
    		HttpServletRequest request,  
    		@RequestBody OnlyofficeVO vo) throws Exception{
    	  JSONObject jsonObject = new JSONObject();
    	  logger.info ("【onlyoffice】保存 进来了1====fileId："+vo.getFileId()+"&fileStream:"+vo.getFileStream());
    	  if(StringUtils.isBlank(vo.getType())) {
		    	User user=UserUtils.getUser();
		    	vo.setType(user.getDataCode());
		    	 logger.info ("【onlyoffice】保存 进来了2===="+vo.getFileId()+"&type="+vo.getType());
		    }
  		  dbChangeService.changeDb(vo.getType());
          jsonObject.put("status", 1);
          JSONObject data = new JSONObject();
    	  TodoInfoFile info=   todoInfoService.getFileInfoByFid(vo.getFileId());
	        if(info==null) {
	        	 jsonObject.put("status", 0);
	        	 data.put("msg", "文件不存在");
	             jsonObject.put("data", data);
	        }
	        
            URL url = new URL(vo.getFileStream());
            
            InputStream fis = url.openConnection().getInputStream();
			
	        
	    String  filename=info.getPath();
    	
        String file1 = JeePlusProperites.newInstance().getUserfilesBaseDir() +filename;

        File f = new File(file1);
        try {
            BufferedOutputStream out = new BufferedOutputStream(
                    new FileOutputStream(f));
            
            byte[] buffer = new byte[5120];
			int r = 0;
			while ((r = fis.read(buffer)) != -1) {
				out.write(buffer, 0, r);
			}
            out.flush();
            out.close();
            info.setUpdateDate(new Date());
            if(info.getEditFlag()!=2) {
            	info.setEditFlag(1);
            }
            
            todoInfoService.updateInfoDate(info);
        } catch (Exception e) {
        	   logger.error ("【onlyoffice】保存 进来了====fileId："+vo.getFileId(),e);
            e.printStackTrace();
        }finally {
        	fis.close();
		}

      
        String name = filename.substring(filename.lastIndexOf("/")+1);
        data.put("name", name);
        jsonObject.put("data", data);

        return jsonObject.toString();
    }
}
