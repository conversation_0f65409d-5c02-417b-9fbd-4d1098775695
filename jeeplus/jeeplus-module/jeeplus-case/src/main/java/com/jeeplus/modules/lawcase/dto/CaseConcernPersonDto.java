/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.dto;


import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseCause;
import com.jeeplus.modules.lawcase.entity.CaseProgram;
import com.jeeplus.modules.sys.entity.User;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 案件当事人Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseConcernPersonDto extends DataEntity<CaseConcernPersonDto> {

	private static final long serialVersionUID = 1L;

	@ExcelField(title="类型（个人、单位）", dictType="customer_type", align=2, sort=8)
	private String type;		// 类型（个人、单位）
	@ExcelField(title="委托方（是/否）", dictType = "yes_no", align=2, sort=9)
	private String isEntrust;		// 委托方（是/否）
	@ExcelField(title="姓名/单位名称", align=2, sort=10)
	private String name;		// 姓名/单位名称
	@ExcelField(title="属性", align=2, sort=11)
	private String attribute;		// 属性
	@ExcelField(title="民族", align=2, sort=12)
	private String nation;		// 民族
//	@ExcelField(title="性别", align=2, sort=13)
	private String sex;		// 性别
	@ExcelField(title="联系方式", align=2, sort=14)
	private String phone;		// 联系方式
	@Length(max = 18)
	@ExcelField(title="证件号码", align=2, sort=15)
	private String idNumber;		// 证件号码
	@ExcelField(title="住所地/单位地址", align=2, sort=16)
	private String address;		// 住所地/单位地址
//	@ExcelField(title="法定代表人", align=2, sort=17)
	private String legalRepresentative;		// 法定代表人
//	@ExcelField(title="统一社会信用代码", align=2, sort=18)
	private String unifiedSocialCreditCode;		// 统一社会信用代码

	private String lawCaseId;		// 关联案件id
	@ExcelField(title="关联案件", align=2, sort=20)
	private String lawCaseName;		// 关联案件名
	@ExcelField(title="案件程序", value = "caseProgram.name", align=2, sort=22)
	private CaseProgram caseProgram;		// 案件程序
	@ExcelField(title="案由", value = "caseCause.name", align=2, sort=23)
	private CaseCause caseCause;		// 案由
	@ExcelField(title="主办人", value = "hostUser.name", align=2, sort=25)
	private User hostUser;		// 主办人
	@ExcelField(title="承办人", align=2, sort=26)
	private String undertakePersonNames;		// 承办人

	public CaseConcernPersonDto() {
		super();
	}

	public CaseConcernPersonDto(String id){
		super(id);
	}
}