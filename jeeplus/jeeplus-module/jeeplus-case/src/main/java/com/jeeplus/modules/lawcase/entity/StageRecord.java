/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.jeeplus.core.persistence.TreeEntity;
import lombok.Data;

/**
 * 阶段记录Entity
 * <AUTHOR>
 * @version 2021-08-12
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class StageRecord extends TreeEntity<StageRecord> {
	
	private static final long serialVersionUID = 1L;
	private StageTemplate stageTemplate;		// 模版id
	private Stage stage;		// 阶段id
	private String type;		// 类型
	private String content;		// 内容/详情
	private String isNotAuditProhibit; // 是否未审核期间禁止编辑 默认：否	2022-03-05 新增
	private Integer nums;
	
	public StageRecord() {
		super();
	}

	public StageRecord(StageTemplate stageTemplate, Stage stage) {
		this.stageTemplate = stageTemplate;
		this.stage = stage;
	}

	public StageRecord(String id){
		super(id);
	}

	public  StageRecord getParent() {
			return parent;
	}
	
	@Override
	public void setParent(StageRecord parent) {
		this.parent = parent;
		
	}
	
	public String getParentId() {
		return parent != null && parent.getId() != null ? parent.getId() : "0";
	}
}