package com.jeeplus.modules.lawcase.util;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jeeplus.modules.lawcase.constant.QiNiuCnst;
import com.jeeplus.modules.lawcase.vo.DeepseekVO;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class DeepseekUtil {
	
	public static String url="https://api.deepseek.com/chat/completions";
	public static String key="Bearer sk-2af78a8aef1548b68b200d69ee3d54be";
	
	public static void main(String[] args) {
		System.out.println(
				getContent("案件类型有哪些？",false,2).getContent());
	}
	
	//
	public static DeepseekVO getContent(String content,boolean stream,Integer type) {
		  DeepseekVO vo=new DeepseekVO();
		OkHttpClient client = new OkHttpClient().newBuilder()
		        .readTimeout(60, TimeUnit.SECONDS) // 设置读取超时
		        .connectTimeout(60, TimeUnit.SECONDS) // 设置连接超时 
		        .build();;
		    MediaType JSON = MediaType.get("application/json; charset=utf-8"); // 在JSON数据中使用
		  
		    JSONObject sys=new JSONObject();
		    sys.put("role", "system");
		    sys.put("content", "您是法律专家.");
		    
		    JSONObject message=new JSONObject();
		    message.put("role", "user");
		    message.put("content", content);
		    JSONArray ms=new JSONArray();
		   // ms.add(sys);
		    ms.add(message);
		    String ttype="deepseek-chat";
		    if(type!=null && type==1) {
		    	ttype="deepseek-reasoner";
		    }
		    String param="{\"model\":\""+ttype+"\",\"stream\":"+stream+",\"messages\":"+ms.toJSONString()+"}";
		    RequestBody body = RequestBody.create(JSON, param
		    		); // 示例JSON数据
		    Request request = new Request.Builder()
		        .url(url) // 目标URL
		        .post(body) // 使用POST方法并设置请求体
		        .addHeader("Authorization", key)
		        // 添加请求头，例如认证令牌
		        .build();
		    try (Response response = client.newCall(request).execute()) { // 使用try-with-resources确保响应被正确关闭
	            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
	            // 处理响应，例如读取响应体内容：
	            if(!stream) {
	            	  JSONObject  rs= JSONObject.parseObject(response.body().string());
	  	            vo.setContent(rs.getJSONArray("choices").getJSONObject(0)
	  	            		.getJSONObject("message").getString("content"));
	  	            if(type!=null && type==1) {
	  	                vo.setReasoningContent(rs.getJSONArray("choices").getJSONObject(0)
	  		            		.getJSONObject("message").getString("reasoning_content"));
	  	            }
	            }else {
	            	
	            }
	          
	            return vo;
	        } catch (IOException e) {
	            e.printStackTrace(); // 处理异常，例如网络错误等。
	        }
		    
		    vo.setContent("系统繁忙请稍后再试");
		return vo;
	}
 
	//获取apkey sk-b6f96665b92a524bf149ff53d74e074b3ca38b56b278c0544ae82b8174995a1f
	public static String getAppKey(){
		OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(QiNiuCnst.app_key_url).header("Authorization", QiNiuCnst.ak)
                .build();
 
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
            	return JSONObject.parseObject(response.body().string()).getString("api_key");
            } else {
                System.err.println("Request failed: " + response);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
	}
	
	

 
}
