/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;

import com.jeeplus.modules.lawcase.entity.TodoInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 待办事项 关联 VO
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class TodoInfoRelevanceVO implements Serializable {

	private static final long serialVersionUID = 1L;

	private String relevanceType;		// 关联类型
	private String relevanceId;		// 关联信息

	private List<TodoInfo> todoInfoList;	// 待办事项数据


	public TodoInfoRelevanceVO() {
		super();
	}

}