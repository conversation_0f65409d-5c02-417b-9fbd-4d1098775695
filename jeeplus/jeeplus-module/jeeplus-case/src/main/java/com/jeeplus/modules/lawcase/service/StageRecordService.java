/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.service.TreeService;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.StageRecord;
import com.jeeplus.modules.lawcase.entity.StageTemplate;
import com.jeeplus.modules.lawcase.mapper.StageRecordMapper;

/**
 * 阶段记录Service
 * <AUTHOR>
 * @version 2021-08-12
 */
@Service
@Transactional(readOnly = true)
public class StageRecordService extends TreeService<StageRecordMapper, StageRecord> {

	public StageRecord get(String id) {
		return super.get(id);
	}

	public List<StageRecord> findList(StageRecord stageRecord) {
		if (StringUtils.isNotBlank(stageRecord.getParentIds())){
			stageRecord.setParentIds(","+stageRecord.getParentIds()+",");
		}
		return super.findList(stageRecord);
	}

	public void save(StageRecord stageRecord) {
		super.save(stageRecord);
		
	}

	@Transactional(readOnly = false)
	public void delete(StageRecord stageRecord) {
		super.delete(stageRecord);
	}

	/**
	 * 根据模版、阶段 删除阶段记录
	 * @param stageRecord
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByStage(StageRecord stageRecord){
		mapper.deleteByStage(stageRecord);
	}

	/**
	 * 根据模版删除待办事项
	 * @param templateId
	 */
	//@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByTemplate(String templateId) {
		mapper.deleteByTemplate(templateId);
	}
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void update(StageRecord stageRecord) {
		mapper.update(stageRecord);
		
	}
 
}