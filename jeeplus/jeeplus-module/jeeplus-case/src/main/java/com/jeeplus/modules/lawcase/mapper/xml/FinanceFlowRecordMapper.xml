<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.FinanceFlowRecordMapper">

	<sql id="financeFlowRecordColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",

		a.name AS "name",
		a.type AS "type",
		a.receivable_money AS "receivableMoney",
		a.case_id AS "lawCase.id",
		a.case_name AS "lawCase.name",
		a.agreed_payment_date AS "agreedPaymentDate",
		a.money AS "money",
		a.happen_date AS "happenDate",
		a.invoice_money AS "invoiceMoney",
		a.invoice_date AS "invoiceDate",
		a.happen_user_id AS "happenUser.id",
		a.happen_user_name AS "happenUser.name",
		a.reimbursement_status AS "reimbursementStatus"
	</sql>

	<sql id="financeFlowRecordJoins">

	</sql>


	<select id="get" resultType="FinanceFlowRecord" >
		SELECT
			<include refid="financeFlowRecordColumns"/>
		FROM law_finance_flow_record a
		<include refid="financeFlowRecordJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="FinanceFlowRecord" >
		SELECT
			<include refid="financeFlowRecordColumns"/>
		FROM law_finance_flow_record a
		<include refid="financeFlowRecordJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="createBy != null and createBy.id != null and createBy.id != ''">
				AND a.create_by = #{createBy.id}
			</if>
			<if test="beginHappenDate != null">
				AND DATE(a.happen_date) <![CDATA[  >=  ]]> DATE(#{beginHappenDate})
			</if>
			<if test="endHappenDate != null ">
				AND DATE(a.happen_date) <![CDATA[  <=  ]]>  DATE(#{endHappenDate})
			</if>
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
			<if test="type != null and type != null ">
				AND a.type = #{type}
			</if>
			<if test="happenUser != null and happenUser.id != null and happenUser.id != ''">
				AND a.happen_user_id = #{happenUser.id}
			</if>
			<if test="happenUser != null and happenUser.name != null and happenUser.name != ''">
				AND a.happen_user_name LIKE concat('%',#{happenUser.name},'%') 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="getCaseFinance" resultType="CaseFinanceVO" >
		SELECT a.id AS "lawCase.id", a.name AS "lawCase.name", a.contract_money AS "lawCase.contractMoney", a.entrust_date AS "lawCase.entrustDate"
		,a.hostUserId AS "lawCase.hostUser.id", a.hostUserName AS "lawCase.hostUser.name"
		,IFNULL(fin.receiveMoney, 0) AS "receiveMoney", IFNULL(fin.expenditureMoney, 0.0) AS "expenditureMoney"
		FROM (
			SELECT a.id, a.`name`, a.contract_money, a.entrust_date, a.create_date
			,a.host_user_id AS "hostUserId", hostUser.name AS "hostUserName"
			FROM law_case a
			LEFT JOIN sys_user hostUser ON hostUser.id = a.host_user_id
			WHERE a.del_flag = '0' AND a.id = #{caseId}
		) a
		LEFT JOIN(
			SELECT a.case_id, SUM(CASE WHEN a.type = '1' THEN IFNULL(a.money, 0) ELSE 0 END) AS "receiveMoney"
			,SUM(CASE WHEN a.type = '2' THEN IFNULL(a.money, 0) ELSE 0 END) AS "expenditureMoney"
			FROM law_finance_flow_record a
			WHERE a.case_id = #{caseId}
			GROUP BY a.case_id
		) fin ON a.id = fin.case_id
	</select>

	<select id="findCaseFinanceList" resultType="CaseFinanceVO" >
		SELECT a.id AS "lawCase.id", a.name AS "lawCase.name", a.contract_money AS "lawCase.contractMoney", a.entrust_date AS "lawCase.entrustDate"
			,a.hostUserId AS "lawCase.hostUser.id", a.hostUserName AS "lawCase.hostUser.name"
			,IFNULL(fin.receiveMoney, 0) AS "receiveMoney", IFNULL(fin.expenditureMoney, 0.0) AS "expenditureMoney"
		FROM (
			SELECT a.id, a.`name`, a.contract_money, a.entrust_date, a.create_date
				,a.host_user_id AS "hostUserId", hostUser.name AS "hostUserName"
			FROM law_case a
			LEFT JOIN sys_user hostUser ON hostUser.id = a.host_user_id
			<where>
				a.del_flag = #{DEL_FLAG_NORMAL}
				${dataScope}
				<if test="lawCase != null and lawCase.type != null and lawCase.type != ''">
					AND a.type = #{lawCase.type}
				</if>
				<if test="lawCase != null and lawCase.hostUser != null and lawCase.hostUser.id != null and lawCase.hostUser.id != ''">
					AND a.host_user_id = #{lawCase.hostUser.id}
				</if>
				<if test="lawCase != null and lawCase.name != null and lawCase.name != ''">
					AND a.name LIKE concat('%',#{lawCase.name},'%')
				</if>
			</where>
		) a
		LEFT JOIN(
			SELECT a.case_id, SUM(CASE WHEN a.type = '1' THEN IFNULL(a.money, 0) ELSE 0 END) AS "receiveMoney"
				,SUM(CASE WHEN a.type = '2' THEN IFNULL(a.money, 0) ELSE 0 END) AS "expenditureMoney"
			FROM law_finance_flow_record a
			GROUP BY a.case_id
		) fin ON a.id = fin.case_id
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.entrust_date DESC, a.create_date
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="FinanceFlowRecord" >
		SELECT
			<include refid="financeFlowRecordColumns"/>
		FROM law_finance_flow_record a
		<include refid="financeFlowRecordJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_finance_flow_record(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,

			`name`,
			`type`,
			receivable_money,
			case_id,
			case_name,
			agreed_payment_date,
			money,
			happen_date,
			invoice_money,
			invoice_date,
			happen_user_id,
			happen_user_name,
			reimbursement_status
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},

			#{name},
			#{type},
			#{receivableMoney},
			#{lawCase.id},
			#{lawCase.name},
			#{agreedPaymentDate},
			#{money},
			#{happenDate},
			#{invoiceMoney},
			#{invoiceDate},
			#{happenUser.id},
			#{happenUser.name},
			#{reimbursementStatus}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_finance_flow_record(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,

			`name`,
			`type`,
			receivable_money,
			case_id,
			case_name,
			agreed_payment_date,
			money,
			happen_date,
			invoice_money,
			invoice_date,
			happen_user_id,
			happen_user_name,
			reimbursement_status
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},

			#{item.name},
			#{item.type},
			#{item.receivableMoney},
			#{item.lawCase.id},
			#{item.lawCase.name},
			#{item.agreedPaymentDate},
			#{item.money},
			#{item.happenDate},
			#{item.invoiceMoney},
			#{item.invoiceDate},
			#{item.happenUser.id},
			#{item.happenUser.name},
			#{item.reimbursementStatus}
			)
		</foreach>
	</insert>

	<update id="update">
		UPDATE law_finance_flow_record SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},

			`name` = #{name},
			`type` = #{type},
			receivable_money = #{receivableMoney},
			case_id = #{lawCase.id},
			case_name = #{lawCase.name},
			agreed_payment_date = #{agreedPaymentDate},
			money = #{money},
			happen_date = #{happenDate},
			invoice_money = #{invoiceMoney},
			invoice_date = #{invoiceDate},
			happen_user_id = #{happenUser.id},
			happen_user_name = #{happenUser.name},
			reimbursement_status = #{reimbursementStatus}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_finance_flow_record
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_finance_flow_record SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="FinanceFlowRecord">
		select * FROM law_finance_flow_record  where ${propertyName} = #{value}
	</select>

</mapper>