<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CustomerContactsMapper">

	<sql id="customerContactsColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.customer_id AS "customer.id",
		a.name AS "name",
		a.sex AS "sex",
		a.email AS "email",
		a.phone AS "phone",
		a.post AS "post",
		a.address AS "address"
	</sql>

	<sql id="customerContactsJoins">

	</sql>


	<select id="get" resultType="CustomerContacts" >
		SELECT
			<include refid="customerContactsColumns"/>
		FROM law_customer_contacts a
		<include refid="customerContactsJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CustomerContacts" >
		SELECT
			<include refid="customerContactsColumns"/>
		FROM law_customer_contacts a
		<include refid="customerContactsJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="customer != null and customer.id != null and customer.id != ''">
				AND a.customer_id = #{customer.id}
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%') 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CustomerContacts" >
		SELECT
			<include refid="customerContactsColumns"/>
		FROM law_customer_contacts a
		<include refid="customerContactsJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_customer_contacts(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			customer_id,
			`name`,
			sex,
			email,
			phone,
			post,
			address
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{customer.id},
			#{name},
			#{sex},
			#{email},
			#{phone},
			#{post},
			#{address}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_customer_contacts(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			customer_id,
			`name`,
			sex,
			email,
			phone,
			post,
			address
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.customer.id},
			#{item.name},
			#{item.sex},
			#{item.email},
			#{item.phone},
			#{item.post},
			#{item.address}
			)
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_customer_contacts SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			customer_id = #{customer.id},
			`name` = #{name},
			sex = #{sex},
			email = #{email},
			phone = #{phone},
			post = #{post},
			address = #{address}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_customer_contacts
		WHERE id = #{id}
	</update>

	<update id="deleteByCustomer">
		DELETE FROM law_customer_contacts
		WHERE customer_id = #{customerId}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_customer_contacts SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CustomerContacts">
		select * FROM law_customer_contacts  where ${propertyName} = #{value}
	</select>

</mapper>