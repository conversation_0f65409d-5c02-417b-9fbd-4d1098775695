/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeesharp.org/">jeesharp</a> All rights reserved.
 */
package com.jeeplus.modules.api.lawcase.web;

import com.google.common.collect.Maps;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseStage;
import com.jeeplus.modules.lawcase.entity.TodoInfo;
import com.jeeplus.modules.lawcase.service.CaseStageService;
import com.jeeplus.modules.lawcase.service.TodoInfoService;
import com.jeeplus.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * 版本信息Controller
 * <AUTHOR>
 * @version 2021-10-20
 */
@RestController
@Api(tags = "待办事项-移动端")
@RequestMapping(value = "/app/lawcase/todoInfo")
public class TodoInfoApiController extends BaseController {

	@Autowired
	private TodoInfoService todoInfoService;
	@Autowired
	private CaseStageService caseStageService;



	@RequiresPermissions(value = {"lawcase:todoInfo:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "待办事项列表-案件", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "relevanceId", required = true),
			@ApiImplicitParam(value = "阶段id", name = "stage.id"),
			@ApiImplicitParam(value = "上级id 默认为空（根目录）", name = "parent.id")
	})
	@PostMapping("caseData")
	public AjaxJson caseList(TodoInfo todoInfo) {
		List<TodoInfo> list = new ArrayList<>();
		if(StringUtils.isNotBlank(todoInfo.getRelevanceId())){
			todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CASE);
			if(todoInfo.getParent() == null || StringUtils.isBlank(todoInfo.getParent().getId())){
				todoInfo.setParent( new TodoInfo("0") );
			}
			list = todoInfoService.findList(todoInfo);
		}
		return AjaxJson.success().put("list", list);
	}

	@RequiresPermissions(value = {"lawcase:todoInfo:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "待办事项树结构列表-案件", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "relevanceId", required = true),
			@ApiImplicitParam(value = "阶段id", name = "stage.id")
	})
	@PostMapping("caseTreeData")
	public AjaxJson caseTreeData(TodoInfo todoInfo) {
		// 查询获取 案件阶段 数据
		List<CaseStage> caseStageList = new ArrayList<>();
		if(StringUtils.isNotBlank(todoInfo.getRelevanceId())){
			CaseStage stage = todoInfo.getStage();
			if(stage == null || StringUtils.isBlank(stage.getId())){
				stage = new CaseStage();
				stage.setLawCase(new Case(todoInfo.getRelevanceId()));
				caseStageList = caseStageService.findList(stage);
			}else {
				stage = caseStageService.get(stage);
				if(stage != null && StringUtils.isNotBlank(stage.getId())){
					caseStageList.add(stage);
				}
			}
		}

		List<TodoInfo> treeList = new ArrayList<>();
		// 判断 案件阶段 数据，根据待办事项数据将 案件阶段封装为 待办事项最顶层 树结构
		if (caseStageList != null && caseStageList.size() > 0) {
			todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CASE);
			todoInfo.setCheckWord( TodoInfo.CHECK_WORD_FILE );
			List<TodoInfo> todoList = todoInfoService.findTreeList(todoInfo);
			if (todoList == null) { todoList = new ArrayList<>(); }
			Map<String, List<TodoInfo>> todoMap = todoList.stream().filter(obj -> obj.getStage() != null && StringUtils.isNotBlank(obj.getStage().getId())).collect(
					Collectors.toMap(obj -> obj.getStage().getId(), obj -> new ArrayList<TodoInfo>(){{ this.add(obj); }}
						,(oldList, newList) -> new ArrayList<TodoInfo>(){{ this.addAll(oldList); this.addAll(newList); }})
			);
			// 将案件阶段 封装为 待办事项最顶层 树结构
			for (CaseStage caseStage : caseStageList) {
				List<TodoInfo> todoInfoList = todoMap.get(caseStage.getId());
				if (todoInfoList == null) { todoInfoList = new ArrayList<>(); }

				TodoInfo ti = new TodoInfo();
				ti.setName(caseStage.getName());
				ti.setId(caseStage.getId());
				ti.setChildren(todoInfoList);
				treeList.add(ti);
			}
		}
		return AjaxJson.success().put("list", treeList);
	}


	@ApiOperation(value = "月视图数据", consumes = "application/form-data")
	@RequiresPermissions(value = {"lawcase:todoInfo:list","user"}, logical = Logical.OR)
	@ApiImplicitParams({
			@ApiImplicitParam(value = "月份", name = "month", paramType = "String", required = true)
	})
	@PostMapping("monthList")
	public AjaxJson monthList(String month) {
		Map<String, List<TodoInfo>> map = Maps.newHashMap();

		// 判断月份字符串。并拆分重组  转换为日期进行查询
		if(StringUtils.isNotBlank(month) && month.contains("-")){
			String[] arr = month.split("-");
			if(arr.length > 1){
				String dateStr = (arr[0] +"-"+ arr[1] +"-01");

				Date startDate = DateUtils.parseDate(dateStr);
				Date endDate = getEndDate(startDate, "month");
				// 查询待办事项
				List<TodoInfo> todoInfoList = findListByDateRange(startDate, endDate);
				// 将待办事项 转为 map
				if(todoInfoList != null && todoInfoList.size() > 0){
					map = todoInfoList.stream().collect( Collectors.toMap(obj -> DateUtils.formatDate(obj.getRemindDate())
							, obj -> new ArrayList<TodoInfo>(){{ this.add(obj); }}
							, (oldList, newList) -> {
								List<TodoInfo> list = new ArrayList<TodoInfo>(oldList);
								list.addAll(newList);
								return list;
							})
					);
				}

			}
		}
		return AjaxJson.success().put("data", map);
	}

	@ApiOperation(value = "日视图数据", consumes = "application/form-data")
	@RequiresPermissions(value = {"lawcase:todoInfo:list","user"}, logical = Logical.OR)
	@ApiImplicitParams({
			@ApiImplicitParam(value = "日期", name = "day", paramType = "String", required = true)
	})
	@PostMapping("dayList")
	public AjaxJson dayList(String day) {
		List<TodoInfo> todoInfoList = new ArrayList<>();
		if(StringUtils.isNotBlank(day)){
			Date dayDate = DateUtils.parseDate(day);

			Date startDate = getStartDate(dayDate, "");
			Date endDate = getEndDate(dayDate, "");

			todoInfoList = findListByDateRange(startDate, endDate);
		}

		return AjaxJson.success().put("data", todoInfoList);
	}

	/**
	 * 根据时间范围 查找待办事项 数据
	 *
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	private List<TodoInfo> findListByDateRange(Date startDate, Date endDate){
		List<TodoInfo> todoInfoList = new ArrayList<>();
		if(startDate != null && endDate != null && endDate.after(startDate)){
			TodoInfo todoInfo = new TodoInfo();
			todoInfo.setQueryStartDate(startDate);
			todoInfo.setQueryEndDate(endDate);
			// 未完成的任务
			todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
			todoInfo.setHostUser(UserUtils.getUser());
			todoInfoList = todoInfoService.findListByMonth(todoInfo);
		}
		return todoInfoList;
	}


	// 获取开始日期
	private Date getStartDate(Date date, String type) {
		if(date == null){
			return  null;
		}

		Calendar c = Calendar.getInstance();
		c.setTime(date);
		if("month".equals(type)){
			//设置为1号,当前日期既为本月第一天
			c.set(Calendar.DAY_OF_MONTH, 1);
		}
		//将小时至0
		c.set(Calendar.HOUR_OF_DAY, 0);
		//将分钟至0
		c.set(Calendar.MINUTE, 0);
		//将秒至0
		c.set(Calendar.SECOND,0);
		//将毫秒至0
		c.set(Calendar.MILLISECOND, 0);
		return c.getTime();
	}

	// 获取结束时间
	private Date getEndDate(Date date, String type) {
		if(date == null){
			return  null;
		}

		Calendar c = Calendar.getInstance();
		c.setTime(date);
		if("month".equals(type)){
			//设置为当月最后一天
			c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
		}
		//将小时至23
		c.set(Calendar.HOUR_OF_DAY, 23);
		//将分钟至59
		c.set(Calendar.MINUTE, 59);
		//将秒至59
		c.set(Calendar.SECOND, 59);
		//将毫秒至999
		c.set(Calendar.MILLISECOND, 999);
		return c.getTime();
	}

}