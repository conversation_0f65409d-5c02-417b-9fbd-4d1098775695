/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeesharp.org/">jeesharp</a> All rights reserved.
 */
package com.jeeplus.modules.api.lawcase.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.entity.Version;
import com.jeeplus.modules.lawcase.service.VersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 版本信息Controller
 * <AUTHOR>
 * @version 2021-10-20
 */
@RestController
@Api(tags = "版本信息-移动端")
@RequestMapping(value = "/app/lawcase/version")
public class VersionApiController extends BaseController {

	@Autowired
	private VersionService versionService;
	
	@ApiOperation(value = "版本信息详情", consumes = "application/form-data")
	@GetMapping("info")
	public AjaxJson info() {
		Version version = new Version();
		version.setIsEnable(JeePlusProperites.YES);
		List<Version> list = versionService.findList(version);
		if(list != null && list.size() > 0){
			version = list.get(0);
		}else {
			version = new Version();
		}
		return AjaxJson.success().put("version", version);
	}


}