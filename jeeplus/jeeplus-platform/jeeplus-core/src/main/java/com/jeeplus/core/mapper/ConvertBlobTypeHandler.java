package com.jeeplus.core.mapper;


import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.sql.*;

/**
 * className:ConvertBlobTypeHandler
 * 
 * 自定义typehandler，解决mybatis存储blob字段后，出现乱码的问题
 * 配置mapper.xml：
 * <result  typeHandler="cn.ffcs.drive.common.util.ConvertBlobTypeHandler"/>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2016-05-05 11:15:23
 * 
 */
@MappedJdbcTypes(JdbcType.BLOB)
public class ConvertBlobTypeHandler extends BaseTypeHandler<String> {//指定字符集
    private static final String DEFAULT_CHARSET = "utf-8";

@Override
public void setNonNullParameter(PreparedStatement ps, int i,
        String parameter, JdbcType jdbcType) throws SQLException {
    ByteArrayInputStream bis;
    try {
        bis = new ByteArrayInputStream(parameter.getBytes(DEFAULT_CHARSET));
    } catch (UnsupportedEncodingException e) {
        throw new RuntimeException("Blob Encoding Error!");
    }
    ps.setBinaryStream(i, bis, parameter.getBytes().length);
}

@Override
public String getNullableResult(ResultSet rs, String columnName)
        throws SQLException {
    Blob blob = rs.getBlob(columnName);
    byte[] returnValue = null;
    if (null != blob) {
        returnValue = blob.getBytes(1, (int) blob.length());
        try {
            return new String(returnValue, DEFAULT_CHARSET);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Blob Encoding Error!");
        }
    }else {
        return "";
    }

}

@Override
public String getNullableResult(CallableStatement cs, int columnIndex)
        throws SQLException {
    Blob blob = cs.getBlob(columnIndex);
    byte[] returnValue = null;
    if (null != blob) {
        returnValue = blob.getBytes(1, (int) blob.length());
        try {
            return new String(returnValue, DEFAULT_CHARSET);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Blob Encoding Error!");
        }
    }else {
        return "";
    }

}

@Override
public String getNullableResult(ResultSet arg0, int arg1) throws SQLException {
	// TODO Auto-generated method stub
	return null;
}  }
