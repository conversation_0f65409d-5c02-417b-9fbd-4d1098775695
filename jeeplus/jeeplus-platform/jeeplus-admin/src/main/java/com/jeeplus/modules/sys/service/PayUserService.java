/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jeeplus.modules.datasource.config.DBContextHolder;
import com.jeeplus.modules.sys.entity.PayUser;
import com.jeeplus.modules.sys.mapper.PayUserMapper;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @version 2016-12-05
 */
@Service
public class PayUserService  {
 

	@Autowired
	private PayUserMapper payUserMapper;

	/**
	 * 获取用户
	 * @param code
	 * @return
	 */
	public PayUser getByDataCode(String code) {
	      DBContextHolder.clearDataSource();
		return payUserMapper.getByDataCode(code);
	}
	
	public void updateCurNumsById(Long id,Integer nums) {
	      DBContextHolder.clearDataSource();
		  payUserMapper.updateCurNumsById(id,nums);
	}

	public void updateAdminNameById(Long id, String adminName) {
	      DBContextHolder.clearDataSource();
		  payUserMapper.updateAdminNameById(id,adminName);
	}

	public PayUser getById(String userNo) {
		   DBContextHolder.clearDataSource();
			return payUserMapper.getById(userNo);
	}
	
 
}
