/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.web.app;

import com.google.common.collect.Maps;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.sys.entity.DictType;
import com.jeeplus.modules.sys.entity.DictValue;
import com.jeeplus.modules.sys.service.DictTypeService;
import com.jeeplus.modules.sys.utils.DictUtils;
import com.jeeplus.modules.sys.vo.DictValueVO;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典Controller
 * <AUTHOR>
 * @version 2017-05-16
 */
@RestController
@RequestMapping("/app/sys/dict")
public class AppDictController extends BaseController {

	@GetMapping("getDictMap")
	public AjaxJson getDictMap() {
		AjaxJson j = new AjaxJson();

		String[] dictTypeArr = new String[]{"case_status", "case_audit_status", "settle_case_status", "case_type", "accept_unit_type", "todo_relevance_type"
				,"todo_status", "execute_measures", "execute_status", "property_type", "property_execute_status", "open_court_type", "trial_result", "charge_mode"
				, "customer_importance", "cooperate_status", "customer_source", "customer_type", "sex", "yes_no", "todo_type", "user_type", "finance_flow_type"
		};
		// 获取字典信息
		Map<String, List<DictValueVO>> dictMap = DictUtils.getDictShortMap();

		Map<String, List<DictValueVO>> map = Maps.newHashMap();
		for (String type : dictTypeArr) {
			List<DictValueVO> list = dictMap.get(type);
			if(list == null){
				list = new ArrayList<DictValueVO>();
			}
			map.put(type, list);
		}

		j.put("dictList", map);
		return j;
	}


}
