<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.mapper.PayUserSonMapper">

	<resultMap id="userPaySonResult" type="PayUserSon">
		  <result property="id"    column="id"    />
        <result property="mobile"    column="mobile"    />
        <result property="pwd"    column="pwd"    />
        <result property="puid"    column="puid"    />
         <result property="type"    column="type"    />
        <result property="status"    column="status"    />
          <result property="userName"    column="userName"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dataCode"    column="dataCode"    />
	</resultMap>

	<sql id="userColumns">
		a.id,
		a.status ,
		a.mobile,
		a.puid,
		a.pwd,
		a.type,
		a.user_name AS "userName",
    	a.data_code AS "dataCode"  
	</sql>

 <select id="getNumByMobile" resultType="int">
		SELECT count(id)
		FROM pay_user_son a  where   mobile=#{mobile} and is_del=0
	</select>
	
	
	
	
	  <select id="getByUserName" resultMap="userPaySonResult">
	SELECT
		<include refid="userColumns"/> 
		FROM pay_user_son a  where   user_name=#{userName} and is_del=0
	</select>
 
  <select id="getByMobile" resultMap="userPaySonResult">
	SELECT
		<include refid="userColumns"/> 
		FROM pay_user_son a  where   mobile=#{mobile} and is_del=0
	</select>

	<!-- 根据编号获得用户 -->
	<select id="getByDataCode" resultMap="userPaySonResult">
		SELECT
		<include refid="userColumns"/> 
		FROM pay_user_son a  where data_code=#{dcode} and mobile=#{mobile}
	</select>
    
   <insert id="insertPayUserSon">
		INSERT INTO pay_user_son(
			id,
			mobile,
			pwd,
			puid,
			type,
			status,
			`user_name`,
			`data_code`,
			`create_time` 
		) VALUES (
					 #{id},
					 #{mobile},
					 #{pwd},
					 #{puid},
					 #{type},
					 #{status},
					 #{userName}, 
					  #{dataCode}, 
					 now()
				 )
	</insert>


	<update id="delUserByMobiles">
		UPDATE pay_user_son SET
							is_del=1, update_time = now() 
		WHERE mobile =#{mobile}  and data_code=#{dcode}
	</update>
	
		<!-- 更新 -->
	<update id="updateUserNameByMobile">
		UPDATE pay_user_son SET user_name = #{userName} 
		mobile = #{mobile}, update_time = now()
		WHERE mobile = #{oldMobile} and data_code=#{dataCode}
	</update>
	
	<update id="updateDataCodeByMobile">
		UPDATE pay_user_son SET data_code = #{dataCode} 
							, update_time = now()
		WHERE mobile = #{mobile} and data_code is null
	</update>

	 
</mapper>
