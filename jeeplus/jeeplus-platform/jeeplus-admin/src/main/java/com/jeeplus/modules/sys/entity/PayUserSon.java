package com.jeeplus.modules.sys.entity;

import java.io.Serializable;

/**
 * 租户对象 pay_user
 * 
 * <AUTHOR>
 */
public class PayUserSon implements Serializable
{
    private static final long serialVersionUID = 1L;

    private Long id;

    /** 手机号 */
    private String mobile;
    /** 用户名 */
    private String userName;

    /** 密码 */
    private String pwd;

    /** 名称 */
    private Long puid;
    
    /** 状态 */
    private Integer status;

    /** 数据原编码 */
    private String dataCode;
    
    private Integer type;
    
    private String oldMobile;
     
	public String getOldMobile() {
		return oldMobile;
	}

	public void setOldMobile(String oldMobile) {
		this.oldMobile = oldMobile;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getPwd() {
		return pwd;
	}

	public void setPwd(String pwd) {
		this.pwd = pwd;
	}

	public Long getPuid() {
		return puid;
	}

	public void setPuid(Long puid) {
		this.puid = puid;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getDataCode() {
		return dataCode;
	}

	public void setDataCode(String dataCode) {
		this.dataCode = dataCode;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

     
}
