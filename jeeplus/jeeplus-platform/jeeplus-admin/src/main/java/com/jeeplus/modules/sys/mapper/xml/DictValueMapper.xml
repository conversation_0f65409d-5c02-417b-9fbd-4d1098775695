<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.mapper.DictValueMapper">

	<sql id="dictValueColumns">
		a.id AS "id",
		a.label AS "label",
		a.value AS "value",
		a.sort AS "sort",
		a.dict_type_id AS "dictType.id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.del_flag AS "delFlag"
	</sql>

	<sql id="dictValueJoins">
		LEFT JOIN sys_dict_type b ON b.id = a.dict_type_id
	</sql>


	<select id="get" resultType="DictValue" >
		SELECT
			<include refid="dictValueColumns"/>
		FROM sys_dict_value a
		<include refid="dictValueJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="DictValue" >
		SELECT
			<include refid="dictValueColumns"/>
		FROM sys_dict_value a
		<include refid="dictValueJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			<if test="label != null and label != ''">
				AND a.label LIKE concat('%',#{label},'%') 
			</if>
			<if test="value != null and value != ''">
				AND a.value = #{value}
			</if>
			<if test="dictType != null and dictType.id != null and dictType.id != ''">
				AND a.dict_type_id = #{dictType.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY (a.sort * 1) ASC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="DictValue" >
		SELECT
			<include refid="dictValueColumns"/>
		FROM sys_dict_value a
		<include refid="dictValueJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date ASC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO sys_dict_value(
			id,
			label,
			value,
			sort,
			dict_type_id,
			create_by,
			create_date,
			update_by,
			update_date,
			del_flag
		) VALUES (
			#{id},
			#{label},
			#{value},
			#{sort},
			#{dictType.id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{delFlag}
		)
	</insert>

	<update id="update">
		UPDATE sys_dict_value SET
			label = #{label},
			value = #{value},
			sort = #{sort},
			dict_type_id = #{dictType.id},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM sys_dict_value
		<choose>
			<when test="id !=null and id != ''">
				WHERE id = #{id}
			</when>
			<otherwise>
				WHERE dict_type_id = #{dictType.id}
			</otherwise>
		</choose>
	</update>

	<!--物理批量删除-->
	<delete id="batchDelete">
		delete from sys_dict_value
		where id IN
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE sys_dict_value SET
			del_flag = #{DEL_FLAG_DELETE}
		<choose>
			<when test="id !=null and id != ''">
				WHERE id = #{id}
			</when>
			<otherwise>
				WHERE dict_type_id = #{dictType.id}
			</otherwise>
		</choose>
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="DictValue">
		select * FROM sys_dict_value  where ${propertyName} = #{value}
	</select>

	<select id="getDictValueByType" resultType="DictValue">
		SELECT
		<include refid="dictValueColumns"/>
		FROM
		sys_dict_value a
		<where> a.del_flag=0 and 
			a.dict_type_id = #{dict_type_id}
			<!-- 变量名 dict_type_id 对应上文的 dict_type_id -->
			<!-- 如果上文中 collection只传一个参数column="id",只要类型匹配,在这里随便写个变量名就可以取到值 #{xyz} -->
		</where>
		ORDER BY (a.sort * 1) ASC
	</select>

</mapper>
