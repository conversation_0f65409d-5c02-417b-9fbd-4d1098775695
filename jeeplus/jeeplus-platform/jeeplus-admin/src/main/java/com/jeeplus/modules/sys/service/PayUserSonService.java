/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jeeplus.modules.datasource.config.DBContextHolder;
import com.jeeplus.modules.sys.entity.PayUserSon;
import com.jeeplus.modules.sys.mapper.PayUserSonMapper;

/**
 * 用户管理
 *
 * <AUTHOR>
 */
@Service
public class PayUserSonService  {
 

	@Autowired
	private PayUserSonMapper payUserSonMapper;

	/**
	 * 获取用户
	 * @param code
	 * @return
	 */
	public PayUserSon getByDataCode(String mobile, String code) {
	      DBContextHolder.clearDataSource();
		return payUserSonMapper.getByDataCode(mobile,code);
	}
	
	public void insertPayUserSon(PayUserSon info) {
	      DBContextHolder.clearDataSource();
	      payUserSonMapper.insertPayUserSon(info);
	}


	public Integer getNumByMobile(String mobile) {
		  DBContextHolder.clearDataSource();
	      return payUserSonMapper.getNumByMobile(mobile);
	}

	public PayUserSon getByMobile(String mobile) {
		  DBContextHolder.clearDataSource();
	      return payUserSonMapper.getByMobile(mobile);
	}

	public void delUserByMobiles(String mobile,String dataCode) {
		  DBContextHolder.clearDataSource();
	       payUserSonMapper.delUserByMobiles(mobile,dataCode);
	}

	public void updateUserNameByMobile(PayUserSon suser) {
		 DBContextHolder.clearDataSource();
	       payUserSonMapper.updateUserNameByMobile(suser);
	}

	public void updateDataCodeByMobile(PayUserSon suser) {
		 DBContextHolder.clearDataSource();
	       payUserSonMapper.updateDataCodeByMobile(suser);
	}

	public PayUserSon getByUserName(String userName) {
		DBContextHolder.clearDataSource();
	      return payUserSonMapper.getByUserName(userName);
	}
 
}
