<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.mapper.PayUserMapper">

	<resultMap id="userResult" type="PayUser">
		<id property="id" column="id" />
		<result property="status" column="status" />
		<result property="mobile" column="mobile" />
		<result property="realName" column="realName" />
		<result property="vipNums" column="vipNums" />
		<result property="curNums" column="curNums" />
		<result property="adminName" column="adminName" />
		<result property="vipEndTime" column="vipEndTime" />
			<result property="vipEndTime" column="vipEndTime" />
				<result property="dataCode" column="dataCode" />
			<result property="buyModel" column="buyModel" />
	   <result property="buyModel1" column="buyModel1" />
       <result property="buyModel2" column="buyModel2" />
       <result property="buyModel3" column="buyModel3" />
       <result property="buyModel4" column="buyModel4" />
	</resultMap>

	<sql id="userColumns">
		a.id,
		a.status ,
		a.mobile,
    	a.vip_nums AS "vipNums",
    	a.cur_nums AS "curNums", 
    	a.vip_end_time AS "vipEndTime" ,
    	a.admin_name as adminName ,
    	a.real_name as realName ,
    	a.data_code as dataCode,
    	a.buy_model as buyModel,
    	a.buy_model1 as buyModel1,
    	a.buy_model2 as buyModel2,
    	a.buy_model3 as buyModel3,
    	a.buy_model4 as buyModel4
	</sql>

 

	<!-- 根据id获得用户 -->
	<select id="getById" resultMap="userResult">
		SELECT
		<include refid="userColumns"/> 
		FROM pay_user a  where id=#{userNo} 
	</select>


	<!-- 根据编号获得用户 -->
	<select id="getByDataCode" resultMap="userResult">
		SELECT
		<include refid="userColumns"/> 
		FROM pay_user a  where data_code=#{dateCode} 
	</select>
    
	<!-- 更新用户当前人数 -->
	<update id="updateCurNumsById">
		UPDATE pay_user SET
			cur_nums = cur_nums+ #{nums},update_time=now() 
		WHERE id = #{id}
	</update>

	<update id="updateAdminNameById" >
		UPDATE pay_user SET
			admin_name =#{adminName},update_time=now() 
		WHERE id = #{id}
	</update>


	 
</mapper>
