/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.sys.entity.PayUser;

/**
 * 用户MAPPER接口
 * <AUTHOR>
 * @version 2017-05-16
 */
@Mapper
@Repository
public interface PayUserMapper extends BaseMapper<PayUser> {
 
	public PayUser getByDataCode(String code);
	
	public void updateCurNumsById(@Param("id") Long id,@Param("nums")Integer nums);

	public void updateAdminNameById(@Param("id") Long id, @Param("adminName") String adminName);

	public PayUser getById(String userNo);
	
 
}
