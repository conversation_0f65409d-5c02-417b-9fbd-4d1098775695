package com.jeeplus.modules.socketio.service;

import com.alibaba.fastjson.JSONObject;
import com.corundumstudio.socketio.*;
import com.corundumstudio.socketio.annotation.OnConnect;
import com.corundumstudio.socketio.annotation.OnDisconnect;
import com.corundumstudio.socketio.annotation.OnEvent;
import com.corundumstudio.socketio.listener.ConnectListener;
import com.corundumstudio.socketio.listener.DisconnectListener;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.socketio.utils.SocketIoUtils;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Map;

/**
 * socket.io 配置信息
 * <AUTHOR>
 * @date 2021-10-21
 */
@Component
public class SocketIoService {

    private static SocketIOServer socketIOServer;

    public SocketIoService(SocketIOServer server){
        socketIOServer = server;
    }

    @PostConstruct
    private void autoStart(){
        socketStart();
    }

    @PreDestroy
    private void autoStop(){
        socketStop();
    }

    private void socketStart(){
        socketIOServer.addConnectListener(new ConnectListener() {
            @Override
            public void onConnect(SocketIOClient client) {
                String sa = client.getRemoteAddress().toString();
                String clientIp = sa.substring(1, sa.indexOf(":"));// 获取设备ip
                System.out.println(clientIp + "-------------------------" + "客户端已连接");
                User user = UserUtils.getUser();
                String id = getParamsByClient(client);
                System.out.println(id);
                if(StringUtils.isNotBlank(id)){
                    SocketIoUtils.put(id, client);
                }
            }
        });

        socketIOServer.addDisconnectListener(new DisconnectListener() {
            @Override
            public void onDisconnect(SocketIOClient client) {
                String sa = client.getRemoteAddress().toString();
                String clientIp = sa.substring(1, sa.indexOf(":"));// 获取设备ip
                System.out.println(clientIp + "-------------------------" + "客户端已断开连接");
                String id = getParamsByClient(client);
                if(StringUtils.isNotBlank(id)){
                    SocketIoUtils.remove(id);
                    client.disconnect();    // 断开连接
                }
            }
        });

        socketIOServer.addEventListener("clientMessage", JSONObject.class, (client, data, sender) -> {
            JSONObject jo = data;
            String id = getParamsByClient(client);
            System.out.println(id);
            System.out.println("接收参数："+ jo.toJSONString());
        });

        socketIOServer.start();
        System.out.println("socket.io 服务已开启");
    }

    private void socketStop(){
        if (socketIOServer != null) {
            socketIOServer.stop();
            socketIOServer = null;
        }
        System.out.println("socket.io 服务已关闭");
    }

    @OnConnect
    public void onConnect(SocketIOClient client){
        String sa = client.getRemoteAddress().toString();
        String clientIp = sa.substring(1, sa.indexOf(":"));// 获取设备ip
        System.out.println(clientIp + "-------------------------" + "客户端已连接");
        String id = getParamsByClient(client);
        SocketIoUtils.put(id, client);
    }

    @OnDisconnect
    public void onDisconnect(SocketIOClient client){
        String sa = client.getRemoteAddress().toString();
        String clientIp = sa.substring(1, sa.indexOf(":"));// 获取设备ip
        System.out.println(clientIp + "-------------------------" + "客户端已断开连接");

        String id = getParamsByClient(client);
        if(StringUtils.isNotBlank(id)){
            SocketIoUtils.remove(id);
            client.disconnect();    // 断开连接
        }

    }

    @OnEvent(value = "text")
    public void onEvent(SocketIOClient client, String data) {
        // TODO Auto-generated method stub
        // 客户端推送advert_info事件时，onData接受数据，这里是string类型的json数据，还可以为Byte[],object其他类型
        String sa = client.getRemoteAddress().toString();
        String clientIp = sa.substring(1, sa.indexOf(":"));// 获取客户端连接的ip
        Map<String, List<String>> params = client.getHandshakeData().getUrlParams();// 获取客户端url参数
        System.out.println(clientIp + "：客户端：************" + data);
        JSONObject gpsData = (JSONObject) JSONObject.parse(data);
        String userIds = gpsData.get("userName") + "";
        String taskIds = gpsData.get("password") + "";
        client.sendEvent("text1", "后台得到了数据");
    }

    @OnEvent(value = "news")
    public void onNewsEvent(SocketIOClient client, AckRequest ackRequest, String data) {
        // TODO Auto-generated method stub
        // 客户端推送advert_info事件时，onData接受数据，这里是string类型的json数据，还可以为Byte[],object其他类型
        String sa = client.getRemoteAddress().toString();
        String clientIp = sa.substring(1, sa.indexOf(":"));// 获取客户端连接的ip
        Map<String, List<String>> params = client.getHandshakeData().getUrlParams();// 获取客户端url参数
        System.out.println(clientIp + "：客户端：************" + data);
        JSONObject gpsData = (JSONObject) JSONObject.parse(data);
        String userIds = gpsData.get("userName") + "";
        String taskIds = gpsData.get("password") + "";
        client.sendEvent("news", "后台得到了数据");
    }

    /**
     * 获取client连接中的参数 可根据需求自定义
     * @param client
     * @return
     */
    public String getParamsByClient(SocketIOClient client){
        Map<String, List<String>> params = client.getHandshakeData().getUrlParams();
        List<String> list = params.get("userId");
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }
}
