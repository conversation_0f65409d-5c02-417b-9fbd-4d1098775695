/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.core.persistence;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.Reflections;
import com.jeeplus.common.utils.StringUtils;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * 数据Entity类
 *
 * <AUTHOR>
 * @version 2017-05-16
 */
public abstract class TreeEntity<T> extends DataEntity<T> {

	private static final long serialVersionUID = 1L;

	protected T parent;    // 父级编号
	protected String parentIds; // 所有父级编号
	protected String name;    // 名称
	protected Integer sort;        // 排序
	private List<T> children = Lists.newArrayList();    // 父级菜单
	protected  Integer flag;
	private String pname;
	private Integer isFee;
	
	public Integer getIsFee() {
		return isFee;
	}

	public void setIsFee(Integer isFee) {
		this.isFee = isFee;
	}

	public TreeEntity() {
		super();
		this.sort = 30;
	}

	public TreeEntity(String id) {
		super(id);
	}

	/**
	 * 父对象，只能通过子类实现，父类实现mybatis无法读取
	 *
	 * @return
	 */
	public abstract T getParent();

	/**
	 * 父对象，只能通过子类实现，父类实现mybatis无法读取
	 *
	 * @return
	 */
	public abstract void setParent(T parent);

	@Length(min = 1, max = 2000)
	public String getParentIds() {
		return parentIds;
	}

	public void setParentIds(String parentIds) {
		this.parentIds = parentIds;
	}

	@Length(min = 1, max = 600)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}


	public String getParentId() {
		String id = null;
		if (parent != null) {
			id = (String) Reflections.getFieldValue(parent, "id");
		}
		return StringUtils.isNotBlank(id) ? id : "0";
	}

	public String[] getParentIdsArr(){
		String parentIds = StringUtils.isNotBlank(this.getParentIds()) ? this.getParentIds() : "";
		String ids = (parentIds + (StringUtils.isNotBlank(this.id) ? this.id : ""));
		String[] arr = new String[]{};
		if(StringUtils.isNotBlank(ids) && ids.startsWith("0,")){
			ids = ids.substring(2);
		}
		if(StringUtils.isNotBlank(ids)){
			arr = ids.split(",");
		}
		return arr;
	}

	public List<T> getChildren() {
		return children;
	}

	public void setChildren(List<T> children) {
		this.children = children;
	}

	public Integer getFlag() {
		return flag;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	public String getPname() {
		return pname;
	}

	public void setPname(String pname) {
		this.pname = pname;
	}

}
