/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.sys.entity.PayUserSon;

/**
 * 用户MAPPER接口
 */
@Mapper
@Repository
public interface PayUserSonMapper extends BaseMapper<PayUserSon> {
 
	public PayUserSon getByDataCode(@Param("mobile") String mobile
			,@Param("dcode")String dcode);
	
	public int insertPayUserSon(PayUserSon info);
	 
	public int updatePayUserSon(PayUserSon info);

	public int getNumByMobile(String mobile);

	public PayUserSon getByMobile(String mobile);

	public Integer delUserByMobiles(@Param("mobile") String mobile,@Param("dcode")String dcode);

	public void updateUserNameByMobile(PayUserSon suser);

	public void updateDataCodeByMobile(PayUserSon suser);

	public PayUserSon getByUserName(String userName);
}
