/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.web.app;

import java.io.IOException;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.CacheUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.datasource.service.DBChangeService;
import com.jeeplus.modules.sys.entity.PayUser;
import com.jeeplus.modules.sys.entity.PayUserSon;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.security.util.JWTUtil;
import com.jeeplus.modules.sys.service.PayUserService;
import com.jeeplus.modules.sys.service.PayUserSonService;
import com.jeeplus.modules.sys.service.UserService;
import com.jeeplus.modules.sys.utils.GeTuiPushUtils;
import com.jeeplus.modules.sys.utils.UserUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 登录Controller
 *
 * <AUTHOR>
 * @version 2016-5-31
 */
@RestController
@Api(tags = "移动端登录管理")
@RequestMapping("/app")
public class AppLoginController extends BaseController {


    @Autowired
    JeePlusProperites jeePlusProperites;

    @Autowired
    UserService userService;
    @Autowired
    PayUserService payUserService;
    @Autowired
    PayUserSonService payUserSonService;
    @Autowired
    private DBChangeService dbChangeService;
    @PostMapping("/sys/login")
    @ApiOperation(value = "登录接口", consumes = "application/form-data", httpMethod = "POST")
    public AjaxJson login(@RequestParam("mobile") String mobile,
                          @RequestParam("password") String password ,
                          String clientId) {
        AjaxJson j = new AjaxJson();
        System.out.println("密码登录------mobile: " + mobile+"--------password:"+password);
        
        if(StringUtils.isBlank(mobile)) {
       	 j.setMsg("手机号不能为空");
       	 j.setSuccess(false);
       	 return j;
        }

        //查询dcode 是否过期
        PayUserSon paySonUser=payUserSonService.getByMobile(mobile);
        if(paySonUser==null) {
	       	 j.setMsg("您不是会员或者有效用户，请核对后再操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
        if(paySonUser.getDataCode()==null) {
        	j.setMsg("您还没有匹配数据源，请核对后再操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
        //查询dcode 是否过期
        PayUser payUser=payUserService.getByDataCode(paySonUser.getDataCode());
       
        if(payUser==null) {
	       	 j.setMsg("您不是会员或者有效用户，请核对后再操作!");
	       	 j.setSuccess(false);
	       	 return j;
        }
    
        if(payUser.getVipEndTime() !=null &&  payUser.getVipEndTime().compareTo(new Date())<=0) {
        	j.setMsg("您的vip已过期，请登录前台控制台续费后操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
     
        
        dbChangeService.changeDb(payUser.getDataCode());
        
        User user = UserUtils.getByMobile( mobile);
        if(user==null) {
        	 j.setSuccess(false);
             j.setMsg("用户名或者密码错误!");
             return j;
        }
        boolean bol = UserService.validatePassword(password, user.getPassword());
        if (user != null && bol) {

            if (JeePlusProperites.NO.equals(user.getLoginFlag())) {
                j.setSuccess(false);
                j.setMsg("该用户已经被禁止登陆!");
            } else {
                j.setSuccess(true);
                j.put("dcode", user.getDataCode());
                j.put(JWTUtil.TOKEN, JWTUtil.createAccessToken(mobile, user.getPassword(),user.getDataCode()));
                j.put(JWTUtil.REFRESH_TOKEN, JWTUtil.createRefreshToken(mobile, user.getPassword(),user.getDataCode()));
                // 保存推送clientId 信息
                if (StringUtils.isNotBlank(clientId) && !"null".equals(clientId)) {
                    GeTuiPushUtils.addClient(mobile, clientId);
                    System.out.println("clientId: " + clientId);
                }
            }

        } else {
            j.setSuccess(false);
            j.setMsg("用户名或者密码错误!");
        }
        return j;
    }
    
    
    @PostMapping("/sys/smsLogin")
    @ApiOperation(value = "验证码登录接口", consumes = "application/form-data", httpMethod = "POST")
    public AjaxJson smslogin( 
                          @RequestParam("mobile") String mobile,
                          @RequestParam("code") String code, 
                          String clientId) {
        AjaxJson j = new AjaxJson();
        System.out.println("验证码登录------mobile: " + mobile+"--------code:"+code);
   

        //查询dcode 是否过期
        PayUserSon paySonUser=payUserSonService.getByMobile(mobile);
        if(paySonUser==null) {
	       	 j.setMsg("您不是会员或者有效用户，请核对后再操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
        if(paySonUser.getDataCode()==null) {
        	j.setMsg("您还没有匹配数据源，请核对后再操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
       //查询dcode 是否过期
       PayUser payUser=payUserService.getByDataCode(paySonUser.getDataCode());
       if(payUser==null) {
       	 j.setMsg("您不是会员或者有效用户，请核对后再操作!");
       	 j.setSuccess(false);
       	 return j;
       }
       if(payUser !=null &&  payUser.getVipEndTime().compareTo(new Date())<=0) {
       	j.setMsg("您的vip已过期，请登录前台控制台续费后操作");
       	 j.setSuccess(false);
       	 return j;
       }
   
       dbChangeService.changeDb(payUser.getDataCode());
       
       User user = UserUtils.getByMobile( mobile);
       if(user==null) {
    	 	j.setMsg("手机号号码错误");
          	 j.setSuccess(false);
          	 return j;
       }
        boolean bol = userService.validateCode(mobile, user.getId(), code);
        if (user != null && bol) {

            if (JeePlusProperites.NO.equals(user.getLoginFlag())) {
                j.setSuccess(false);
                j.setMsg("该用户已经被禁止登陆!");
            } else {
                j.setSuccess(true);
                j.put("dcode", user.getDataCode());
                j.put(JWTUtil.TOKEN, JWTUtil.createAccessToken(mobile, user.getPassword(),user.getDataCode()));
                j.put(JWTUtil.REFRESH_TOKEN, JWTUtil.createRefreshToken(mobile, user.getPassword(),user.getDataCode()));
                // 保存推送clientId 信息
                if (StringUtils.isNotBlank(clientId) && !"null".equals(clientId)) {
                    GeTuiPushUtils.addClient(mobile, clientId);
                    System.out.println("clientId: " + clientId);
                }
            }

        } else {
            j.setSuccess(false);
            j.setMsg("验证码错误");
        }
        return j;
    }


    /**
     * 退出登录
     * @throws IOException
     */
    @ApiOperation("用户退出")
    @GetMapping("/sys/logout")
    public AjaxJson logout(String clientId) {
        AjaxJson j = new AjaxJson();
        String token = UserUtils.getToken();
        if (StringUtils.isNotBlank(token)) {
            String loginName = UserUtils.getUser().getLoginName();
            CacheUtils.remove(loginName);
            UserUtils.clearCache();
            UserUtils.getSubject().logout();
        }
        if (StringUtils.isNotBlank(clientId)) {
            GeTuiPushUtils.removeClient(clientId);
            System.out.println("clientId: " + clientId);
        }
        j.setMsg("退出成功");
        return j;
    }

    
    @PostMapping("/sys/sendCode")
    @ApiOperation("验证码获取接口")
    public AjaxJson sendCode(HttpServletRequest request, String mobile
    		 ) {
         System.out.println("==========mobile:"+mobile);
         PayUserSon payUserSon=payUserSonService.getByMobile(mobile);
         if(payUserSon==null) {
        	 AjaxJson j=new AjaxJson();
         	 j.setMsg("您的账号或者密码错误！！");
 	       	 j.setSuccess(false);
 	       	 return j;
        }
         
         if(payUserSon.getDataCode()==null) {
        	 AjaxJson j=new AjaxJson();
         	j.setMsg("您不是会员或者有效用户，请核对后再操作");
 	       	 j.setSuccess(false);
 	       	 return j;
         }
         //查询dcode 是否过期
         PayUser payUser=payUserService.getByDataCode(payUserSon.getDataCode());
         if(payUser==null) {
        	 AjaxJson j=new AjaxJson();
 	       	 j.setMsg("非法访问，请检查你的访问地址");
 	       	 j.setSuccess(false);
 	       	 return j;
         }
         if(payUser.getVipEndTime() !=null && payUser.getVipEndTime().compareTo(new Date())<=0) {
        	 AjaxJson j=new AjaxJson();
         	j.setMsg("您的vip已过期，请登录前台控制台续费后操作");
 	       	 j.setSuccess(false);
 	       	 return j;
         }
         
         if(payUser.getDataCode()==null) {
        	 AjaxJson j=new AjaxJson();
         	j.setMsg("您还没有匹配数据源，请核对后再操作");
 	       	 j.setSuccess(false);
 	       	 return j;
         }
         
         dbChangeService.changeDb(payUser.getDataCode());
        return userService.getVerificationCode(mobile);
    }


}
