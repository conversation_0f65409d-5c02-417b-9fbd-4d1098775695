/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.entity;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.jeeplus.common.utils.SpringContextHolder;
import com.jeeplus.config.swagger.IgnoreSwaggerParameter;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.common.utils.Collections3;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.common.utils.excel.fieldtype.RoleListType;
import com.jeeplus.core.persistence.DataEntity;
/**
 * 用户Entity
 * <AUTHOR>
 * @version 2016-12-05
 */
public class User extends DataEntity<User> {

	private static final long serialVersionUID = 1L;
	/** 用户类型 1、管理员  2、主任 3、律师 */
	public static final String TYPE_MANAGE = "1";
	public static final String TYPE_DIRECTOR = "2";
	public static final String TYPE_LAWYER = "3";

	@IgnoreSwaggerParameter
	private Office company;	// 归属公司
	@IgnoreSwaggerParameter
	private Office office;	// 归属部门
	private String loginName;// 登录名
	private String password;// 密码
	private String no;		// 工号
	private String name;	// 姓名
	private String email;	// 邮箱
	private String phone;	// 电话
	private String mobile;	// 手机
	@IgnoreSwaggerParameter
	private String loginIp;	// 最后登陆IP
	@IgnoreSwaggerParameter
	private Date loginDate;	// 最后登陆日期
	private String loginFlag;	// 是否允许登陆
	private String photo;	// 头像
	private String qrCode;	//二维码
	private String oldLoginName;// 原登录名
	private String newPassword;	// 新密码
	private String sign;//签名
	@IgnoreSwaggerParameter
	private String oldLoginIp;	// 上次登陆IP
	@IgnoreSwaggerParameter
	private Date oldLoginDate;	// 上次登陆日期
	@IgnoreSwaggerParameter
	private Role role;	// 根据角色查询用户条件
	private Post post;
	private String type;		// 拥有的用户类型 1.管理员  2.主任  3.律师
	
	private Integer buyModel;
	//民
	private Integer buyModel1;
	//刑
	private Integer buyModel2;
	//行政
	private Integer buyModel3;
	//执行
	private Integer buyModel4;

	public Integer getBuyModel1() {
		return buyModel1;
	}

	public void setBuyModel1(Integer buyModel1) {
		this.buyModel1 = buyModel1;
	}

	public Integer getBuyModel2() {
		return buyModel2;
	}

	public void setBuyModel2(Integer buyModel2) {
		this.buyModel2 = buyModel2;
	}

	public Integer getBuyModel3() {
		return buyModel3;
	}

	public void setBuyModel3(Integer buyModel3) {
		this.buyModel3 = buyModel3;
	}

	public Integer getBuyModel4() {
		return buyModel4;
	}

	public void setBuyModel4(Integer buyModel4) {
		this.buyModel4 = buyModel4;
	}

	public Integer getBuyModel() {
		return buyModel;
	}

	public void setBuyModel(Integer buyModel) {
		this.buyModel = buyModel;
	}

	@IgnoreSwaggerParameter
	private List<Role> roleList = Lists.newArrayList(); // 拥有角色列表

	private String dataCode;

	public String getDataCode() {
		return dataCode;
	}

	public void setDataCode(String dataCode) {
		this.dataCode = dataCode;
	}

	public User() {
		super();
	}

	public User(String id){
		super(id);
	}

	public User(String id, String loginName,String mobile){
		super(id);
		this.loginName = loginName;
		this.mobile = mobile;
	}

	public User(Role role){
		super();
		this.role = role;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public String getLoginFlag() {
		return loginFlag;
	}

	public void setLoginFlag(String loginFlag) {
		this.loginFlag = loginFlag;
	}

	public String getId() {
		return id;
	}

	//	@NotNull(message="归属公司不能为空")
	@ExcelField(title="归属公司", align=2, sort=20)
	public Office getCompany() {
		return company;
	}

	public void setCompany(Office company) {
		this.company = company;
	}

	//	@NotNull(message="归属部门不能为空")
	@ExcelField(title="归属部门", align=2, sort=25)
	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	@Length(min=5, max=100, message="登录名长度必须介于 1 和 100 之间")
	@ExcelField(title="登录名", align=2, sort=30)
	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	@JsonIgnore
	@Length(min=1, max=100, message="密码长度必须介于 1 和 100 之间")
	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Length(min=1, max=100, message="姓名长度必须介于 1 和 100 之间")
	@ExcelField(title="姓名", align=2, sort=40)
	public String getName() {
		return name;
	}

	//	@Length(min=1, max=100, message="工号长度必须介于 1 和 100 之间")
	@ExcelField(title="工号", align=2, sort=45)
	public String getNo() {
		return no;
	}

	public void setNo(String no) {
		this.no = no;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Email(message="邮箱格式不正确")
	@Length(min=0, max=200, message="邮箱长度必须介于 1 和 200 之间")
	@ExcelField(title="邮箱", align=1, sort=50)
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Length(min=0, max=200, message="电话长度必须介于 1 和 200 之间")
	@ExcelField(title="电话", align=2, sort=60)
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	@Length(min=0, max=200, message="手机长度必须介于 1 和 200 之间")
	@ExcelField(title="手机", align=2, sort=70)
	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	@ExcelField(title="备注", align=1, sort=900)
	public String getRemarks() {
		return remarks;
	}

	@ExcelField(title="创建时间", type=0, align=1, sort=90)
	public Date getCreateDate() {
		return createDate;
	}

	@ExcelField(title="最后登录IP", type=0, align=1, sort=100)
	public String getLoginIp() {
		return loginIp;
	}

	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="最后登录日期", type=0, align=1, sort=110)
	public Date getLoginDate() {
		return loginDate;
	}

	public void setLoginDate(Date loginDate) {
		this.loginDate = loginDate;
	}

	public String getOldLoginName() {
		return oldLoginName;
	}

	public void setOldLoginName(String oldLoginName) {
		this.oldLoginName = oldLoginName;
	}

	public String getNewPassword() {
		return newPassword;
	}

	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}

	public String getOldLoginIp() {
		if (oldLoginIp == null){
			return loginIp;
		}
		return oldLoginIp;
	}

	public void setOldLoginIp(String oldLoginIp) {
		this.oldLoginIp = oldLoginIp;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getOldLoginDate() {
		if (oldLoginDate == null){
			return loginDate;
		}
		return oldLoginDate;
	}

	public void setOldLoginDate(Date oldLoginDate) {
		this.oldLoginDate = oldLoginDate;
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	@JsonIgnore
	@ExcelField(title="拥有角色", align=1, sort=800, fieldType=RoleListType.class)
	public List<Role> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<Role> roleList) {
		this.roleList = roleList;
	}

	public List<String> getRoleIdList() {
		List<String> roleIdList = Lists.newArrayList();
		for (Role role : roleList) {
			roleIdList.add(role.getId());
		}
		return roleIdList;
	}

	public void setRoleIdList(List<String> roleIdList) {
		roleList = Lists.newArrayList();
		for (String roleId : roleIdList) {
			Role role = new Role();
			role.setId(roleId);
			roleList.add(role);
		}
	}

	/**
	 * 用户拥有的角色名称字符串, 多个角色名称用','分隔.
	 */
	public String getRoleNames() {
		return Collections3.extractToString(roleList, "name", ",");
	}

	public String getRoleIds() {
		return Collections3.extractToString(roleList, "id", ",");
	}


	public boolean isAdmin(){
		return isAdmin(this.id);
	}

	public static boolean isAdmin(String id){
		return id != null && "1".equals(id);
	}

	@Override
	public String toString() {
		return id;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getQrCode() {
		return qrCode;
	}

	/**
	 * @param sign the createAccessToken to set
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * @return the createAccessToken
	 */
	public String getSign() {
		return sign;
	}


	public Post getPost() {
		return post;
	}

	public void setPost(Post post) {
		this.post = post;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
