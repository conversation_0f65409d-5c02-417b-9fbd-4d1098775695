
package com.jeeplus.modules.sys.utils;

import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.jeeplus.modules.datasource.config.DBContextHolder;
import com.jeeplus.modules.datasource.service.DBChangeService;
import com.jeeplus.modules.sys.security.util.JWTUtil;

/**
 * 数据源切换
 *
 */
@Aspect
@Component
@Order(-1)
public class DataSourceAspect {

	 
	@Autowired
	private DBChangeService dbChangeService;

	@Pointcut(value = "execution(* com.jeeplus..*Controller*.*(..)) ")
	private void aspectPointcut() {

	}

	@Around(value = "aspectPointcut()")
	public Object doAround(ProceedingJoinPoint point) throws Throwable {
		if(SecurityUtils.getSubject()!=null && SecurityUtils.getSubject().getPrincipal()!=null) {
			dbChangeService.changeDb(JWTUtil.getDataSourceCode(SecurityUtils.getSubject().getPrincipal().toString()));
		}
		return point.proceed();
	}
}
