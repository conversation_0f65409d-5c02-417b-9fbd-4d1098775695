package com.jeeplus.modules.sys.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 租户对象 pay_user
 * 
 * <AUTHOR>
 */
public class PayUser implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 手机号 */
    private String mobile;

    /** 密码 */
    private String password;

    /** 名称 */
    private String realName;
    
    /** 号码 */
    private String no;

    /** 地址 */
    private String address;

    /** vip标志 */
    private Integer isVip;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date vipStartTime;

    /** 过期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date vipEndTime;

    /** vip人数 */
    private Integer vipNums;

    /** 当前使用人数 */
    private Integer curNums;

    /** 状态 */
    private Integer status;

    /** 数据原编码 */
    private String dataCode;
     
    private String adminName;
    
    //"购买全部模板"
    private Integer buyModel;
    // "购买民事模板"
    private Integer buyModel1;
   //"购买刑事模板")
    private Integer buyModel2;
    //"购买民政模板")
    private Integer buyModel3;
    //"购买执行模板")
    private Integer buyModel4;
    
	public Integer getBuyModel1() {
		return buyModel1;
	}

	public void setBuyModel1(Integer buyModel1) {
		this.buyModel1 = buyModel1;
	}

	public Integer getBuyModel2() {
		return buyModel2;
	}

	public void setBuyModel2(Integer buyModel2) {
		this.buyModel2 = buyModel2;
	}

	public Integer getBuyModel3() {
		return buyModel3;
	}

	public void setBuyModel3(Integer buyModel3) {
		this.buyModel3 = buyModel3;
	}

	public Integer getBuyModel4() {
		return buyModel4;
	}

	public void setBuyModel4(Integer buyModel4) {
		this.buyModel4 = buyModel4;
	}

	public String getAdminName() {
		return adminName;
	}

	public void setAdminName(String adminName) {
		this.adminName = adminName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getNo() {
		return no;
	}

	public void setNo(String no) {
		this.no = no;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Integer getIsVip() {
		return isVip;
	}

	public void setIsVip(Integer isVip) {
		this.isVip = isVip;
	}

	public Date getVipStartTime() {
		return vipStartTime;
	}

	public void setVipStartTime(Date vipStartTime) {
		this.vipStartTime = vipStartTime;
	}

	public Date getVipEndTime() {
		return vipEndTime;
	}

	public void setVipEndTime(Date vipEndTime) {
		this.vipEndTime = vipEndTime;
	}

	public Integer getVipNums() {
		return vipNums;
	}

	public void setVipNums(Integer vipNums) {
		this.vipNums = vipNums;
	}

	public Integer getCurNums() {
		return curNums;
	}

	public void setCurNums(Integer curNums) {
		this.curNums = curNums;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getDataCode() {
		return dataCode;
	}

	public void setDataCode(String dataCode) {
		this.dataCode = dataCode;
	}

	public Integer getBuyModel() {
		return buyModel;
	}

	public void setBuyModel(Integer buyModel) {
		this.buyModel = buyModel;
	}
     
}
