/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.CacheUtils;
import com.jeeplus.common.utils.Encodes;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.security.Digests;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.core.service.ServiceException;
import com.jeeplus.modules.sys.entity.Office;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.entity.VerificationCode;
import com.jeeplus.modules.sys.mapper.UserMapper;
import com.jeeplus.modules.sys.utils.MsmConstantUtils;
import com.jeeplus.modules.sys.utils.PlfMD5;
import com.jeeplus.modules.sys.utils.UserUtils;

import cn.hutool.core.date.DateUtil;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @version 2016-12-05
 */
@Service
@Transactional
public class UserService extends CrudService<UserMapper, User> {

	public static final String HASH_ALGORITHM = "SHA-1";
	public static final int HASH_INTERATIONS = 1024;
	public static final int SALT_SIZE = 8;

	@Autowired
	private MsmConstantUtils msmConstantUtils;

	@Autowired
	private UserMapper userMapper;
	public User getById(String id) {
		return userMapper.get(id);
	}
	/**
	 * 获取用户
	 *
	 * @param id
	 * @return
	 */
	public User get(String id) {
		return UserUtils.get(id);
	}

	/**
	 * 根据登录名获取用户
	 *
	 * @param loginName
	 * @return
	 */
	public User getUserByLoginName(String loginName) {
		return UserUtils.getByMobile(loginName);
	}

	/**
	 * 获取简短的用户列表信息
	 *
	 * @param user
	 * @return
	 */
	public List<User> findBriefList(User user) {
		return mapper.findBriefList(user);
	}

	/**
	 * 通过部门ID获取用户列表，仅返回用户id和name（树查询用户时用）
	 *
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<User> findUserByOfficeId(String officeId) {
		List<User> list = (List<User>) CacheUtils.get(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + officeId);
		if (list == null) {
			User user = new User();
			user.setOffice(new Office(officeId));
			list = mapper.findUserByOfficeId(user);
			CacheUtils.put(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + officeId, list);
		}
		return list;
	}

	/**
	 * 保存更新用户 及其角色信息
	 *
	 * @param user
	 */
	@Transactional(readOnly = false)
	public void saveUser(User user) {
		if (StringUtils.isBlank(user.getId())) {
			user.preInsert();
			mapper.insert(user);
		} else {
			// 清除原用户机构用户缓存
			User oldUser = mapper.get(user.getId());
			if (oldUser.getOffice() != null && oldUser.getOffice().getId() != null) {
				CacheUtils.remove(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + oldUser.getOffice().getId());
			}
			// 更新用户数据
			user.preUpdate();
			mapper.update(user);
		}
		if (StringUtils.isNotBlank(user.getId())) {
			// 更新用户与角色关联
			mapper.deleteUserRole(user);
			if (user.getRoleList() != null && user.getRoleList().size() > 0) {
				mapper.insertUserRole(user);
			} else {
				throw new ServiceException(user.getLoginName() + "没有设置角色！");
			}
			// 清除用户缓存
			UserUtils.clearCache(user);
		}
	}

	/**
	 * 保存、更新用户
	 *
	 * @param user
	 */
	@Transactional(readOnly = false)
	public void saveUserInfo(User user) {
		if (StringUtils.isBlank(user.getId())) {
			user.preInsert();
			mapper.insert(user);
		} else {
			// 清除原用户机构用户缓存
			User oldUser = mapper.get(user.getId());
			if (oldUser!=null && oldUser.getOffice() != null && oldUser.getOffice().getId() != null) {
				CacheUtils.remove(UserUtils.USER_CACHE, UserUtils.USER_CACHE_LIST_BY_OFFICE_ID_ + oldUser.getOffice().getId());
			}
			// 更新用户数据
			user.preUpdate();
			mapper.update(user);
		}
	}

	@Transactional(readOnly = false)
	public void updateUserInfo(User user) {
		user.preUpdate();
		mapper.updateUserInfo(user);
		// 清除用户缓存
		UserUtils.clearCache(user);
	}

	@Transactional(readOnly = false)
	public void deleteUser(User user) {
		mapper.deleteUserRole(user);
		mapper.delete(user);
		// 清除用户缓存
		UserUtils.clearCache(user);
	}

	@Transactional(readOnly = false)
	public void updatePasswordById(String id, String loginName, String newPassword) {
		User user = new User(id);
		user.setPassword(PlfMD5.encodeByMd5AndSalt(newPassword));
		mapper.updatePasswordById(user);
		// 清除用户缓存
		user.setLoginName(loginName);
		UserUtils.clearCache(user);
	}

	@Transactional(readOnly = false)
	public void updateUserLoginInfo(User user) {
		// 保存上次登录信息
		user.setOldLoginIp(user.getLoginIp());
		user.setOldLoginDate(user.getLoginDate());
		// 更新本次登录信息
		user.setLoginIp(UserUtils.getSession().getHost());
		user.setLoginDate(new Date());
		mapper.updateLoginInfo(user);
	}

	/**
	 * 生成安全的密码，生成随机的16位salt并经过1024次 sha-1 hash
	 */
	public static String entryptPassword(String plainPassword) {
		byte[] salt = Digests.generateSalt(SALT_SIZE);
		byte[] hashPassword = Digests.sha1(plainPassword.getBytes(), salt, HASH_INTERATIONS);
		return Encodes.encodeHex(salt) + Encodes.encodeHex(hashPassword);
	}

	/**
	 * 验证密码
	 * @param plainPassword 明文密码
	 * @param password 密文密码
	 * @return 验证成功返回true
	 */
	public static boolean validatePassword(String plainPassword, String password) {
		
		System.out.println("=密码对比==pwd:"+plainPassword+",pwd2:"+password);
		if(StringUtils.isNotEmpty(plainPassword) && StringUtils.isNotEmpty(password)){
			return PlfMD5.encodeByMd5AndSalt(plainPassword).equals(password);
		}
		/**if(StringUtils.isNotEmpty(plainPassword) && StringUtils.isNotEmpty(password)){
			byte[] salt = Encodes.decodeHex(password.substring(0,16));
			byte[] hashPassword = Digests.sha1(plainPassword.getBytes(), salt, HASH_INTERATIONS);
			return password.equals(Encodes.encodeHex(salt)+Encodes.encodeHex(hashPassword));
		}**/
		return false;
	}

	/**
	 * 验证验证码
	 */
	public boolean validateCode(String mobile,  String userId,  String code) {
		if (Objects.nonNull(mobile) && Objects.nonNull(code)  && Objects.nonNull(userId)) {
			  VerificationCode verificationCode = mapper.findVerificationCodeByMobileAndUserId(mobile, userId, DateUtil.formatDateTime(new Date()));
			if (Objects.nonNull(verificationCode) && code.equals(verificationCode.getCode().toString())) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取验证码
	 */
	public AjaxJson getVerificationCode(final String mobile) {

		if (Objects.isNull(mobile)) {
			return AjaxJson.error("手机号不能为空...");
		}

		final User user = new User();
		user.setMobile(mobile);
		final User u = super.get(user);
		if (Objects.isNull(u)) {
			return AjaxJson.error("不存在此手机号用户...");
		}

		final Date date = new Date();
		final VerificationCode verificationCode = mapper.findVerificationCodeByMobileAndUserId(u.getMobile(), u.getId(), DateUtil.formatDateTime(date));
		if (Objects.nonNull(verificationCode)) {
			return AjaxJson.error("验证码正在发送中，请不要重复发送...");
		}

		final String code = MsmConstantUtils.generateValidateCode(6);
		final boolean bol = MsmConstantUtils.sendPhone(code, String.valueOf(mobile));
		if (bol) {
			VerificationCode vCode = new VerificationCode();
			vCode.setUserId(u.getId());
			vCode.setCode(Integer.valueOf(code));
			vCode.setMobile(Long.valueOf(mobile));
			vCode.setValidTime(String.valueOf(DateUtil.offsetMinute(date, 1)));
			vCode.setCreateTime(DateUtil.formatDateTime(new Date()));
			mapper.installVerificationCode(vCode);
			return AjaxJson.success();
		}
		return AjaxJson.error("短信发送异常...");
	}

	public User getUserByMobile(String mobile) {
		return UserUtils.getByMobile(mobile);
	}
	
	public int updateUserWordToken(User user) {
		return mapper.updateUserWordToken(user);
	}
	public void updateBuyModel(User user) {
		  mapper.updateBuyModel(user);
	}
	public User getByUserId(String id) {
		return userMapper.getByUserId(id);
	}
}
