/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.web;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.jasig.cas.client.validation.Assertion;
import org.jasig.cas.client.validation.Cas20ServiceTicketValidator;
import org.jasig.cas.client.validation.TicketValidationException;
import org.jasig.cas.client.validation.TicketValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.auth0.jwt.JWT;
import com.google.common.collect.Maps;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.CacheUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.core.web.GlobalErrorController;
import com.jeeplus.modules.datasource.service.DBChangeService;
import com.jeeplus.modules.sys.entity.Office;
import com.jeeplus.modules.sys.entity.PayUser;
import com.jeeplus.modules.sys.entity.PayUserSon;
import com.jeeplus.modules.sys.entity.Post;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.security.util.JWTUtil;
import com.jeeplus.modules.sys.service.PayUserService;
import com.jeeplus.modules.sys.service.PayUserSonService;
import com.jeeplus.modules.sys.service.UserService;
import com.jeeplus.modules.sys.utils.PlfMD5;
import com.jeeplus.modules.sys.utils.UserUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 登录Controller
 *
 * <AUTHOR>
 * @version 2016-5-31
 */
@RestController
@Api(tags = "登录管理")
public class LoginController extends BaseController {
 
    @Autowired
    JeePlusProperites jeePlusProperites;
    @Autowired
    private DBChangeService dbChangeService;
    @Autowired
    UserService userService;
    @Autowired
    PayUserService payUserService;
    @Autowired
    PayUserSonService payUserSonService;
     
    @PostMapping("/sys/autoLogin")
    @ApiOperation("自动登录接口")
    public AjaxJson autoLogin(HttpServletRequest request,
    		String mobile, 
    		String puid, 
    		String userName,
    		String dsCode) {
        AjaxJson j = new AjaxJson();
        if(dsCode==null ||  puid==null) {
        	 j.setMsg("非法访问，请检查你的访问地址");
        	 j.setSuccess(false);
        	 return j;
        }
        
  
        //查询dcode 是否过期
        PayUser payUser=payUserService.getByDataCode(dsCode);
       
        if(payUser==null) {
	       	 j.setMsg("非法访问，请检查你的访问地址");
	       	 j.setSuccess(false);
	       	 return j;
        }
        if(payUser.getVipEndTime().compareTo(new Date())<=0) {
        	j.setMsg("您的vip已过期，请登录前台控制台续费后操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
        if(!payUser.getId().toString().equals(puid)) {
	       	 j.setMsg("非法访问，请检查你的访问地址");
	       	 j.setSuccess(false);
	       	 return j;
        }
        PayUserSon payUserSon=payUserSonService.getByMobile(mobile);
        
        dbChangeService.changeDb(dsCode);
        User user = UserUtils.getByMobile(payUserSon.getMobile());
        if(user != null) {
             if (JeePlusProperites.NO.equals(user.getLoginFlag())) {
                 j.setSuccess(false);
                 j.setMsg("该用户已经被禁止登陆!");
             } else {
            	  updateModelInfo(payUser,user);
                 j.setSuccess(true);
                 j.put(JWTUtil.TOKEN, JWTUtil.createAccessToken(mobile, user.getPassword(),user.getDataCode()));
                 j.put(JWTUtil.REFRESH_TOKEN, JWTUtil.createRefreshToken(mobile, user.getPassword(),user.getDataCode()));
             }

        }else {
             j.setSuccess(false);
        }
       
        return j;
    }
    

    @PostMapping("/sys/login")
    @ApiOperation("登录接口")
    public AjaxJson login(HttpServletRequest request,
    		String password, String mobile, String code) {
        AjaxJson j = new AjaxJson();
        
        System.out.println("==登录来了==mobile:"+mobile);
   
        PayUserSon payUserSon=payUserSonService.getByMobile(mobile);
        if(payUserSon==null) {
        	 j.setMsg("您的账号或者密码错误！！");
	       	 j.setSuccess(false);
	       	 return j;
       }
        if(payUserSon.getDataCode()==null) {
        	j.setMsg("您不是会员或者有效用户，请核对后再操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
        
        PayUser payUser= payUserService.getByDataCode(payUserSon.getDataCode());
        if(payUser==null) {
	       	 j.setMsg("您的账号或者密码错误");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
        
        if(payUser.getVipEndTime().compareTo(new Date())<=0) {
        	j.setMsg("您的vip已过期，请登录前台控制台续费后操作");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
        
        dbChangeService.changeDb(payUserSon.getDataCode());
        User user = UserUtils.getByMobile(payUserSon.getMobile());
        if(user != null) {
        	 boolean bol =StringUtils.isBlank(code)? UserService.validatePassword(password, user.getPassword()):userService.validateCode(mobile, user.getId(), code);
             if (user != null && bol) {

                 if (JeePlusProperites.NO.equals(user.getLoginFlag())) {
                     j.setSuccess(false);
                     j.setMsg("该用户已经被禁止登陆。");
                 } else {
                	  updateModelInfo(payUser,user);
                     j.setSuccess(true);
                     j.put(JWTUtil.TOKEN, JWTUtil.createAccessToken(user.getLoginName(), user.getPassword(),user.getDataCode()));
                     j.put(JWTUtil.REFRESH_TOKEN, JWTUtil.createRefreshToken(user.getLoginName(), user.getPassword(),user.getDataCode()));
                 }

             } else {
                 if (Objects.nonNull(code)) {
                     j.setMsg("验证码错误");
                 } else {
                     j.setMsg("用户名或者密码错误!");
                 }
                 j.setSuccess(false);
             }
        }else {
        	 if (Objects.nonNull(code)) {
                 j.setMsg("验证码错误");
             } else {
                 j.setMsg("用户名或者密码错误!");
             }
             j.setSuccess(false);
        }
       
        return j;
    }


    private void updateModelInfo(PayUser payUser, User user) {
		 System.out.println("====p:"+payUser.getBuyModel()+"---u:"+user.getBuyModel());
		if(payUser.getBuyModel()==1 && user.getBuyModel()==0) {
			user.setBuyModel(1);
			user.setBuyModel1(user.getBuyModel1());
			user.setBuyModel2(user.getBuyModel2());
			user.setBuyModel3(user.getBuyModel3());
			user.setBuyModel4(user.getBuyModel4());
			userService.updateBuyModel(user);
		}
		if(payUser.getBuyModel1()==1 && user.getBuyModel1()==0) {
			user.setBuyModel1(1);
			user.setBuyModel(user.getBuyModel());
			user.setBuyModel2(user.getBuyModel2());
			user.setBuyModel3(user.getBuyModel3());
			user.setBuyModel4(user.getBuyModel4());
			userService.updateBuyModel(user);
		}
		if(payUser.getBuyModel2()==1 && user.getBuyModel2()==0) {
			user.setBuyModel2(1);
			user.setBuyModel1(user.getBuyModel1());
			user.setBuyModel(user.getBuyModel());
			user.setBuyModel3(user.getBuyModel3());
			user.setBuyModel4(user.getBuyModel4());
			userService.updateBuyModel(user);
		}
		if(payUser.getBuyModel3()==1 && user.getBuyModel3()==0) {
			user.setBuyModel3(1);
			user.setBuyModel1(user.getBuyModel1());
			user.setBuyModel2(user.getBuyModel2());
			user.setBuyModel(user.getBuyModel());
			user.setBuyModel4(user.getBuyModel4());
			userService.updateBuyModel(user);
		}
		if(payUser.getBuyModel4()==1 && user.getBuyModel4()==0) {
			user.setBuyModel4(1);
			user.setBuyModel1(user.getBuyModel1());
			user.setBuyModel2(user.getBuyModel2());
			user.setBuyModel3(user.getBuyModel3());
			user.setBuyModel(user.getBuyModel());
			userService.updateBuyModel(user);
		}
	}


	/**
     * cas登录
     * vue 传递ticket参数验证，并返回token
     */
    @RequestMapping("/sys/casLogin")
    public AjaxJson casLogin(@RequestParam(name = "ticket") String ticket,
                             @RequestParam(name = "service") String service, @Value("${cas.server-url-prefix}") String casServer) throws Exception {
        AjaxJson j = new AjaxJson();
        //ticket检验器
        TicketValidator ticketValidator = new Cas20ServiceTicketValidator(casServer);
        try {
            // 去CAS服务端中验证ticket的合法性
            Assertion casAssertion = ticketValidator.validate(ticket, service);
            // 从CAS服务端中获取相关属性,包括用户名、是否设置RememberMe等
            AttributePrincipal casPrincipal = casAssertion.getPrincipal();
            String loginName = casPrincipal.getName();
          
            // 校验用户名密码
            User user = UserUtils.getByMobile(loginName);
            if (user != null) {
                if (JeePlusProperites.NO.equals(user.getLoginFlag())) {
                    throw new AuthenticationException("msg:该已帐号禁止登录.");
                }

                j.put(JWTUtil.TOKEN, JWTUtil.createAccessToken(user.getLoginName(), user.getPassword(),user.getDataCode()));
                j.put(JWTUtil.REFRESH_TOKEN, JWTUtil.createRefreshToken(user.getLoginName(), user.getPassword(),user.getDataCode()));
                return j;


            } else {
                AuthenticationException e = new AuthenticationException("用户【" + loginName + "】不存在!");
                logger.error("用户【loginName:" + loginName + "】不存在!", e);
                throw e;
            }
        } catch (TicketValidationException e) {
            logger.error("Unable to validate ticket [" + ticket + "]", e);
            throw new AuthenticationException("未通过验证的ticket [" + ticket + "]", e);
        }

    }

    @GetMapping("/sys/refreshToken")
    @ApiOperation("刷新token")
    public AjaxJson accessTokenRefresh(String refreshToken, 
    		HttpServletRequest request, HttpServletResponse response) {

        if (JWTUtil.verify(refreshToken) == 1) {
            GlobalErrorController.response4022(request, response);

        } else if (JWTUtil.verify(refreshToken) == 2) {
            return AjaxJson.error("用户名密码错误");
        }

        String loginName = JWTUtil.getLoginName(refreshToken);
        String password = UserUtils.getByMobile(loginName).getPassword();
        //创建新的accessToken
        String accessToken = JWTUtil.createAccessToken(loginName, password,request.getHeader("dsCode"));

        //下面判断是否刷新 REFRESH_TOKEN，如果refreshToken 快过期了 需要重新生成一个替换掉
        long minTimeOfRefreshToken = 2 * JeePlusProperites.newInstance().getEXPIRE_TIME();//REFRESH_TOKEN 有效时长是应该为accessToken有效时长的2倍
        Long refreshTokenExpirationTime = JWT.decode(refreshToken).getExpiresAt().getTime();//refreshToken创建的起始时间点
        //(refreshToken过期时间- 当前时间点) 表示refreshToken还剩余的有效时长，如果小于2倍accessToken时长 ，则刷新 REFRESH_TOKEN
        if (refreshTokenExpirationTime - System.currentTimeMillis() <= minTimeOfRefreshToken) {
            //刷新refreshToken
            refreshToken = JWTUtil.createRefreshToken(loginName, password,request.getHeader("dsCode"));
        }

        return AjaxJson.success().put(JWTUtil.TOKEN, accessToken).put(JWTUtil.REFRESH_TOKEN, refreshToken);
    }


    /**
     * 退出登录
     *
     * @throws IOException
     */
    @ApiOperation("用户退出")
    @GetMapping("/sys/logout")
    public AjaxJson logout() {
        AjaxJson j = new AjaxJson();
        String token = UserUtils.getToken();
        if (StringUtils.isNotBlank(token)) {
            UserUtils.clearCache();
            UserUtils.getSubject().logout();
        }
        j.setMsg("退出成功");
        return j;
    }


    /**
     * 是否是验证码登录
     *
     * @param useruame 用户名
     * @param isFail   计数加1
     * @param clean    计数清零
     * @return
     */
    @SuppressWarnings("unchecked")
    public static boolean isValidateCodeLogin(String useruame, boolean isFail, boolean clean) {
        Map<String, Integer> loginFailMap = (Map<String, Integer>) CacheUtils.get("loginFailMap");
        if (loginFailMap == null) {
            loginFailMap = Maps.newHashMap();
            CacheUtils.put("loginFailMap", loginFailMap);
        }
        Integer loginFailNum = loginFailMap.get(useruame);
        if (loginFailNum == null) {
            loginFailNum = 0;
        }
        if (isFail) {
            loginFailNum++;
            loginFailMap.put(useruame, loginFailNum);
        }
        if (clean) {
            loginFailMap.remove(useruame);
        }
        return loginFailNum >= 3;
    }

    @PostMapping("/sys/getVerificationCode")
    @ApiOperation("验证码获取接口")
    public AjaxJson getVerificationCode(HttpServletRequest request, String mobile) {
     
    	 PayUserSon payUserSon=payUserSonService.getByMobile(mobile);
         if(payUserSon==null) {
        	 AjaxJson j=new AjaxJson();
         	 j.setMsg("您的账号或者密码错误！！");
 	       	 j.setSuccess(false);
 	       	 return j;
        }
         
         if(payUserSon.getDataCode()==null) {
        	 AjaxJson j=new AjaxJson();
         	j.setMsg("您不是会员或者有效用户，请核对后再操作");
 	       	 j.setSuccess(false);
 	       	 return j;
         }
         
         
         //查询dcode 是否过期
         PayUser payUser=payUserService.getByDataCode(payUserSon.getDataCode());
         if(payUser==null) {
        	 AjaxJson j=new AjaxJson();
 	       	 j.setMsg("非法访问，请检查你的访问地址");
 	       	 j.setSuccess(false);
 	       	 return j;
         }
         if(payUser.getVipEndTime().compareTo(new Date())<=0) {
        	 AjaxJson j=new AjaxJson();
         	j.setMsg("您的vip已过期，请登录前台控制台续费后操作");
 	       	 j.setSuccess(false);
 	       	 return j;
         }
         
         dbChangeService.changeDb(payUserSon.getDataCode());
        return userService.getVerificationCode(mobile);
    }


    
   //获取创建管理员基本信息
    @GetMapping("/sys/getPayAdmin")
    public AjaxJson getPayAdmin(HttpServletRequest request,String dsCode) {
        AjaxJson j = new AjaxJson();
        if(dsCode==null) {
        	 j.setMsg("非法访问，请检查你的访问地址");
        	 j.setSuccess(false);
        	 return j;
        } 
        //查询dcode 是否过期
        PayUser payUser=payUserService.getByDataCode(dsCode);
        if(payUser==null) {
	       	 j.setMsg("非法访问，请检查你的访问地址");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
        if(StringUtils.isNoneBlank(payUser.getAdminName())) {
	       	 j.setMsg("已创建过管理员了，请问重复创建");
	       	 j.setSuccess(false);
	       	 return j;
       }
        
        j.setSuccess(true);
        j.put("mobile", payUser.getMobile());
        
        
        return j;
    }
    
 
    
    //创建管理员
    @RequestMapping("/sys/createPayAdmin")
    public AjaxJson createPayAdmin(HttpServletRequest request,String dsCode,String sign) {
    	 AjaxJson j = new AjaxJson();
        if(dsCode==null) {
        	 j.setMsg("非法访问，请检查你的访问地址");
        	 j.setSuccess(false);
        	 return j;
        } 
        if(!"law2024@qwe".equals(sign)){
        	 j.setMsg("非法访问，请检查你的访问地址!!");
        	 j.setSuccess(false);
        	 return j;
    	}
        //查询dcode 是否过期
        PayUser payUser=payUserService.getByDataCode(dsCode);
        if(payUser==null) {
	       	 j.setMsg("非法访问，请检查你的访问地址!");
	       	 j.setSuccess(false);
	       	 return j;
        }
        
        if(StringUtils.isNoneBlank(payUser.getAdminName())) {
	       	 j.setMsg("已创建过管理员了，请问重复创建");
	       	 j.setSuccess(false);
	       	 return j;
       }
        
        
        payUserService.updateAdminNameById(payUser.getId(), "admin");
        
    	PayUserSon suser=new PayUserSon();
    	suser.setMobile(payUser.getMobile());
    	suser.setDataCode(payUser.getDataCode());
    	payUserSonService.updateDataCodeByMobile(suser);
        
        
        dbChangeService.changeDb(dsCode);
        User user =new User();
        user.setMobile(payUser.getMobile());
        user.setDataCode(dsCode);
        user.setDelFlag("0");
        user.setId("1");
        user.setLoginFlag("1");
        user.setLoginName(payUser.getMobile());
        user.setNo("1");
        user.setPassword(PlfMD5.encodeByMd5AndSalt(payUser.getMobile().substring(5,11)));
        if(payUser.getRealName()!=null) {
            user.setName(payUser.getRealName()+"(管理员)");
        }else {
        	user.setName(payUser.getMobile()+"(管理员)");
        }
    
        user.setType("1,2");
        user.setCompany(null);
        user.setCreateDate(new Date());
        user.setUpdateDate(user.getCreateDate());
        Office office=new Office();
        office.setId("2f0a0b8d0b2745bab97c4c13cedd59af");
        user.setCompany(office);
        Office office2=new Office();
        office2.setId("6bc43c2a6cd44550a011bd160b78d92d");
        user.setOffice(office2);
        Post post=new Post();
        post.setId("3f3b0c6ab8f840f58f184a418a5de00e");
        user.setPost(post);
        user.setIsNewRecord(true);
        userService.save(user);
        
        j.setSuccess(true);
        
        return j;
    }
}
