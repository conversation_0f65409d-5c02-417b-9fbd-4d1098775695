/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.utils;

import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.common.utils.SpringContextHolder;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.FileProperties;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.config.web.Servlets;
import com.jeeplus.modules.sys.entity.FileData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributeView;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.Principal;
import java.util.*;

/**
 * Created by 刘高峰 on 2018/3/18.
 */
@Component
public class FileKit {

    @Autowired
    private FileProperties fileProperties;
    /* 文件属性 因使用Configuration 注解 需特殊处理 */
    private static FileProperties FILE_PROPERTIES;
    private static String MY_DOCUMENTS = "myDocuments/";             // 我的文档
    private static String SHARE_DOCUMENTS = "shareDocuments/";       // 共享文档
    public static String PROGRAM_ATTACHMENT = "/programAttachment/"; // 程序附件

    /**
     * PostConstruct 注解 执行顺序在 Autowired（依赖注入）之后
     * PostConstruct 注解的方法 不可以有参数
     * PostConstruct 注解需与 Component 注解配合使用
     */
    @PostConstruct
    public void init(){
        FILE_PROPERTIES = this.fileProperties;
    }

    public static List<FileData> getFileList(File[] files) {
        List fileDataList = Lists.newArrayList();
        if (files != null) {
            for (File file : files) {
                FileData fileData = new FileData();
                if (file.isDirectory()) { // 判断是文件还是文件夹
                    fileData.setId(file.getName());
                    fileData.setType("folder");
                    fileData.setOpen(true);
                    fileData.setValue(file.getName());
                    fileData.setData(getFileList(file.listFiles()));
                } else  { // 判断文件名是否以.avi结尾
                    fileData.setId(file.getName());
                    fileData.setType("file");
                    fileData.setSize(String.valueOf(file.getTotalSpace()));
                    fileData.setValue(file.getName());
                }
                fileDataList.add(fileData);
            }

        }
        return fileDataList;
    }

    /**
     * 获取文件后缀名
     * @param fileName
     * @return
     */
    public static String getFileSuffix(String fileName){
        String suffix = "";
        if(StringUtils.isNotBlank(fileName)){
            suffix = StringUtils.substringAfterLast(fileName, ".");
        }
        return suffix;
    }

    public  static String getFileType(String fileName){
        String type = "file";
        String suffix = StringUtils.substringAfterLast(fileName, ".");
        switch (suffix){
            //html
            case "htm":
            case "html":
            case "css":
            case "less":
            case "asp":
            case "php":
            case "jsp":
            case "js":
            case "java":
            case "class":
            case "c":
            case "sql":
                type = "code";
                break;
            //word
            case "doc":
                type = "word";
                break;
            case "txt":
                type = "text";
                break;
            case "wps":
                type = "word";
                break;
            case "xls":
            case "xlsx":
                type = "excel";
                break;
            case "ppt":
            case "pptx":
                type = "pp";
                break;
            case "pdf":
                //压缩文件
                type = "pdf";
                break;
            case "rar":
            case "zip":
                type = "archive";
                break;
            //
            case "exe":
                type = "fa-windows";
                break;
            //视频
            case "rmvb":
            case "wmv":
            case "asf":
            case "avi":
            case "3gp":
            case "mpg":
            case "mkv":
            case "mp4":
            case "dvd":
            case "ogm":
            case "mov":
            case "mpeg2":
            case "mpeg4":
                type = "video";
                break;
            //音频
            case "mp3":
            case "ogg":
            case "wav":
            case "ape":
            case "cda":
            case "au":
            case "midi":
            case "mac":
            case "aac":
                type = "audio";
                break;
            //flash
            case "flv":
            case "swf":
            case "m4v":
            case "f4v":
                type = "flash";
                break;
            //图片
            case "gif":
            case "jpeg":
            case "bmp":
            case "tif":
            case "png":
            case "jpg":
            case "pcd":
            case "qti":
            case "qtf":
            case "tiff":
                type = "image";
                break;
            default:
                type = "file";
        }
        return type;
    }

    private static Long getCreateTime(String fullFileName){
        Path path= Paths.get(fullFileName);
        BasicFileAttributeView basicview= Files.getFileAttributeView(path, BasicFileAttributeView.class, LinkOption.NOFOLLOW_LINKS );
        BasicFileAttributes attr;
        try {
            attr = basicview.readAttributes();
            Date createDate = new Date(attr.creationTime().toMillis());
            return createDate.getTime()/1000;
        } catch (Exception e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        cal.set(1970, 0, 1, 0, 0, 0);
        return cal.getTime().getTime()/1000;
    }

    public static List<FileData> getFileList(String pId,List<File> files) {
        List fileDataList = Lists.newArrayList();
        if (files != null) {
            for (File file : files) {
                FileData fileData = new FileData();
                if (file.isDirectory()) { // 判断是文件还是文件夹
                    fileData.setId(transDirToUrl (file.getAbsolutePath ()));
                    fileData.setType("folder");
                    fileData.setOpen(true);
                    fileData.setpId(pId);
                    fileData.setDate(getCreateTime(file.getAbsolutePath()));
                    fileData.setValue(file.getName());
                    file.listFiles();
                    fileData.setData(getFileList(file.getName(), 	Lists.newArrayList(file.listFiles())));
                } else  { // 判断文件
                    fileData.setId(transDirToUrl (file.getAbsolutePath()));
                    fileData.setType(getFileType(file.getName()));
                    fileData.setpId(pId);
                    fileData.setSize(String.valueOf(file.length()));
                    fileData.setDate(getCreateTime(file.getAbsolutePath()));
                    fileData.setValue(file.getName());
                }
                fileDataList.add(fileData);
            }

        }
        return fileDataList;
    }

    /**
     * 网络地址转为绝对地址
     * @return
     */
    public static  String getFileDir(String fileUrl){
        return  (JeePlusProperites.newInstance().getUserfilesBaseDir() + fileUrl).replace("\\","/");
    }

    /**
     * 绝对地址转换为网络地址
     * @return
     */
    public static String transDirToUrl(String dir){
        return   dir.substring(JeePlusProperites.newInstance().getUserfilesBaseDir().length());
    }


    /**
     * 共享文档物理存储地址
     * @return
     */
    public static String getShareBaseDir(){
        String dir =  JeePlusProperites.newInstance().getUserfilesBaseDir() + JeePlusProperites.USERFILES_BASE_URL  + SHARE_DOCUMENTS;
        com.jeeplus.common.utils.FileUtils.createDirectory(dir);
        return dir;
    }
    /**
     * 共享文档网络访问地址
     * @return
     */
    public static String getShareBaseUrl(){
        return  JeePlusProperites.USERFILES_BASE_URL + "/"+ SHARE_DOCUMENTS;
    }

    /**
     * 我的文档物理存储地址
     * @return
     */
    public static String getMyDocDir(){
        String id = UserUtils.getUser().getId();
        String dir = JeePlusProperites.newInstance().getUserfilesBaseDir() + JeePlusProperites.USERFILES_BASE_URL + id + "/"+ MY_DOCUMENTS;
        com.jeeplus.common.utils.FileUtils.createDirectory(dir);
        return dir;
    }
    /**
     * 我的文档网络访问地址
     * @return
     */
    public static String getMyDocUrl(){
        String id = UserUtils.getUser().getId();
//        return  JeePlusProperites.USERFILES_BASE_URL + id + "/我的文档/";
        return  JeePlusProperites.USERFILES_BASE_URL + id + "/"+ MY_DOCUMENTS;
    }

    /**
     * 程序附件物理存储地址
     * @return
     */
    public static String getAttachmentDir(){
        String id = UserUtils.getUser().getId();
        String dir = JeePlusProperites.newInstance().getUserfilesBaseDir() + JeePlusProperites.USERFILES_BASE_URL+UserUtils.getUser().getDataCode()+"/" + id + PROGRAM_ATTACHMENT;
        com.jeeplus.common.utils.FileUtils.createDirectory(dir);
        return dir;
    }

    /**
     * 程序附件网络访问地址
     * @return
     */
    public static String getAttachmentUrl(){
    	
        String id = UserUtils.getUser().getId();
        return  JeePlusProperites.USERFILES_BASE_URL+UserUtils.getUser().getDataCode()+"/" + id + PROGRAM_ATTACHMENT;
    }


    public static String getFileSize(String fileDir){
        File file = new File(fileDir);
        long size = file.length()*100;
        String label;
        if (size == 0F){
            label = "0";
        }else if(size < 1024*100){
            label = String.valueOf(size/100)+"b";
        }else if(size <1024*1024*100){
            label = String.valueOf(size/1024/100F)+"KB";
        }else{
            label = String.valueOf(size/(1024*1024)/100F)+"M";
        }
        return label;
    }

    public static void fileUpload2(MultipartFile file, String uploadPath) 
    		throws Exception{
    	   File newFile =new File(uploadPath);
           file.transferTo (newFile);
    }
    
    /**
     * 文件上传
     * @param file          文件
     * @param uploadPath    上传保存路径
     * @return
     * @throws Exception
     */
    public static AjaxJson fileUpload(MultipartFile file, String uploadPath) throws Exception{
        String fileUrl = FileKit.getAttachmentUrl()+uploadPath;
        String fileDir = FileKit.getAttachmentDir()+uploadPath;
        // 根据文件名进行判断 若文件名中无汉字、长度大于30 并且是图片的 进行重命名
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        List<String> imgSuffixList = Arrays.asList("gif","jpg","jpeg","bmp","png");
        boolean isRename = false;
        boolean result = (originalFilename.length() == originalFilename.getBytes().length);//true:无汉字  false:有汉字
        if(result && originalFilename.length() >= 30 && imgSuffixList.contains(suffix)){
            isRename = true;
        }
        return FileKit.fileUpload(file, fileUrl, fileDir, isRename, "");
    }
    public static AjaxJson fileUpload(MultipartFile file, String uploadPath, boolean isRename, String newFileName) throws Exception{
        String fileUrl = FileKit.getAttachmentUrl()+uploadPath;
        String fileDir = FileKit.getAttachmentDir()+uploadPath;
        return FileKit.fileUpload(file, fileUrl, fileDir, isRename, newFileName);
    }
    /**
     * 文件上传
     * @param file      文件
     * @param fileUrl   网络访问路径
     * @param fileDir   本地路径
     * @param isRename  是否重命名
     * @return
     * @throws Exception
     */
    public static AjaxJson fileUpload(MultipartFile file, String fileUrl, String fileDir, boolean isRename, String newFileName) throws Exception{
        AjaxJson j = new AjaxJson();
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH )+1;
        fileUrl = (fileUrl +"/"+year+"/"+month+"/");
        fileDir = (fileDir +"/"+year+"/"+month+"/");
        // 判断文件是否为空
        if (file != null) {
            String originalFilename = file.getOriginalFilename ();
            if(FILE_PROPERTIES.isAvailable (originalFilename)) {
                // 创建文件保存路径 并转存文件
                FileUtils.createDirectory (fileDir);
                String fileName = originalFilename;
                // 是否重命名
                if(isRename){
                    if(StringUtils.isNotBlank(newFileName)){
                        fileName = newFileName;
                    }else {
                        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
                        fileName = (DateUtils.formatDate(new Date(), "yyyyMMddHHmmss") + RandomUtil.randomLong(100, 999) + suffix);
                    }
                }
                File newFile = FileUtils.getAvailableFile ((fileDir + fileName), 0);
                file.transferTo (newFile);
                String url = fileUrl + newFile.getName();
                j.put ("id", FileKit.transDirToUrl (newFile.getAbsolutePath ()));
                j.put ("url", url);
                j.put ("fullUrl", JeePlusProperites.domainNameValue() + url);
                j.put("suffix", getFileSuffix(url));
                j.put ("name", newFile.getName ());
                return j;
            }else{
                return AjaxJson.error ("请勿上传非法文件!");
            }
        }else {
            return AjaxJson.error ("文件不存在!");
        }
    }

    /**
     * 文件复制
     * @param srcFilePath
     * @param uploadPath
     * @return
     * @throws Exception
     */
    public static AjaxJson fileCopy(String srcFilePath, String uploadPath) throws Exception{
        File srcFile = new File(srcFilePath);
        if (!srcFile.exists()) {
            return AjaxJson.error("源文件不存在！");
        }
        // 判断源文件是否是合法的文件
        else if (!srcFile.isFile()) {
            return AjaxJson.error("源文件格式不正确！");
        }
        // 新文件 保存路径
        String fileUrl = FileKit.getAttachmentUrl()+uploadPath;
        String fileDir = FileKit.getAttachmentDir()+uploadPath;
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH )+1;
        fileUrl = (fileUrl +"/"+year+"/"+month+"/");
        fileDir = (fileDir +"/"+year+"/"+month+"/");
        // 创建文件保存路径
        FileUtils.createDirectory(fileDir);
        // 新文件
        String fileName = srcFile.getName();
        File newFile = FileUtils.getAvailableFile ((fileDir + fileName), 0);

        AjaxJson j = new AjaxJson();
        // 准备复制文件
        // 读取的位数
        int readByte = 0;
        InputStream ins = null;
        OutputStream outs = null;
        try {
            // 打开源文件
            ins = new FileInputStream(srcFile);
            // 打开目标文件的输出流
            outs = new FileOutputStream(newFile);
            byte[] buf = new byte[1024];
            // 一次读取1024个字节，当readByte为-1时表示文件已经读取完毕
            while ((readByte = ins.read(buf)) != -1) {
                // 将读取的字节流写入到输出流
                outs.write(buf, 0, readByte);
            }
            j.put ("id", FileKit.transDirToUrl (newFile.getAbsolutePath ()));
            j.put ("url", fileUrl + newFile.getName ());
            j.put ("name", newFile.getName ());
        } catch (Exception e) {
            return AjaxJson.error("文件复制失败");
        } finally {
            // 关闭输入输出流，首先关闭输出流，然后再关闭输入流
            if (outs != null) {
                try {
                    outs.close();
                } catch (IOException oute) {
                    oute.printStackTrace();
                }
            }
            if (ins != null) {
                try {
                    ins.close();
                } catch (IOException ine) {
                    ine.printStackTrace();
                }
            }
        }
        return j;
    }

    /**
     * 删除文件
     * @param url
     */
    public static void deleteFileByUrl(String url){
        if(StringUtils.isNotBlank(url)){
            String path = FileKit.getFileDir(url);
            if(!FileUtils.delFile(path)){
                System.out.println("删除失败");
            }
        }
    }

    //文件目录
	public static void fileDirCopy(String source, String target) {
		System.out.println("===========source:"+source+"===========target:"+target);
		fileCopy(new File(source), new File(target));
		
	}

	  public static final int DATA_STREAM = 1024;
	    public static final int DATA_SIZE = 10;
	    /**
	     * 目录拷贝
	     * @param file1 要拷贝的目录
	     * @param file2 拷贝到的目录
	     */
	    public static void fileCopy(File file1, File file2) {
	        /*
	        * 文件夹的拷贝，判断文件类型是否是文件夹，
	        * 文件夹file1的名字与文件夹file2的名字是否相同，
	        * 是文件夹并且文件夹file2的名字与文件夹1的名字不相同才能继续。
	        * */
	        if(file1.isDirectory() ){
	        	/*
	        	* 传递文件夹file2的绝对路径与文件夹file1的名字赋给文件夹file2，
	        	* 使得file2的绝对路径更改
	        	*/
	        	System.out.println("------111------"+file2.getAbsolutePath() + "/" + file1.getName());
	            file2 = new File(file2.getAbsolutePath() + "/" + file1.getName());
	            //新建文件夹file2
	            file2.mkdirs();
	            //列出文件夹file1的子目录
	            File[] files1 = file1.listFiles();
	            //子目录不为空，递归循环创建，
	            //使得文件夹file1及其子文件夹、孙子文件夹等全部创建
	            if (files1 != null) {
	                File[] files2 = new File[files1.length];
	                for (int i = 0; i < files2.length; i++) {
	                    files2[i] = new File(file2.getAbsolutePath());
	                    fileCopy(files1[i],files2[i]);
	                }
	            }
	        }
	        /*
	        * 文件的拷贝，除文件夹以外全部文件的拷贝
	        * */
	        if(file1.isFile() ){
	        	System.out.println("-----222-------"+file2.getAbsolutePath() + "/" + file1.getName());
	            file2 = new File(file2.getAbsolutePath() + "/" + file1.getName());
	            FileInputStream fis = null;
	            FileOutputStream fos = null;
	            //异常抛出
	            try {
	                file2.createNewFile();
	                fis = new FileInputStream(file1.getAbsolutePath());
	                fos = new FileOutputStream(file2.getAbsolutePath());
	                //读取file1文件并写入file2文件
	                byte[] bytes = new byte[DATA_STREAM * DATA_STREAM * DATA_SIZE];
	                int readCount = 0;
	                while((readCount = fis.read(bytes)) != -1){
	                    fos.write(bytes,0,readCount);
	                }
	                //刷新
	                fos.flush();
	            } catch (IOException e) {
	                e.printStackTrace();
	            }finally{
	                if (fis != null) {
	                    try {
	                        fis.close();
	                    } catch (IOException e) {
	                        e.printStackTrace();
	                    }
	                }
	                if (fos != null) {
	                    try {
	                        fos.close();
	                    } catch (IOException e) {
	                        e.printStackTrace();
	                    }
	                }
	            }
	        }
	    }
}
