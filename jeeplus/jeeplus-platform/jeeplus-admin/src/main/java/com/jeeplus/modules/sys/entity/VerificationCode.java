package com.jeeplus.modules.sys.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @author: Marlon
 * @date: 2023/1/2 17:53
 **/

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VerificationCode {
    private Integer id;
    /**
     * 验证码
     */
    private Integer code;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 手机号
     */
    private long mobile;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 失效时间
     */
    private String validTime;
}
