<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/jeeplus-module/jeeplus-api/src/main/java" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.13.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.13.3" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.30" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.26" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-cache:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.lettuce:lettuce-core:5.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.3.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-freemarker:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.freemarker:freemarker:2.3.30" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:3.4.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-starter:2.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:2.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.5" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.0.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-validation:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish:jakarta.el:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.1.6.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.validation:jakarta.validation-api:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-websocket:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-messaging:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-websocket:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-mail:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.sun.mail:jakarta.mail:1.6.5" level="project" />
    <orderEntry type="library" name="Maven: com.sun.activation:jakarta.activation:1.2.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-devtools:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.39" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.39" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.microsoft.sqlserver:mssql-jdbc:7.4.1.jre8" level="project" />
    <orderEntry type="library" name="Maven: net.sourceforge.jtds:jtds:1.3.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: mysql:mysql-connector-java:8.0.22" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.postgresql:postgresql:42.2.18" level="project" />
    <orderEntry type="library" name="Maven: org.checkerframework:checker-qual:3.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid-spring-boot-starter:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.30" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib:3.2.7" level="project" />
    <orderEntry type="library" name="Maven: org.ow2.asm:asm:6.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ant:ant:1.10.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ant:ant-launcher:1.10.3" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: javax.servlet.jsp:jsp-api:2.1" level="project" />
    <orderEntry type="library" name="Maven: commons-dbcp:commons-dbcp:1.4" level="project" />
    <orderEntry type="library" name="Maven: commons-pool:commons-pool:1.6" level="project" />
    <orderEntry type="library" name="Maven: com.auth0:java-jwt:3.4.0" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.14" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-core:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-lang:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-cache:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-crypto-hash:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-crypto-core:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-crypto-cipher:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-config-core:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-config-ogdl:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-event:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-spring:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-cas:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.jasig.cas.client:cas-client-core:3.2.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-web:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.owasp.encoder:encoder:1.2.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.shiro:shiro-ehcache:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: net.sf.ehcache:ehcache-core:2.6.11" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-quartz:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.2.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.quartz-scheduler:quartz:2.3.2" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.8.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: jakarta.activation:jakarta.activation-api:1.2.2" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.45" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-thymeleaf:2.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf:thymeleaf-spring5:3.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf:thymeleaf:3.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.attoparser:attoparser:2.0.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.unbescape:unbescape:1.1.6.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf.extras:thymeleaf-extras-java8time:3.0.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.3.5" level="project" />
    <orderEntry type="library" name="Maven: net.sf.dozer:dozer:5.5.1" level="project" />
    <orderEntry type="library" name="Maven: commons-beanutils:commons-beanutils:1.9.1" level="project" />
    <orderEntry type="library" name="Maven: commons-collections:commons-collections:3.2.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jcl-over-slf4j:1.7.30" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi:3.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml:3.17" level="project" />
    <orderEntry type="library" name="Maven: com.github.virtuald:curvesapi:1.04" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml-schemas:3.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xmlbeans:xmlbeans:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: stax:stax-api:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.13" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.13" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger2:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spi:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-core:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-schema:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-common:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-web:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mapstruct:mapstruct:1.2.0.Final" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-ui:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.xiaoymin:swagger-bootstrap-ui:1.9.3" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-bean-validators:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.6.2" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-models:1.6.2" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:29.0-jre" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:failureaccess:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:jsr305:3.0.2" level="project" />
    <orderEntry type="library" name="Maven: com.google.errorprone:error_prone_annotations:2.3.4" level="project" />
    <orderEntry type="library" name="Maven: com.google.j2objc:j2objc-annotations:1.3" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.10" level="project" />
    <orderEntry type="library" name="Maven: javax.servlet:javax.servlet-api:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: net.sf.json-lib:json-lib:jdk15:2.4" level="project" />
    <orderEntry type="library" name="Maven: net.sf.ezmorph:ezmorph:1.0.6" level="project" />
    <orderEntry type="library" name="Maven: com.mchange:c3p0:0.9.5.2" level="project" />
    <orderEntry type="library" name="Maven: com.mchange:mchange-commons-java:0.2.11" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.maven-play-plugin.com.mchange:c3p0-oracle-thin-extras:0.9.5" level="project" />
    <orderEntry type="library" name="Maven: dom4j:dom4j:1.6" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate:hibernate-ehcache:5.4.22.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.4.1.Final" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate:hibernate-core:5.4.22.Final" level="project" />
    <orderEntry type="library" name="Maven: javax.persistence:javax.persistence-api:2.2" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.24.0-GA" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy:1.10.17" level="project" />
    <orderEntry type="library" name="Maven: antlr:antlr:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:1.1.1.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss:jandex:2.1.3.Final" level="project" />
    <orderEntry type="library" name="Maven: org.dom4j:dom4j:2.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.common:hibernate-commons-annotations:5.1.0.Final" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jaxb:jaxb-runtime:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jaxb:txw2:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: com.sun.istack:istack-commons-runtime:3.0.11" level="project" />
    <orderEntry type="library" name="Maven: net.sf.ehcache:ehcache:2.10.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-hibernate5:2.11.3" level="project" />
    <orderEntry type="library" name="Maven: javax.transaction:javax.transaction-api:1.3" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.6" level="project" />
    <orderEntry type="library" name="Maven: org.jeeplus:jeeplus-mybatis-dynamic-vue:8.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ddlutils:ddlutils:1.0" level="project" />
    <orderEntry type="library" name="Maven: commons-betwixt:commons-betwixt:0.8" level="project" />
    <orderEntry type="library" name="Maven: commons-beanutils:commons-beanutils-core:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: commons-digester:commons-digester:1.7" level="project" />
    <orderEntry type="library" name="Maven: xml-apis:xml-apis:1.0.b2" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging-api:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: junit:junit:4.13.1" level="project" />
    <orderEntry type="library" name="Maven: org.hamcrest:hamcrest-core:2.2" level="project" />
    <orderEntry type="library" name="Maven: org.hamcrest:hamcrest:2.2" level="project" />
    <orderEntry type="library" name="Maven: oro:oro:2.0.8" level="project" />
    <orderEntry type="library" name="Maven: p6spy:p6spy:3.8.7" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.16" level="project" />
    <orderEntry type="library" name="Maven: com.aliyun:aliyun-java-sdk-core:4.5.16" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.jacoco:org.jacoco.agent:runtime:0.8.6" level="project" />
    <orderEntry type="library" name="Maven: org.ini4j:ini4j:0.5.4" level="project" />
    <orderEntry type="library" name="Maven: io.opentracing:opentracing-api:0.33.0" level="project" />
    <orderEntry type="library" name="Maven: io.opentracing:opentracing-util:0.33.0" level="project" />
    <orderEntry type="library" name="Maven: io.opentracing:opentracing-noop:0.33.0" level="project" />
    <orderEntry type="library" name="Maven: com.aliyun:aliyun-java-sdk-dysmsapi:2.1.0" level="project" />
    <orderEntry type="library" name="Maven: com.oracle.database.jdbc:ojdbc8:19.8.0.0" level="project" />
    <orderEntry type="library" name="Maven: javax.xml.bind:jaxb-api:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:javax.activation-api:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.bind:jaxb-core:2.3.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.bind:jaxb-impl:2.3.0.1" level="project" />
    <orderEntry type="library" name="Maven: io.socket:socket.io-client:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.socket:engine.io-client:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okhttp3:okhttp:3.14.9" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okio:okio:1.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.json:json:20090211" level="project" />
    <orderEntry type="library" name="Maven: com.corundumstudio.socketio:netty-socketio:1.7.16" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec-http:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport-native-epoll:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport-native-unix-common:4.1.53.Final" level="project" />
    <orderEntry type="library" name="Maven: com.getui.push:restful-sdk:1.0.0.6" level="project" />
  </component>
</module>