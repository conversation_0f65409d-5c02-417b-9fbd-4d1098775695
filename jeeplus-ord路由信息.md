# JeePlus-Ord 后端项目路由明细

## 项目概述
- **项目名称**: JeePlus-Ord (律管云管理系统)
- **框架**: Spring Boot + Spring Security + MyBatis Plus
- **版本**: 3.3.2
- **主要模块**: lawcase-admin (主启动模块)、lawcase-system (系统模块)、lawcase-mall (商城模块)、lawcase-quartz (定时任务)、lawcase-generator (代码生成)、lawcase-common (通用工具)、lawcase-framework (核心框架)

## 系统管理模块 (/system/)

### 登录认证 - SysLoginController
**基础路径**: `/`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/login` | 用户登录 | 匿名 |
| GET | `/getInfo` | 获取用户信息 | 认证用户 |
| GET | `/getRouters` | 获取路由信息 | 认证用户 |

### 用户管理 - SysUserController
**基础路径**: `/system/user`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 用户列表 | system:user:list |
| GET | `/export` | 导出用户 | system:user:export |
| POST | `/importData` | 导入用户 | system:user:import |
| GET | `/importTemplate` | 导入模板 | system:user:import |
| GET | `/{userId}` | 根据ID获取用户 | system:user:query |
| POST | `/` | 新增用户 | system:user:add |
| PUT | `/` | 修改用户 | system:user:edit |
| DELETE | `/{userIds}` | 删除用户 | system:user:remove |
| PUT | `/resetPwd` | 重置密码 | system:user:resetPwd |
| PUT | `/changeStatus` | 状态修改 | system:user:edit |

### 菜单管理 - SysMenuController
**基础路径**: `/system/menu`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 菜单列表 | system:menu:list |
| GET | `/{menuId}` | 根据ID获取菜单 | system:menu:query |
| GET | `/treeselect` | 菜单下拉树列表 | system:menu:query |
| GET | `/roleMenuTreeselect/{roleId}` | 角色菜单列表树 | system:menu:query |
| POST | `/` | 新增菜单 | system:menu:add |
| PUT | `/` | 修改菜单 | system:menu:edit |
| DELETE | `/{menuId}` | 删除菜单 | system:menu:remove |

### 角色管理 - SysRoleController
**基础路径**: `/system/role`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 角色列表 | system:role:list |
| GET | `/export` | 导出角色 | system:role:export |
| GET | `/{roleId}` | 根据ID获取角色 | system:role:query |
| POST | `/` | 新增角色 | system:role:add |
| PUT | `/` | 修改角色 | system:role:edit |
| PUT | `/dataScope` | 修改数据权限 | system:role:edit |
| PUT | `/changeStatus` | 状态修改 | system:role:edit |
| DELETE | `/{roleIds}` | 删除角色 | system:role:remove |
| GET | `/optionselect` | 查询角色选择框列表 | system:role:query |

### 部门管理 - SysDeptController
**基础路径**: `/system/dept`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 部门列表 | system:dept:list |
| GET | `/list/exclude/{deptId}` | 查询部门列表(排除节点) | system:dept:list |
| GET | `/{deptId}` | 根据ID获取部门 | system:dept:query |
| GET | `/treeselect` | 部门下拉树列表 | system:dept:query |
| GET | `/roleDeptTreeselect/{roleId}` | 角色部门列表树 | system:dept:query |
| POST | `/` | 新增部门 | system:dept:add |
| PUT | `/` | 修改部门 | system:dept:edit |
| DELETE | `/{deptId}` | 删除部门 | system:dept:remove |

### 岗位管理 - SysPostController
**基础路径**: `/system/post`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 岗位列表 | system:post:list |
| GET | `/export` | 导出岗位 | system:post:export |
| GET | `/{postId}` | 根据ID获取岗位 | system:post:query |
| POST | `/` | 新增岗位 | system:post:add |
| PUT | `/` | 修改岗位 | system:post:edit |
| DELETE | `/{postIds}` | 删除岗位 | system:post:remove |
| GET | `/optionselect` | 查询岗位选择框列表 | system:post:query |

### 字典类型 - SysDictTypeController
**基础路径**: `/system/dict/type`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 字典类型列表 | system:dict:list |
| GET | `/export` | 导出字典类型 | system:dict:export |
| GET | `/{dictId}` | 根据ID获取字典类型 | system:dict:query |
| POST | `/` | 新增字典类型 | system:dict:add |
| PUT | `/` | 修改字典类型 | system:dict:edit |
| DELETE | `/{dictIds}` | 删除字典类型 | system:dict:remove |
| GET | `/optionselect` | 获取字典选择框列表 | system:dict:query |

### 字典数据 - SysDictDataController
**基础路径**: `/system/dict/data`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 字典数据列表 | system:dict:list |
| GET | `/export` | 导出字典数据 | system:dict:export |
| GET | `/{dictCode}` | 根据ID获取字典数据 | system:dict:query |
| GET | `/type/{dictType}` | 根据字典类型查询数据 | system:dict:query |
| POST | `/` | 新增字典数据 | system:dict:add |
| PUT | `/` | 修改字典数据 | system:dict:edit |
| DELETE | `/{dictCodes}` | 删除字典数据 | system:dict:remove |

### 参数配置 - SysConfigController
**基础路径**: `/system/config`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 参数配置列表 | system:config:list |
| GET | `/export` | 导出参数配置 | system:config:export |
| GET | `/{configId}` | 根据ID获取参数配置 | system:config:query |
| GET | `/configKey/{configKey}` | 根据参数键名查询参数值 | 无 |
| POST | `/` | 新增参数配置 | system:config:add |
| PUT | `/` | 修改参数配置 | system:config:edit |
| DELETE | `/{configIds}` | 删除参数配置 | system:config:remove |

### 通知公告 - SysNoticeController
**基础路径**: `/system/notice`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 通知公告列表 | system:notice:list |
| GET | `/{noticeId}` | 根据ID获取通知公告 | system:notice:query |
| POST | `/` | 新增通知公告 | system:notice:add |
| PUT | `/` | 修改通知公告 | system:notice:edit |
| DELETE | `/{noticeIds}` | 删除通知公告 | system:notice:remove |

### 个人信息 - SysProfileController
**基础路径**: `/system/user/profile`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/` | 个人信息 | 认证用户 |
| PUT | `/` | 修改用户 | 认证用户 |
| PUT | `/updatePwd` | 重置密码 | 认证用户 |
| POST | `/avatar` | 头像上传 | 认证用户 |

## 监控模块 (/monitor/)

### 在线用户 - SysUserOnlineController
**基础路径**: `/monitor/online`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 在线用户列表 | monitor:online:list |
| DELETE | `/{tokenId}` | 强退用户 | monitor:online:forceLogout |

### 登录日志 - SysLogininforController
**基础路径**: `/monitor/logininfor`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 登录日志列表 | monitor:logininfor:list |
| GET | `/export` | 导出登录日志 | monitor:logininfor:export |
| DELETE | `/{infoIds}` | 删除登录日志 | monitor:logininfor:remove |
| DELETE | `/clean` | 清空登录日志 | monitor:logininfor:remove |

### 操作日志 - SysOperlogController
**基础路径**: `/monitor/operlog`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 操作日志列表 | monitor:operlog:list |
| GET | `/export` | 导出操作日志 | monitor:operlog:export |
| DELETE | `/{operIds}` | 删除操作日志 | monitor:operlog:remove |
| DELETE | `/clean` | 清空操作日志 | monitor:operlog:remove |

### 服务监控 - ServerController
**基础路径**: `/monitor/server`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/` | 服务器信息 | monitor:server:list |

### 缓存监控 - CacheController
**基础路径**: `/monitor/cache`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/` | 缓存信息 | monitor:cache:list |

## 商城模块 (/mall/)

### 订单管理 - PayOrdController
**基础路径**: `/mall/ord`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 订单列表 | mall:ord:select |
| GET | `/export` | 导出订单 | mall:ord:export |
| GET | `/{id}` | 根据ID获取订单 | mall:ord:select |
| POST | `/` | 新增订单 | mall:ord:set |
| PUT | `/` | 修改订单 | mall:ord:set |
| DELETE | `/{ids}` | 删除订单 | mall:ord:remove |

### 产品套餐 - PayProductController
**基础路径**: `/mall/product`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 产品套餐列表 | mall:product:select |
| GET | `/export` | 导出产品套餐 | mall:product:export |
| GET | `/{id}` | 根据ID获取产品套餐 | mall:product:select |
| POST | `/` | 新增产品套餐 | mall:product:set |
| PUT | `/` | 修改产品套餐 | mall:product:set |
| DELETE | `/{ids}` | 删除产品套餐 | mall:product:remove |

### 用户管理 - PayUserController
**基础路径**: `/mall/user`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 用户列表 | mall:user:select |
| GET | `/export` | 导出用户 | mall:user:export |
| GET | `/{id}` | 根据ID获取用户 | mall:user:select |
| POST | `/` | 新增用户 | mall:user:set |
| PUT | `/` | 修改用户 | mall:user:set |
| DELETE | `/{ids}` | 删除用户 | mall:user:remove |

### 银行管理 - PayBankController
**基础路径**: `/mall/bank`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 银行列表 | mall:bank:select |
| GET | `/export` | 导出银行 | mall:bank:export |
| GET | `/{id}` | 根据ID获取银行 | mall:bank:select |
| POST | `/` | 新增银行 | mall:bank:set |
| PUT | `/` | 修改银行 | mall:bank:set |
| DELETE | `/{ids}` | 删除银行 | mall:bank:remove |

### 支付配置 - PayConfigController
**基础路径**: `/mall/config`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 支付配置列表 | mall:config:select |
| GET | `/export` | 导出支付配置 | mall:config:export |
| GET | `/{id}` | 根据ID获取支付配置 | mall:config:select |
| POST | `/` | 新增支付配置 | mall:config:set |
| PUT | `/` | 修改支付配置 | mall:config:set |
| DELETE | `/{ids}` | 删除支付配置 | mall:config:remove |

### 数据库源 - SysDatabaseController
**基础路径**: `/mall/database`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 数据库源列表 | mall:database:select |
| GET | `/export` | 导出数据库源 | mall:database:export |
| GET | `/{id}` | 根据ID获取数据库源 | mall:database:select |
| POST | `/` | 新增数据库源 | mall:database:set |
| PUT | `/` | 修改数据库源 | mall:database:set |
| DELETE | `/{ids}` | 删除数据库源 | mall:database:remove |

## 工具模块 (/tool/)

### 测试用户 - TestController
**基础路径**: `/test/user`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 获取用户列表 | 无 |
| GET | `/{userId}` | 获取用户详细 | 无 |
| POST | `/` | 新增用户 | 无 |
| PUT | `/` | 修改用户 | 无 |
| DELETE | `/{userId}` | 删除用户 | 无 |

### Swagger文档 - SwaggerController
**基础路径**: `/tool/swagger`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/` | Swagger文档 | tool:swagger:view |

## 通用模块 (/common/)

### 验证码 - CaptchaController
**基础路径**: `/captchaImage`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/` | 生成验证码 | 匿名 |

### 通用请求 - CommonController
**基础路径**: `/common/`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/upload` | 通用上传请求 | 认证用户 |
| GET | `/download/resource` | 本地资源通用下载 | 认证用户 |
| GET | `/download` | 通用下载请求 | 认证用户 |

## 定时任务模块 (/monitor/job/)

### 定时任务 - SysJobController
**基础路径**: `/monitor/job`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 定时任务列表 | monitor:job:list |
| GET | `/export` | 导出定时任务 | monitor:job:export |
| GET | `/{jobId}` | 根据ID获取定时任务 | monitor:job:query |
| POST | `/` | 新增定时任务 | monitor:job:add |
| PUT | `/` | 修改定时任务 | monitor:job:edit |
| DELETE | `/{jobIds}` | 删除定时任务 | monitor:job:remove |
| PUT | `/changeStatus` | 任务状态修改 | monitor:job:changeStatus |
| PUT | `/run` | 定时任务立即执行一次 | monitor:job:changeStatus |

### 调度日志 - SysJobLogController
**基础路径**: `/monitor/jobLog`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 调度日志列表 | monitor:job:list |
| GET | `/export` | 导出调度日志 | monitor:job:export |
| DELETE | `/{jobLogIds}` | 删除调度日志 | monitor:job:remove |
| DELETE | `/clean` | 清空调度日志 | monitor:job:remove |

## 代码生成模块 (/tool/gen/)

### 代码生成 - GenController
**基础路径**: `/tool/gen`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 查询生成表数据 | tool:gen:list |
| GET | `/db/list` | 查询数据库列表 | tool:gen:list |
| GET | `/column/{talbleId}` | 查询表列信息 | tool:gen:list |
| POST | `/importTable` | 导入表结构 | tool:gen:import |
| PUT | `/` | 修改代码生成信息 | tool:gen:edit |
| DELETE | `/{tableIds}` | 删除代码生成 | tool:gen:remove |
| GET | `/preview/{tableId}` | 预览代码 | tool:gen:preview |
| GET | `/download/{tableName}` | 生成代码(下载方式) | tool:gen:code |
| GET | `/genCode/{tableName}` | 生成代码(自定义路径) | tool:gen:code |
| GET | `/synchDb/{tableName}` | 同步数据库 | tool:gen:edit |
| GET | `/batchGenCode` | 批量生成代码 | tool:gen:code |

## 路由统计汇总

### 按模块统计
| 模块 | Controller数量 | 路由数量(估算) |
|------|----------------|----------------|
| 系统管理模块 | 10个 | 80+ |
| 监控模块 | 6个 | 30+ |
| 商城模块 | 6个 | 42+ |
| 工具模块 | 3个 | 20+ |
| 通用模块 | 2个 | 5+ |
| 定时任务模块 | 2个 | 15+ |
| 代码生成模块 | 1个 | 12+ |
| **总计** | **30个** | **204+** |

### 按HTTP方法统计
- **GET请求**: 约120个路由 (查询、列表、导出、下载等)
- **POST请求**: 约40个路由 (新增、导入、上传等)
- **PUT请求**: 约35个路由 (修改、状态变更等)
- **DELETE请求**: 约25个路由 (删除、清空等)

### 按权限类型统计
- **匿名访问**: 约5个路由
- **认证用户**: 约15个路由
- **具体功能权限**: 约184个路由

## 权限说明

### 权限级别
- **匿名**: 无需登录即可访问
- **认证用户**: 需要登录但无特定权限要求
- **具体权限**: 需要对应的功能权限

### 权限格式说明
权限格式为: `模块:功能:操作`
- **模块**: system(系统)、monitor(监控)、mall(商城)、tool(工具)等
- **功能**: user(用户)、role(角色)、menu(菜单)、job(任务)等
- **操作**: list(列表)、query(查询)、add(新增)、edit(编辑)、remove(删除)、export(导出)等

### 特殊权限
- `@ss.hasPermi()`: Spring Security权限表达式
- 支持复合权限验证

## 安全配置

### Spring Security框架
系统使用 Spring Security 进行权限控制，主要特性：
- JWT Token认证
- 基于注解的权限控制
- 方法级权限验证
- 自定义权限表达式

### 认证流程
1. 用户登录获取JWT Token
2. 请求时携带Token进行验证
3. 根据用户角色和权限进行授权

### 演示模式
系统支持演示模式，在演示模式下：
- 只有管理员可以进行增删改操作
- 普通用户只能查看数据
- 特定URL可以放开限制

## 技术特性

### 框架特点
- **Spring Boot 2.2.12**: 主框架
- **Spring Security**: 安全框架
- **MyBatis Plus 3.3.2**: ORM框架
- **Redis**: 缓存支持
- **Quartz**: 定时任务
- **Swagger 2.9.2**: API文档

### 功能特性
- **多租户支持**: 支持多数据库源
- **代码生成**: 自动生成CRUD代码
- **定时任务**: 支持动态任务管理
- **系统监控**: 服务器、缓存、在线用户监控
- **操作日志**: 完整的操作审计
- **文件管理**: 支持文件上传下载

### 商城功能
- **订单管理**: 完整的订单生命周期
- **产品管理**: 支付产品套餐管理
- **用户管理**: 商城用户体系
- **支付集成**: 多种支付方式支持
- **银行管理**: 银行信息维护

## 开发建议

### API调用规范
1. **认证**: 请求头携带 `Authorization: Bearer {token}`
2. **权限**: 确保用户具有对应的功能权限
3. **参数**: 遵循RESTful规范
4. **响应**: 统一使用AjaxResult格式

### 测试建议
1. 使用Swagger UI进行API测试
2. 注意权限验证和参数校验
3. 测试演示模式的限制

### 扩展开发
1. 新增Controller需继承BaseController
2. 使用@PreAuthorize注解进行权限控制
3. 遵循现有的路径命名规范
4. 添加适当的日志注解
