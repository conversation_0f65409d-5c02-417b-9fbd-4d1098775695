import request from '@/utils/request'

// 查询订单列表
export function listOrd(query) {
  return request({
    url: '/mall/ord/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrd(id) {
  return request({
    url: '/mall/ord/' + id,
    method: 'get'
  })
}

// 新增订单
export function addOrd(data) {
  return request({
    url: '/mall/ord',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrd(data) {
  return request({
    url: '/mall/ord',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrd(id) {
  return request({
    url: '/mall/ord/' + id,
    method: 'delete'
  })
}

// 导出订单
export function exportOrd(query) {
  return request({
    url: '/mall/ord/export',
    method: 'get',
    params: query
  })
}

// 修改后台备注
export function updateRemark(params) {
  return request({
    url: '/mall/ord/updateReamrk',
    method: 'get',
    params: params
  })
}

// 订单线下审核
export function ordCheck(params) {
  return request({
    url: '/mall/ord/ordCheck',
    method: 'get',
    params: params
  })
}

