import request from '@/utils/request'

// 查询租户列表
export function listUser(query) {
  return request({
    url: '/mall/user/list',
    method: 'get',
    params: query
  })
}

// 查询租户详细
export function getUser(id) {
  return request({
    url: '/mall/user/' + id,
    method: 'get'
  })
}

// 新增租户
export function addUser(data) {
  return request({
    url: '/mall/user',
    method: 'post',
    data: data
  })
}

// 修改租户
export function updateUser(data) {
  return request({
    url: '/mall/user',
    method: 'put',
    data: data
  })
}

// 删除租户
export function delUser(id) {
  return request({
    url: '/mall/user/' + id,
    method: 'delete'
  })
}

// 导出租户
export function exportUser(query) {
  return request({
    url: '/mall/user/export',
    method: 'get',
    params: query
  })
}

// 修改租户后台备注
export function updateRemark(params) {
  return request({
    url: '/mall/user/updateRemark',
    method: 'get',
    params: params
  })
}

// 审核租户审核
export function checkUser(params) {
  return request({
    url: '/mall/user/checkUser',
    method: 'get',
    params: params
  })
}

// 租户修改状态
export function updateStatus(params) {
  return request({
    url: '/mall/user/updateStatus',
    method: 'get',
    params: params
  })
}


// 租户修改数据源
export function updateDataSource(params) {
  return request({
    url: '/mall/user/updateDataSource',
    method: 'get',
    params: params
  })
}

// 租户修改链接
export function updateDomainUrl(params) {
  return request({
    url: '/mall/user/updateDomainUrl',
    method: 'get',
    params: params
  })
}


