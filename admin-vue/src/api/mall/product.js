import request from '@/utils/request'

// 查询支付产品套餐列表
export function listProduct(query) {
  return request({
    url: '/mall/product/list',
    method: 'get',
    params: query
  })
}

// 查询支付产品套餐详细
export function getProduct(id) {
  return request({
    url: '/mall/product/' + id,
    method: 'get'
  })
}

// 新增支付产品套餐
export function addProduct(data) {
  return request({
    url: '/mall/product',
    method: 'post',
    data: data
  })
}

// 修改支付产品套餐
export function updateProduct(data) {
  return request({
    url: '/mall/product',
    method: 'put',
    data: data
  })
}

// 删除支付产品套餐
export function delProduct(id) {
  return request({
    url: '/mall/product/' + id,
    method: 'delete'
  })
}

// 导出支付产品套餐
export function exportProduct(query) {
  return request({
    url: '/mall/product/export',
    method: 'get',
    params: query
  })
}