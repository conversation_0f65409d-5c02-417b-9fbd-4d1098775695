import request from '@/utils/request'

// 查询数据库源列表
export function listDatabase(query) {
  return request({
    url: '/mall/database/list',
    method: 'get',
    params: query
  })
}

// 查询数据库源详细
export function getDatabase(id) {
  return request({
    url: '/mall/database/' + id,
    method: 'get'
  })
}

// 新增数据库源
export function addDatabase(data) {
  return request({
    url: '/mall/database',
    method: 'post',
    data: data
  })
}

// 修改数据库源
export function updateDatabase(data) {
  return request({
    url: '/mall/database',
    method: 'put',
    data: data
  })
}

// 删除数据库源
export function delDatabase(id) {
  return request({
    url: '/mall/database/' + id,
    method: 'delete'
  })
}

// 导出数据库源
export function exportDatabase(query) {
  return request({
    url: '/mall/database/export',
    method: 'get',
    params: query
  })
}