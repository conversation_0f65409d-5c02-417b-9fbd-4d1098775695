import request from '@/utils/request'

// 查询人数套餐配置列表
export function listConfig(query) {
  return request({
    url: '/mall/config/list',
    method: 'get',
    params: query
  })
}

// 查询人数套餐配置详细
export function getConfig(id) {
  return request({
    url: '/mall/config/' + id,
    method: 'get'
  })
}

// 新增人数套餐配置
export function addConfig(data) {
  return request({
    url: '/mall/config',
    method: 'post',
    data: data
  })
}

// 修改人数套餐配置
export function updateConfig(data) {
  return request({
    url: '/mall/config',
    method: 'put',
    data: data
  })
}

// 删除人数套餐配置
export function delConfig(id) {
  return request({
    url: '/mall/config/' + id,
    method: 'delete'
  })
}

// 导出人数套餐配置
export function exportConfig(query) {
  return request({
    url: '/mall/config/export',
    method: 'get',
    params: query
  })
}