import request from '@/utils/request'

// 查询支付用户配置列表
export function listBank(query) {
  return request({
    url: '/mall/bank/list',
    method: 'get',
    params: query
  })
}

// 查询支付用户配置详细
export function getBank(id) {
  return request({
    url: '/mall/bank/' + id,
    method: 'get'
  })
}

// 新增支付用户配置
export function addBank(data) {
  return request({
    url: '/mall/bank',
    method: 'post',
    data: data
  })
}

// 修改支付用户配置
export function updateBank(data) {
  return request({
    url: '/mall/bank',
    method: 'put',
    data: data
  })
}

// 删除支付用户配置
export function delBank(id) {
  return request({
    url: '/mall/bank/' + id,
    method: 'delete'
  })
}

// 导出支付用户配置
export function exportBank(query) {
  return request({
    url: '/mall/bank/export',
    method: 'get',
    params: query
  })
}