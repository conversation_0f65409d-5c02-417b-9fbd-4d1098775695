# JeePlus 快速启动指南

## 🚀 一键启动

### Linux/macOS 用户
```bash
# 启动所有服务
./start.sh

# 只启动后端
./start.sh backend

# 只启动前端
./start.sh frontend

# 停止所有服务
./start.sh stop
```

### Windows 用户
```cmd
# 启动所有服务
start.bat

# 只启动后端
start.bat backend

# 只启动前端
start.bat frontend

# 停止所有服务
start.bat stop
```

## 📋 启动前准备

### 1. 环境检查
确保已安装以下软件：
- ✅ **Java 8+** (必需)
- ✅ **MySQL 5.7+** (必需)
- ✅ **Node.js 14+** (前端必需)
- ⚡ **Maven 3.6+** (可选，项目包含 Maven Wrapper)

### 2. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE law_case_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据 (在 sql/ 目录下找到初始化脚本)
mysql -u root -p law_case_center < sql/init.sql
```

### 3. 配置数据库连接
编辑文件：`jeeplus/jeeplus-web/src/main/resources/application-development.yml`

```yaml
spring:
  datasource: 
    username: root          # 修改为你的数据库用户名
    password: 123456w       # 修改为你的数据库密码
    url: ******************************************************************************************************************************************************************************************************************
```

## 🌐 访问地址

启动成功后，通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| 🔧 **后端服务** | http://localhost:8081/law/ | 主要业务接口 |
| 📚 **API文档** | http://localhost:8081/law/doc.html | Swagger API文档 |
| 👨‍💼 **管理前端** | http://localhost:80 | 管理员界面 |
| 👤 **用户管理** | http://localhost:8080 | 用户管理界面 |
| 🌍 **用户前端** | http://localhost:3000 | 用户前台界面 |

## 🔧 手动启动 (备选方案)

### 后端启动
```bash
cd jeeplus
mvn spring-boot:run -pl jeeplus-web
```

### 前端启动
```bash
# 管理员前端
cd admin-vue
npm install --registry=https://registry.npm.taobao.org
npm run dev

# 用户管理前端
cd user-admin-vue
yarn install
yarn serve

# 用户前端
cd user-front-vue
npm install
npm run dev
```

## ❗ 常见问题

### 端口冲突
如果遇到端口冲突，修改配置文件中的端口：
- 后端端口：`jeeplus/jeeplus-web/src/main/resources/application-development.yml`
- 前端端口：各前端项目的配置文件

### 数据库连接失败
1. 检查 MySQL 服务是否启动
2. 确认数据库用户名密码
3. 确认数据库 `law_case_center` 是否存在

### Maven 依赖下载慢
使用阿里云镜像，或者使用项目内置的 Maven Wrapper：
```bash
cd jeeplus/jeeplus-web
./mvnw spring-boot:run  # Linux/macOS
mvnw.cmd spring-boot:run  # Windows
```

### 内存不足
增加 JVM 内存：
```bash
export MAVEN_OPTS="-Xms512m -Xmx1024m"
mvn spring-boot:run -pl jeeplus-web
```

## 📁 项目结构

```
jeeplus-parent/
├── 📄 JEEPLUS_运行文档.md     # 详细运行文档
├── 📄 快速启动指南.md         # 本文件
├── 🚀 start.sh               # Linux/macOS 启动脚本
├── 🚀 start.bat              # Windows 启动脚本
├── 📁 jeeplus/               # 后端主项目
│   └── 📁 jeeplus-web/       # Web启动模块
├── 📁 admin-vue/             # 管理员前端
├── 📁 user-admin-vue/        # 用户管理前端
├── 📁 user-front-vue/        # 用户前端
├── 📁 app/                   # 移动端应用
└── 📁 sql/                   # 数据库脚本
```

## 🎯 下一步

1. **首次启动**：运行 `./start.sh` 或 `start.bat`
2. **访问系统**：打开 http://localhost:8081/law/
3. **查看文档**：访问 http://localhost:8081/law/doc.html
4. **开发调试**：查看详细的 `JEEPLUS_运行文档.md`

## 📞 获取帮助

- 📖 详细文档：查看 `JEEPLUS_运行文档.md`
- 🌐 官方网站：https://www.jeeplus.org
- 📋 查看帮助：`./start.sh help` 或 `start.bat help`

---

**提示**：首次启动可能需要下载依赖，请耐心等待。建议使用国内镜像源加速下载。
