#!/bin/bash

# JeePlus 项目启动脚本
# 使用方法: ./start.sh [backend|frontend|all]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(pwd)
JEEPLUS_DIR="$PROJECT_ROOT/jeeplus"
ADMIN_VUE_DIR="$PROJECT_ROOT/admin-vue"
USER_ADMIN_VUE_DIR="$PROJECT_ROOT/user-admin-vue"
USER_FRONT_VUE_DIR="$PROJECT_ROOT/user-front-vue"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查 Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装或未配置到 PATH"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "Java 版本: $JAVA_VERSION"
    
    # 检查 Maven
    if ! command -v mvn &> /dev/null; then
        log_warning "Maven 未安装，将使用项目内置的 Maven Wrapper"
    else
        MVN_VERSION=$(mvn -version | head -n 1)
        log_info "Maven 版本: $MVN_VERSION"
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_warning "Node.js 未安装，无法启动前端项目"
    else
        NODE_VERSION=$(node --version)
        log_info "Node.js 版本: $NODE_VERSION"
    fi
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    cd "$JEEPLUS_DIR"
    
    # 检查是否已编译
    if [ ! -d "jeeplus-web/target" ]; then
        log_info "首次运行，正在编译项目..."
        if command -v mvn &> /dev/null; then
            mvn clean compile -DskipTests
        else
            cd jeeplus-web
            ./mvnw clean compile -DskipTests
            cd ..
        fi
    fi
    
    log_info "启动 Spring Boot 应用..."
    if command -v mvn &> /dev/null; then
        mvn spring-boot:run -pl jeeplus-web &
    else
        cd jeeplus-web
        ./mvnw spring-boot:run &
        cd ..
    fi
    
    BACKEND_PID=$!
    echo $BACKEND_PID > "$PROJECT_ROOT/.backend.pid"
    
    log_success "后端服务启动中... PID: $BACKEND_PID"
    log_info "后端服务地址: http://localhost:8081/law/"
    log_info "API文档地址: http://localhost:8081/law/doc.html"
    
    cd "$PROJECT_ROOT"
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    # 启动管理员前端
    if [ -d "$ADMIN_VUE_DIR" ]; then
        log_info "启动管理员前端 (admin-vue)..."
        cd "$ADMIN_VUE_DIR"
        
        if [ ! -d "node_modules" ]; then
            log_info "安装依赖..."
            npm install --registry=https://registry.npm.taobao.org
        fi
        
        npm run dev &
        ADMIN_VUE_PID=$!
        echo $ADMIN_VUE_PID > "$PROJECT_ROOT/.admin-vue.pid"
        log_success "管理员前端启动中... PID: $ADMIN_VUE_PID"
        log_info "管理员前端地址: http://localhost:80"
        
        cd "$PROJECT_ROOT"
    fi
    
    # 启动用户管理前端
    if [ -d "$USER_ADMIN_VUE_DIR" ]; then
        log_info "启动用户管理前端 (user-admin-vue)..."
        cd "$USER_ADMIN_VUE_DIR"
        
        if [ ! -d "node_modules" ]; then
            log_info "安装依赖..."
            yarn install
        fi
        
        yarn serve &
        USER_ADMIN_VUE_PID=$!
        echo $USER_ADMIN_VUE_PID > "$PROJECT_ROOT/.user-admin-vue.pid"
        log_success "用户管理前端启动中... PID: $USER_ADMIN_VUE_PID"
        log_info "用户管理前端地址: http://localhost:8080"
        
        cd "$PROJECT_ROOT"
    fi
    
    # 启动用户前端
    if [ -d "$USER_FRONT_VUE_DIR" ]; then
        log_info "启动用户前端 (user-front-vue)..."
        cd "$USER_FRONT_VUE_DIR"
        
        if [ ! -d "node_modules" ]; then
            log_info "安装依赖..."
            npm install
        fi
        
        npm run dev &
        USER_FRONT_VUE_PID=$!
        echo $USER_FRONT_VUE_PID > "$PROJECT_ROOT/.user-front-vue.pid"
        log_success "用户前端启动中... PID: $USER_FRONT_VUE_PID"
        log_info "用户前端地址: http://localhost:3000"
        
        cd "$PROJECT_ROOT"
    fi
}

# 停止所有服务
stop_all() {
    log_info "停止所有服务..."
    
    # 停止后端
    if [ -f "$PROJECT_ROOT/.backend.pid" ]; then
        BACKEND_PID=$(cat "$PROJECT_ROOT/.backend.pid")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            log_success "后端服务已停止"
        fi
        rm -f "$PROJECT_ROOT/.backend.pid"
    fi
    
    # 停止前端服务
    for service in admin-vue user-admin-vue user-front-vue; do
        if [ -f "$PROJECT_ROOT/.$service.pid" ]; then
            PID=$(cat "$PROJECT_ROOT/.$service.pid")
            if kill -0 $PID 2>/dev/null; then
                kill $PID
                log_success "$service 服务已停止"
            fi
            rm -f "$PROJECT_ROOT/.$service.pid"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo "JeePlus 项目启动脚本"
    echo ""
    echo "使用方法:"
    echo "  ./start.sh [选项]"
    echo ""
    echo "选项:"
    echo "  backend     只启动后端服务"
    echo "  frontend    只启动前端服务"
    echo "  all         启动所有服务 (默认)"
    echo "  stop        停止所有服务"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./start.sh              # 启动所有服务"
    echo "  ./start.sh backend      # 只启动后端"
    echo "  ./start.sh frontend     # 只启动前端"
    echo "  ./start.sh stop         # 停止所有服务"
}

# 主函数
main() {
    case "${1:-all}" in
        "backend")
            check_environment
            start_backend
            ;;
        "frontend")
            check_environment
            start_frontend
            ;;
        "all")
            check_environment
            start_backend
            sleep 5  # 等待后端启动
            start_frontend
            ;;
        "stop")
            stop_all
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获 Ctrl+C 信号
trap 'log_info "接收到中断信号，正在停止服务..."; stop_all; exit 0' INT

# 执行主函数
main "$@"

# 如果启动了服务，等待用户输入
if [ "${1:-all}" != "stop" ] && [ "${1:-all}" != "help" ]; then
    log_success "所有服务启动完成！"
    echo ""
    echo "访问地址:"
    echo "  后端服务: http://localhost:8081/law/"
    echo "  API文档:  http://localhost:8081/law/doc.html"
    echo "  管理前端: http://localhost:80"
    echo "  用户管理: http://localhost:8080"
    echo "  用户前端: http://localhost:3000"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    while true; do
        sleep 1
    done
fi
