package com.my.web.api.controller;

import java.util.concurrent.TimeUnit;

import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.my.common.core.redis.RedisCache;
import com.my.web.api.cnst.RedisCnst;
import com.my.web.api.util.msg.MsgConstantUtils;
import com.my.web.api.vo.ResultVO;

import io.swagger.annotations.Api;

@RestController
@RequestMapping("/userapi")
@Api(tags = "验证码接口")
@Validated
public class ApiMsgController {
  
    @Autowired
    private RedisCache redisCache;
    
	@RequestMapping(value ="/sendRegMsgCode", method = RequestMethod.GET)
    public ResultVO<String> sendMsgCode(@NotNull(message = "手机号不能为空")  String mobile){
		ResultVO<String> rs = new ResultVO<String>();
		final String code = MsgConstantUtils.generateValidateCode(6);
		redisCache.setCacheObject(RedisCnst.SMS_CODE_REG+mobile, code, 5, TimeUnit.MINUTES);
	     MsgConstantUtils.sendPhone(code, String.valueOf(mobile),MsgConstantUtils.REGTEMPLATEID);
		return rs.success();
	}
	
	@RequestMapping(value ="/sendLoginMsgCode", method = RequestMethod.GET)
    public ResultVO<String> sendLoginMsgCode(@NotNull(message = "手机号不能为空")  String mobile){
		ResultVO<String> rs = new ResultVO<String>();
		final String code = MsgConstantUtils.generateValidateCode(6);
		redisCache.setCacheObject(RedisCnst.SMS_CODE_LOGIN+mobile, code, 5, TimeUnit.MINUTES);
	     System.out.println("验证码："+code+",mobile:"+mobile);
		  MsgConstantUtils.sendPhone(code, String.valueOf(mobile),MsgConstantUtils.TEMPLATE_ID);
		return rs.success();
	}
	
	@RequestMapping(value ="/sendEditPwdMsgCode", method = RequestMethod.GET)
    public ResultVO<String> sendEditPwdMsgCode(@NotNull(message = "手机号不能为空")  String mobile){
		ResultVO<String> rs = new ResultVO<String>();
		final String code = MsgConstantUtils.generateValidateCode(6);
		redisCache.setCacheObject(RedisCnst.SMS_CODE_EDITPWD+mobile, code, 5, TimeUnit.MINUTES);
	     MsgConstantUtils.sendPhone(code, String.valueOf(mobile),MsgConstantUtils.EDITPWDTEMPLATEID);
		return rs.success();
	}
	
	@RequestMapping(value ="/sendEditMobileMsgCode", method = RequestMethod.GET)
    public ResultVO<String> sendEditMobileMsgCode(@NotNull(message = "手机号不能为空")  String mobile){
		ResultVO<String> rs = new ResultVO<String>();
		final String code = MsgConstantUtils.generateValidateCode(6);
		redisCache.setCacheObject(RedisCnst.SMS_CODE_EDITMOBILE+mobile, code, 5, TimeUnit.MINUTES);
	     MsgConstantUtils.sendPhone(code, String.valueOf(mobile),MsgConstantUtils.EDITMOBILETEMPLATEID);
		return rs.success();
	}
	
	@RequestMapping(value ="/sendFindPwdMsgCode", method = RequestMethod.GET)
    public ResultVO<String> sendFindPwdMsgCode(@NotNull(message = "手机号不能为空")  String mobile){
		ResultVO<String> rs = new ResultVO<String>();
		final String code = MsgConstantUtils.generateValidateCode(6);
		redisCache.setCacheObject(RedisCnst.SMS_CODE_FINDPWD+mobile, code, 5, TimeUnit.MINUTES);
	     MsgConstantUtils.sendPhone(code, String.valueOf(mobile),MsgConstantUtils.FINDPWDTEMPLATEID);
		return rs.success();
	}
	
}
