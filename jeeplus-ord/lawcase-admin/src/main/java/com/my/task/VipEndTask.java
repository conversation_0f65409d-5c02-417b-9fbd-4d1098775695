package com.my.task;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.my.common.utils.DateUtils;
import com.my.mall.entity.PayUser;
import com.my.mall.service.IPayUserService;
import com.my.web.api.util.msg.MsgConstantUtils;

/**
 * VIP过期前7天提醒
 * 
 */
@Component("vipEndTask")
public class VipEndTask
{
   

    @Autowired
    private IPayUserService payUserService;
    
    
    public void remindDay()
    {
        System.out.println("执行过期预警短信");
        List<PayUser> list=payUserService.listEndUser(7);
        if(list!=null && list.size()>0) {
        	for(PayUser user:list) {
        		  MsgConstantUtils.sendPhone(DateUtils.dateTime(user.getVipEndTime()),user.getMobile()
        				  ,MsgConstantUtils.VIPTEMPLATEID);
        	}
        }
    
    }
}
