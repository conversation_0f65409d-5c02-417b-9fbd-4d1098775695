package com.my.wechatpay.utils;

import java.io.BufferedReader;
import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.my.wechatpay.cnst.WXConfig;
import com.my.wechatpay.vo.NotifyVo;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByIdRequest;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByOutTradeNoRequest;
 
public class WechatPayUtils extends Pay{
	   private static final Logger logger = LoggerFactory.getLogger(WechatPayUtils.class);
	
	   
	  public static Config config =null;
	  public static NativePayService service=null;
	   //二维码下单
	public static PrepayResponse prepay(PrepayRequest request) {
		
		request.setAppid(WXConfig.APP_ID);
		request.setMchid(WXConfig.MAC_HID);
		
		if(config==null) {
			 config =
				        new RSAAutoCertificateConfig.Builder()
				            .merchantId(WXConfig.MAC_HID)
				            .privateKey(WXConfig.PRIVATEKEY)
				            .merchantSerialNumber(WXConfig.SERIAL_NO)
				            .apiV3Key(WXConfig.API3)
				            .build();
		}
		  
		if(service==null) {
			service = new NativePayService.Builder().config(config).build();
		}
		
		return service.prepay(request);
	}
	

	//查询二维码下单订单
	public static Transaction queryNativeOrderById(String ordId) {
		
		QueryOrderByOutTradeNoRequest    queryRequest = new QueryOrderByOutTradeNoRequest();
		queryRequest.setMchid(WXConfig.MAC_HID);
		queryRequest.setOutTradeNo(ordId);
		
		if(config==null) {
			 config =
				        new RSAAutoCertificateConfig.Builder()
				            .merchantId(WXConfig.MAC_HID)
				            .privateKey(WXConfig.PRIVATEKEY)
				            .merchantSerialNumber(WXConfig.SERIAL_NO)
				            .apiV3Key(WXConfig.API3)
				            .build();
		}
		if(service==null) {
			service = new NativePayService.Builder().config(config).build();
		}
		return service.queryOrderByOutTradeNo(queryRequest);
	}
	
	 public static NotifyVo notify(HttpServletRequest request, HttpServletResponse response, String privateKey) throws Exception {
	        String result = readData(request);
	        
	        System.out.println("===回调=="+result);
	        logger.info("回调：result："+result);
	        // 需要通过证书序列号查找对应的证书，verifyNotify 中有验证证书的序列号
	        String plainText = verifyNotify(result, privateKey);
	        //发送消息通知微信
	        sendMessage(response, plainText);
	        NotifyVo notifyVo = null;
	        try {
	            ObjectMapper mapper = new ObjectMapper();
	            notifyVo = mapper.readValue(plainText,
	                    new TypeReference<NotifyVo>() {
	                    });
	        } catch (Exception e) {
	            logger.error(e.getMessage());
	        }
	        return notifyVo;
	    }
	 
	 /**
	     * 处理返回对象
	     *
	     * @param request
	     * @return
	     */
	      public static String readData(HttpServletRequest request) {
	        BufferedReader br = null;
	        try {
	            StringBuilder result = new StringBuilder();
	            br = request.getReader();
	            for (String line; (line = br.readLine()) != null; ) {
	                if (result.length() > 0) {
	                    result.append("\n");
	                }
	                result.append(line);
	            }
	            return result.toString();
	        } catch (IOException e) {
	            throw new RuntimeException(e);
	        } finally {
	            if (br != null) {
	                try {
	                    br.close();
	                } catch (IOException e) {
	                    e.printStackTrace();
	                }
	            }
	        }
	    }
	
	//微信小程序下单
	
	
	//微信小程序订单查询
	
	
	//微信提现
}
