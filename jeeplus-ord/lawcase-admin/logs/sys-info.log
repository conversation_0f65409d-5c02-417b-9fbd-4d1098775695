13:53:54.721 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.21.Final
13:53:54.743 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStarting,55] - Starting LawcaseWxApplication on lldeMacBook-Pro.local with PID 16198 (/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord/lawcase-admin/target/classes started by ll in /Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord/lawcase-admin)
13:53:54.743 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStartupProfileInfo,655] - The following profiles are active: druid
13:53:56.721 [restartedMain] INFO  o.e.jetty.util.log - [initialized,169] - Logging initialized @2766ms to org.eclipse.jetty.util.log.Slf4jLog
13:53:56.975 [restartedMain] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.35.v20201120; built: 2020-11-20T21:17:03.964Z; git: bdc54f03a5e0a7e280fab27f55c3c75ee8da89fb; jvm 1.8.0_432-b06
13:53:57.004 [restartedMain] INFO  o.e.j.s.h.C.application - [log,2363] - Initializing Spring embedded WebApplicationContext
13:53:57.635 [restartedMain] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
13:53:57.636 [restartedMain] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
13:53:57.637 [restartedMain] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
13:53:57.651 [restartedMain] INFO  o.e.j.s.h.ContextHandler - [doStart,916] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@34cff055{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.7545614196144722070/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],AVAILABLE}
13:53:57.651 [restartedMain] INFO  o.e.j.server.Server - [doStart,415] - Started @3697ms
13:53:59.668 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,985] - {dataSource-1} inited
13:54:00.028 [restartedMain] INFO  i.l.c.EpollProvider - [<clinit>,68] - Starting without optional epoll library
13:54:00.030 [restartedMain] INFO  i.l.c.KqueueProvider - [<clinit>,70] - Starting without optional kqueue library
13:54:00.272 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2032] - {dataSource-1} closing ...
13:54:00.278 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
13:54:00.402 [restartedMain] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
13:54:00.405 [restartedMain] INFO  o.e.j.s.h.ContextHandler - [doStop,1154] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@34cff055{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.7545614196144722070/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],STOPPED}
13:55:24.043 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.21.Final
13:55:24.063 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStarting,55] - Starting LawcaseWxApplication on lldeMacBook-Pro.local with PID 16368 (/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord/lawcase-admin/target/classes started by ll in /Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord/lawcase-admin)
13:55:24.064 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStartupProfileInfo,655] - The following profiles are active: druid
13:55:26.131 [restartedMain] INFO  o.e.jetty.util.log - [initialized,169] - Logging initialized @2888ms to org.eclipse.jetty.util.log.Slf4jLog
13:55:26.372 [restartedMain] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.35.v20201120; built: 2020-11-20T21:17:03.964Z; git: bdc54f03a5e0a7e280fab27f55c3c75ee8da89fb; jvm 1.8.0_432-b06
13:55:26.402 [restartedMain] INFO  o.e.j.s.h.C.application - [log,2363] - Initializing Spring embedded WebApplicationContext
13:55:27.070 [restartedMain] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
13:55:27.070 [restartedMain] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
13:55:27.071 [restartedMain] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
13:55:27.085 [restartedMain] INFO  o.e.j.s.h.ContextHandler - [doStart,916] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@21c32d75{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.2527149460200406114/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],AVAILABLE}
13:55:27.086 [restartedMain] INFO  o.e.j.server.Server - [doStart,415] - Started @3843ms
13:55:29.069 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,985] - {dataSource-1} inited
13:55:29.382 [restartedMain] INFO  i.l.c.EpollProvider - [<clinit>,68] - Starting without optional epoll library
13:55:29.384 [restartedMain] INFO  i.l.c.KqueueProvider - [<clinit>,70] - Starting without optional kqueue library
13:55:29.951 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:55:29.971 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:55:29.971 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:55:29.981 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'lldeMacBook-Pro.local1753682129954'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:55:29.981 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
13:55:29.981 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:55:29.983 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@279ae2d3
13:55:30.602 [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
13:55:31.515 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
13:55:31.542 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
13:55:31.592 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:55:31.814 [restartedMain] INFO  o.e.j.s.h.C.application - [log,2363] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:55:31.841 [restartedMain] INFO  o.e.j.s.AbstractConnector - [doStart,331] - Started ServerConnector@69e425b1{HTTP/1.1, (http/1.1)}{0.0.0.0:7500}
13:55:31.844 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStarted,61] - Started LawcaseWxApplication in 8.101 seconds (JVM running for 8.601)
13:55:32.760 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_lldeMacBook-Pro.local1753682129954 started.
13:58:43.151 [schedule-pool-1] INFO  sys-user - [run,56] - [127.0.0.1]内网IP[admin][Error][用户不存在/密码错误]
13:59:04.032 [schedule-pool-1] INFO  sys-user - [run,56] - [127.0.0.1]内网IP[manager][Success][登录成功]
14:06:27.174 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was localhost/127.0.0.1:6379
14:06:27.183 [lettuce-nioEventLoop-4-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$4,188] - Reconnected to localhost:6379
14:11:28.066 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was localhost/127.0.0.1:6379
14:11:28.071 [lettuce-nioEventLoop-4-4] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$4,188] - Reconnected to localhost:6379
14:21:12.028 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was localhost/127.0.0.1:6379
14:21:12.031 [lettuce-nioEventLoop-4-5] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$4,188] - Reconnected to localhost:6379
14:38:36.933 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was localhost/127.0.0.1:6379
14:38:36.936 [lettuce-nioEventLoop-4-6] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$4,188] - Reconnected to localhost:6379
14:41:22.340 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_lldeMacBook-Pro.local1753682129954 paused.
14:41:22.352 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_lldeMacBook-Pro.local1753682129954 shutting down.
14:41:22.352 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_lldeMacBook-Pro.local1753682129954 paused.
14:41:22.354 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_lldeMacBook-Pro.local1753682129954 shutdown complete.
14:41:22.355 [SpringContextShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:41:22.362 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2032] - {dataSource-1} closing ...
14:41:22.372 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
14:41:22.506 [SpringContextShutdownHook] INFO  o.e.j.s.AbstractConnector - [doStop,381] - Stopped ServerConnector@69e425b1{HTTP/1.1, (http/1.1)}{0.0.0.0:7500}
14:41:22.506 [SpringContextShutdownHook] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
14:41:22.507 [SpringContextShutdownHook] INFO  o.e.j.s.h.C.application - [log,2363] - Destroying Spring FrameworkServlet 'dispatcherServlet'
14:41:22.508 [SpringContextShutdownHook] INFO  o.e.j.s.h.ContextHandler - [doStop,1154] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@21c32d75{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.2527149460200406114/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],STOPPED}
