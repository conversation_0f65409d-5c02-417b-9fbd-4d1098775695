13:47:58.293 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.21.Final
13:47:58.313 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStarting,55] - Starting LawcaseWxApplication on lldeMacBook-Pro.local with PID 15169 (/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord/lawcase-admin/target/classes started by ll in /Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord)
13:47:58.313 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStartupProfileInfo,655] - The following profiles are active: druid
13:48:00.521 [restartedMain] INFO  o.e.jetty.util.log - [initialized,169] - Logging initialized @3025ms to org.eclipse.jetty.util.log.Slf4jLog
13:48:00.807 [restartedMain] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.35.v20201120; built: 2020-11-20T21:17:03.964Z; git: bdc54f03a5e0a7e280fab27f55c3c75ee8da89fb; jvm 1.8.0_432-b06
13:48:00.836 [restartedMain] INFO  o.e.j.s.h.C.application - [log,2363] - Initializing Spring embedded WebApplicationContext
13:48:01.655 [restartedMain] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
13:48:01.655 [restartedMain] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
13:48:01.657 [restartedMain] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 660000ms
13:48:01.669 [restartedMain] INFO  o.e.j.s.h.ContextHandler - [doStart,916] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@46f49d72{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.689341573108143915/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],AVAILABLE}
13:48:01.670 [restartedMain] INFO  o.e.j.server.Server - [doStart,415] - Started @4174ms
13:48:03.900 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,985] - {dataSource-1} inited
13:48:04.159 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2032] - {dataSource-1} closing ...
13:48:04.165 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
13:48:04.184 [restartedMain] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
13:48:04.186 [restartedMain] INFO  o.e.j.s.h.ContextHandler - [doStop,1154] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@46f49d72{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.689341573108143915/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],STOPPED}
