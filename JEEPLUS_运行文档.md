# JeePlus 项目运行文档

## 项目概述

JeePlus 是一个基于 Spring Boot 2.3.5 的企业级Java Web应用框架，集成了多个功能模块，包括案件管理、办公自动化、报表等功能。

## 系统架构

```
jeeplus-parent/
├── jeeplus/                    # 主应用模块
│   ├── jeeplus-web/           # Web启动模块 (主要运行入口)
│   ├── jeeplus-platform/      # 平台核心模块
│   ├── jeeplus-plugins/       # 插件模块
│   └── jeeplus-module/        # 业务模块
├── admin-vue/                 # 管理员前端 (Vue.js)
├── user-admin-vue/           # 用户管理前端
├── user-front-vue/           # 用户前端 (Nuxt.js)
├── app/                      # 移动端应用 (uni-app)
└── sql/                      # 数据库脚本
```

## 环境要求

### 必需环境
- **Java**: JDK 1.8 或以上
- **Maven**: 3.6+ (项目包含 Maven Wrapper)
- **数据库**: MySQL 5.7+ / PostgreSQL / Oracle / SQL Server
- **Node.js**: 14+ (用于前端项目)

### 可选环境
- **Redis**: 用于缓存 (可选，默认使用 EhCache)
- **Docker**: 用于容器化部署

## 快速启动

### 1. 数据库准备

1. 创建数据库：
```sql
CREATE DATABASE law_case_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 导入数据库脚本：
```bash
# 在 sql/ 目录下找到相应的数据库初始化脚本
mysql -u root -p law_case_center < sql/init.sql
```

### 2. 配置数据库连接

编辑配置文件：`jeeplus/jeeplus-web/src/main/resources/application-development.yml`

```yaml
spring:
  datasource: 
    username: root                    # 数据库用户名
    password: 123456w                 # 数据库密码
    url: ******************************************************************************************************************************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
```

### 3. 启动后端服务

#### 方式一：使用 Maven 命令 (推荐)

```bash
# 进入 jeeplus 目录
cd jeeplus

# 编译项目
mvn clean compile

# 启动应用 (开发模式)
mvn spring-boot:run -pl jeeplus-web
```

#### 方式二：使用 Maven Wrapper

```bash
# 进入 jeeplus-web 目录
cd jeeplus/jeeplus-web

# 使用 Maven Wrapper 启动
./mvnw spring-boot:run
```

#### 方式三：打包后运行

```bash
# 在 jeeplus 根目录下打包
mvn clean package -DskipTests

# 运行打包后的 jar 文件
java -jar jeeplus-web/target/law.jar
```

### 4. 启动前端服务

#### 管理员前端 (admin-vue)

```bash
cd admin-vue

# 安装依赖
npm install --registry=https://registry.npm.taobao.org

# 启动开发服务器
npm run dev
```

#### 用户管理前端 (user-admin-vue)

```bash
cd user-admin-vue

# 安装依赖
yarn install

# 启动开发服务器
yarn serve
```

#### 用户前端 (user-front-vue)

```bash
cd user-front-vue

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 访问地址

启动成功后，可以通过以下地址访问：

- **后端服务**: http://localhost:8081/law/
- **API文档**: http://localhost:8081/law/doc.html
- **管理员前端**: http://localhost:80
- **用户管理前端**: http://localhost:8080 (默认Vue CLI端口)
- **用户前端**: http://localhost:3000 (默认Nuxt.js端口)

## 配置说明

### 环境配置

项目支持多环境配置：

- `application.yml` - 主配置文件
- `application-development.yml` - 开发环境配置
- `application-production.yml` - 生产环境配置

当前激活的环境在 `application.yml` 中设置：
```yaml
spring:
  profiles:
    active: production  # 可改为 development
```

### 主要配置项

1. **服务器配置**:
   - 端口: 8081
   - 上下文路径: /law

2. **数据库配置**:
   - 支持 MySQL、PostgreSQL、Oracle、SQL Server
   - 连接池使用 Druid

3. **缓存配置**:
   - 默认使用 EhCache
   - 可切换到 Redis

4. **文件上传**:
   - 支持本地存储、阿里云OSS、MinIO
   - 默认上传路径: `D:/www/law_project/server-data/`

## 常见问题

### 1. 端口冲突
如果 8081 端口被占用，修改 `application-development.yml` 中的端口：
```yaml
server:
  port: 8082  # 改为其他端口
```

### 2. 数据库连接失败
- 检查数据库服务是否启动
- 确认用户名密码是否正确
- 检查数据库是否存在

### 3. Maven 依赖下载失败
使用阿里云镜像：
```bash
mvn clean compile -s settings.xml
```

### 4. 内存不足
增加 JVM 内存：
```bash
java -Xms512m -Xmx1024m -jar jeeplus-web/target/law.jar
```

## 生产部署

### Docker 部署

项目包含 Dockerfile，可以使用 Docker 部署：

```bash
# 构建镜像
docker build -t jeeplus:latest .

# 运行容器
docker run -d -p 8081:8081 --name jeeplus jeeplus:latest
```

### 传统部署

1. 打包项目：
```bash
mvn clean package -DskipTests
```

2. 上传 `law.jar` 到服务器

3. 启动服务：
```bash
nohup java -jar law.jar > app.log 2>&1 &
```

## 开发说明

### 项目结构
- **jeeplus-web**: 启动模块，包含主类 `JeeplusWebApplication`
- **jeeplus-core**: 核心功能模块
- **jeeplus-admin**: 管理功能模块
- **jeeplus-plugins**: 插件模块（监控、工具、定时任务等）
- **jeeplus-module**: 业务模块（案件管理、API等）

### 技术栈
- **后端**: Spring Boot 2.3.5, MyBatis, Shiro, Quartz
- **前端**: Vue.js, Element UI, Nuxt.js
- **移动端**: uni-app
- **数据库**: MySQL/PostgreSQL/Oracle/SQL Server
- **缓存**: Redis/EhCache

## 联系支持

- 官网: https://www.jeeplus.org
- 项目版本: 8.0
- Spring Boot 版本: 2.3.5.RELEASE
