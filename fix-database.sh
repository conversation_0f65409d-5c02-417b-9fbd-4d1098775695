#!/bin/bash

# JeePlus 数据库问题自动修复脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(pwd)
CONFIG_FILE="$PROJECT_ROOT/jeeplus/jeeplus-web/src/main/resources/application-production.yml"
DEV_CONFIG_FILE="$PROJECT_ROOT/jeeplus/jeeplus-web/src/main/resources/application-development.yml"
MAIN_CONFIG_FILE="$PROJECT_ROOT/jeeplus/jeeplus-web/src/main/resources/application.yml"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL服务状态
check_mysql_service() {
    log_info "检查MySQL服务状态..."
    
    if command -v systemctl &> /dev/null; then
        if systemctl is-active --quiet mysql; then
            log_success "MySQL服务正在运行 (systemctl)"
            return 0
        elif systemctl is-active --quiet mysqld; then
            log_success "MySQL服务正在运行 (mysqld)"
            return 0
        else
            log_warning "MySQL服务未运行，尝试启动..."
            sudo systemctl start mysql || sudo systemctl start mysqld
            return $?
        fi
    elif command -v brew &> /dev/null; then
        if brew services list | grep mysql | grep started &> /dev/null; then
            log_success "MySQL服务正在运行 (Homebrew)"
            return 0
        else
            log_warning "MySQL服务未运行，尝试启动..."
            brew services start mysql
            return $?
        fi
    elif command -v service &> /dev/null; then
        if service mysql status &> /dev/null; then
            log_success "MySQL服务正在运行 (service)"
            return 0
        else
            log_warning "MySQL服务未运行，尝试启动..."
            sudo service mysql start
            return $?
        fi
    else
        log_warning "无法检测MySQL服务状态，请手动确认MySQL已启动"
        return 0
    fi
}

# 测试数据库连接
test_database_connection() {
    local username=$1
    local password=$2
    local host=${3:-127.0.0.1}
    local port=${4:-3306}
    
    log_info "测试数据库连接 ($username@$host:$port)..."
    
    if mysql -u "$username" -p"$password" -h "$host" -P "$port" -e "SELECT 1;" &> /dev/null; then
        log_success "数据库连接成功"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 检查数据库是否存在
check_database_exists() {
    local username=$1
    local password=$2
    local database=$3
    local host=${4:-127.0.0.1}
    local port=${5:-3306}
    
    log_info "检查数据库 '$database' 是否存在..."
    
    local db_count=$(mysql -u "$username" -p"$password" -h "$host" -P "$port" -e "SHOW DATABASES LIKE '$database';" 2>/dev/null | wc -l)
    
    if [ "$db_count" -gt 1 ]; then
        log_success "数据库 '$database' 已存在"
        return 0
    else
        log_warning "数据库 '$database' 不存在"
        return 1
    fi
}

# 创建数据库
create_database() {
    local username=$1
    local password=$2
    local database=$3
    local host=${4:-127.0.0.1}
    local port=${5:-3306}
    
    log_info "创建数据库 '$database'..."
    
    if mysql -u "$username" -p"$password" -h "$host" -P "$port" -e "CREATE DATABASE $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null; then
        log_success "数据库 '$database' 创建成功"
        return 0
    else
        log_error "数据库 '$database' 创建失败"
        return 1
    fi
}

# 更新配置文件
update_config_file() {
    local config_file=$1
    local username=$2
    local password=$3
    local host=${4:-127.0.0.1}
    local port=${5:-3306}
    local database=${6:-law_case_center}
    
    log_info "更新配置文件: $config_file"
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 备份原配置文件
    cp "$config_file" "$config_file.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 更新配置
    local url="**************************************************************************************************************************************************************************************************************************************"
    
    # 使用sed更新配置
    sed -i.bak "s|username:.*|username: $username|" "$config_file"
    sed -i.bak "s|password:.*|password: $password|" "$config_file"
    sed -i.bak "s|url: jdbc:mysql://.*|url: $url|" "$config_file"
    
    log_success "配置文件更新完成"
    return 0
}

# 切换到开发环境
switch_to_development() {
    log_info "切换到开发环境..."
    
    if [ -f "$MAIN_CONFIG_FILE" ]; then
        # 备份主配置文件
        cp "$MAIN_CONFIG_FILE" "$MAIN_CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 更新环境配置
        sed -i.bak 's/active: production/active: development/' "$MAIN_CONFIG_FILE"
        sed -i.bak 's/#active: development/active: development/' "$MAIN_CONFIG_FILE"
        sed -i.bak 's/active: production/#active: production/' "$MAIN_CONFIG_FILE"
        
        log_success "已切换到开发环境"
        return 0
    else
        log_error "主配置文件不存在: $MAIN_CONFIG_FILE"
        return 1
    fi
}

# 导入数据库结构
import_database_schema() {
    local username=$1
    local password=$2
    local database=$3
    local host=${4:-127.0.0.1}
    local port=${5:-3306}
    
    log_info "查找数据库初始化脚本..."
    
    # 查找SQL文件
    local sql_files=($(find "$PROJECT_ROOT" -name "*.sql" -type f | head -5))
    
    if [ ${#sql_files[@]} -eq 0 ]; then
        log_warning "未找到SQL初始化脚本"
        return 1
    fi
    
    echo "找到以下SQL文件："
    for i in "${!sql_files[@]}"; do
        echo "  $((i+1)). ${sql_files[$i]}"
    done
    
    echo -n "请选择要导入的SQL文件 (输入数字，回车跳过): "
    read -r choice
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#sql_files[@]} ]; then
        local selected_file="${sql_files[$((choice-1))]}"
        log_info "导入SQL文件: $selected_file"
        
        if mysql -u "$username" -p"$password" -h "$host" -P "$port" "$database" < "$selected_file"; then
            log_success "数据库结构导入成功"
            return 0
        else
            log_error "数据库结构导入失败"
            return 1
        fi
    else
        log_info "跳过数据库结构导入"
        return 0
    fi
}

# 主修复流程
main() {
    echo "========================================"
    echo "    JeePlus 数据库问题自动修复脚本"
    echo "========================================"
    echo
    
    # 1. 检查MySQL服务
    if ! check_mysql_service; then
        log_error "MySQL服务启动失败，请手动检查"
        exit 1
    fi
    
    # 2. 获取数据库连接信息
    echo
    log_info "请输入数据库连接信息："
    read -p "MySQL用户名 [root]: " db_username
    db_username=${db_username:-root}
    
    read -p "MySQL密码: " -s db_password
    echo
    
    read -p "MySQL主机 [127.0.0.1]: " db_host
    db_host=${db_host:-127.0.0.1}
    
    read -p "MySQL端口 [3306]: " db_port
    db_port=${db_port:-3306}
    
    read -p "数据库名 [law_case_center]: " db_name
    db_name=${db_name:-law_case_center}
    
    # 3. 测试数据库连接
    echo
    if ! test_database_connection "$db_username" "$db_password" "$db_host" "$db_port"; then
        log_error "数据库连接失败，请检查用户名、密码和服务状态"
        exit 1
    fi
    
    # 4. 检查并创建数据库
    echo
    if ! check_database_exists "$db_username" "$db_password" "$db_name" "$db_host" "$db_port"; then
        echo -n "数据库不存在，是否创建? (y/N): "
        read -r create_db
        if [[ "$create_db" =~ ^[Yy]$ ]]; then
            if ! create_database "$db_username" "$db_password" "$db_name" "$db_host" "$db_port"; then
                log_error "数据库创建失败"
                exit 1
            fi
        else
            log_error "数据库不存在且用户选择不创建"
            exit 1
        fi
    fi
    
    # 5. 选择配置环境
    echo
    echo "请选择配置环境："
    echo "  1. 开发环境 (development) - 推荐"
    echo "  2. 生产环境 (production)"
    read -p "请选择 [1]: " env_choice
    env_choice=${env_choice:-1}
    
    if [ "$env_choice" = "1" ]; then
        switch_to_development
        target_config="$DEV_CONFIG_FILE"
    else
        target_config="$CONFIG_FILE"
    fi
    
    # 6. 更新配置文件
    echo
    if ! update_config_file "$target_config" "$db_username" "$db_password" "$db_host" "$db_port" "$db_name"; then
        log_error "配置文件更新失败"
        exit 1
    fi
    
    # 7. 导入数据库结构
    echo
    echo -n "是否导入数据库结构? (y/N): "
    read -r import_schema
    if [[ "$import_schema" =~ ^[Yy]$ ]]; then
        import_database_schema "$db_username" "$db_password" "$db_name" "$db_host" "$db_port"
    fi
    
    # 8. 完成
    echo
    log_success "数据库配置修复完成！"
    echo
    echo "下一步："
    echo "  1. 重新启动应用: ./start.sh backend"
    echo "  2. 访问: http://localhost:8081/law/"
    echo "  3. 查看API文档: http://localhost:8081/law/doc.html"
    echo
    echo "如果仍有问题，请查看详细文档: 数据库问题诊断和解决方案.md"
}

# 执行主函数
main "$@"
