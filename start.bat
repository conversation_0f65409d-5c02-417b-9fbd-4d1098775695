@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: JeePlus 项目启动脚本 (Windows版本)
:: 使用方法: start.bat [backend|frontend|all|stop]

set "PROJECT_ROOT=%~dp0"
set "JEEPLUS_DIR=%PROJECT_ROOT%jeeplus"
set "ADMIN_VUE_DIR=%PROJECT_ROOT%admin-vue"
set "USER_ADMIN_VUE_DIR=%PROJECT_ROOT%user-admin-vue"
set "USER_FRONT_VUE_DIR=%PROJECT_ROOT%user-front-vue"

:: 颜色定义 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: 日志函数
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:: 检查环境
:check_environment
call :log_info "检查运行环境..."

:: 检查 Java
java -version >nul 2>&1
if errorlevel 1 (
    call :log_error "Java 未安装或未配置到 PATH"
    pause
    exit /b 1
)

for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set "JAVA_VERSION=%%i"
    goto :java_found
)
:java_found
call :log_info "Java 版本: %JAVA_VERSION%"

:: 检查 Maven
mvn -version >nul 2>&1
if errorlevel 1 (
    call :log_warning "Maven 未安装，将使用项目内置的 Maven Wrapper"
    set "USE_MVN_WRAPPER=true"
) else (
    call :log_info "Maven 已安装"
    set "USE_MVN_WRAPPER=false"
)

:: 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    call :log_warning "Node.js 未安装，无法启动前端项目"
    set "NODE_AVAILABLE=false"
) else (
    for /f %%i in ('node --version') do set "NODE_VERSION=%%i"
    call :log_info "Node.js 版本: %NODE_VERSION%"
    set "NODE_AVAILABLE=true"
)
goto :eof

:: 启动后端服务
:start_backend
call :log_info "启动后端服务..."

cd /d "%JEEPLUS_DIR%"

:: 检查是否已编译
if not exist "jeeplus-web\target" (
    call :log_info "首次运行，正在编译项目..."
    if "%USE_MVN_WRAPPER%"=="true" (
        cd jeeplus-web
        call mvnw.cmd clean compile -DskipTests
        cd ..
    ) else (
        call mvn clean compile -DskipTests
    )
)

call :log_info "启动 Spring Boot 应用..."
if "%USE_MVN_WRAPPER%"=="true" (
    cd jeeplus-web
    start "JeePlus Backend" cmd /c "mvnw.cmd spring-boot:run"
    cd ..
) else (
    start "JeePlus Backend" cmd /c "mvn spring-boot:run -pl jeeplus-web"
)

call :log_success "后端服务启动中..."
call :log_info "后端服务地址: http://localhost:8081/law/"
call :log_info "API文档地址: http://localhost:8081/law/doc.html"

cd /d "%PROJECT_ROOT%"
goto :eof

:: 启动前端服务
:start_frontend
if "%NODE_AVAILABLE%"=="false" (
    call :log_error "Node.js 未安装，无法启动前端服务"
    goto :eof
)

call :log_info "启动前端服务..."

:: 启动管理员前端
if exist "%ADMIN_VUE_DIR%" (
    call :log_info "启动管理员前端 (admin-vue)..."
    cd /d "%ADMIN_VUE_DIR%"
    
    if not exist "node_modules" (
        call :log_info "安装依赖..."
        call npm install --registry=https://registry.npm.taobao.org
    )
    
    start "Admin Vue Frontend" cmd /c "npm run dev"
    call :log_success "管理员前端启动中..."
    call :log_info "管理员前端地址: http://localhost:80"
)

:: 启动用户管理前端
if exist "%USER_ADMIN_VUE_DIR%" (
    call :log_info "启动用户管理前端 (user-admin-vue)..."
    cd /d "%USER_ADMIN_VUE_DIR%"
    
    if not exist "node_modules" (
        call :log_info "安装依赖..."
        call yarn install
    )
    
    start "User Admin Vue Frontend" cmd /c "yarn serve"
    call :log_success "用户管理前端启动中..."
    call :log_info "用户管理前端地址: http://localhost:8080"
)

:: 启动用户前端
if exist "%USER_FRONT_VUE_DIR%" (
    call :log_info "启动用户前端 (user-front-vue)..."
    cd /d "%USER_FRONT_VUE_DIR%"
    
    if not exist "node_modules" (
        call :log_info "安装依赖..."
        call npm install
    )
    
    start "User Front Vue" cmd /c "npm run dev"
    call :log_success "用户前端启动中..."
    call :log_info "用户前端地址: http://localhost:3000"
)

cd /d "%PROJECT_ROOT%"
goto :eof

:: 停止所有服务
:stop_all
call :log_info "停止所有服务..."

:: 停止 Java 进程 (Spring Boot)
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo table /nh 2^>nul') do (
    taskkill /pid %%i /f >nul 2>&1
)

:: 停止 Node.js 进程
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo table /nh 2^>nul') do (
    taskkill /pid %%i /f >nul 2>&1
)

call :log_success "所有服务已停止"
goto :eof

:: 显示帮助信息
:show_help
echo JeePlus 项目启动脚本 (Windows版本)
echo.
echo 使用方法:
echo   start.bat [选项]
echo.
echo 选项:
echo   backend     只启动后端服务
echo   frontend    只启动前端服务
echo   all         启动所有服务 (默认)
echo   stop        停止所有服务
echo   help        显示此帮助信息
echo.
echo 示例:
echo   start.bat              # 启动所有服务
echo   start.bat backend      # 只启动后端
echo   start.bat frontend     # 只启动前端
echo   start.bat stop         # 停止所有服务
goto :eof

:: 主函数
:main
set "ACTION=%~1"
if "%ACTION%"=="" set "ACTION=all"

if "%ACTION%"=="backend" (
    call :check_environment
    call :start_backend
) else if "%ACTION%"=="frontend" (
    call :check_environment
    call :start_frontend
) else if "%ACTION%"=="all" (
    call :check_environment
    call :start_backend
    timeout /t 5 /nobreak >nul
    call :start_frontend
) else if "%ACTION%"=="stop" (
    call :stop_all
) else if "%ACTION%"=="help" (
    call :show_help
) else (
    call :log_error "未知选项: %ACTION%"
    call :show_help
    pause
    exit /b 1
)

if not "%ACTION%"=="stop" if not "%ACTION%"=="help" (
    call :log_success "所有服务启动完成！"
    echo.
    echo 访问地址:
    echo   后端服务: http://localhost:8081/law/
    echo   API文档:  http://localhost:8081/law/doc.html
    echo   管理前端: http://localhost:80
    echo   用户管理: http://localhost:8080
    echo   用户前端: http://localhost:3000
    echo.
    echo 按任意键退出...
    pause >nul
)

goto :eof

:: 执行主函数
call :main %*
