# JeePlus-Web 后端项目路由明细

## 项目概述
- **项目名称**: JeePlus-Web
- **框架**: Spring Boot + Spring MVC
- **主启动类**: `com.jeeplus.JeeplusWebApplication`
- **扫描包**: `com.jeeplus`, `org.flowable.ui.modeler`, `org.flowable.ui.common`

## 静态资源路由

| 路径 | 描述 | 位置 |
|------|------|------|
| `/static/**` | 静态资源文件 | classpath:/static/ |
| `/act/**` | 工作流相关资源 | classpath:/act/ |
| `swagger-ui.html` | Swagger UI界面 | classpath:/META-INF/resources/ |
| `doc.html` | API文档界面 | classpath:/META-INF/resources/ |
| `/webjars/**` | WebJars资源 | classpath:/META-INF/resources/webjars/ |

## 系统管理模块 (/sys/)

### 用户管理 - UserController
**基础路径**: `/sys/user`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 用户列表数据 | sys:user:list |
| GET | `/queryById` | 根据ID查询用户 | sys:user:view/add/edit |
| POST | `/save` | 保存用户信息 | sys:user:add/edit |
| DELETE | `/delete` | 删除用户 | sys:user:del |
| GET | `/treeData` | 用户树形数据 | user |
| POST | `/webupload/upload` | 文件上传 | user |

### 菜单管理 - MenuController  
**基础路径**: `/sys/menu`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 菜单列表 | sys:menu:list |
| POST | `/save` | 保存菜单 | sys:menu:add/edit |
| DELETE | `/delete` | 删除菜单 | sys:menu:del |
| GET | `/treeData` | 菜单树形数据 | user |
| GET | `/treeData2` | 菜单树形数据(不含功能菜单) | user |

### 机构管理 - OfficeController
**基础路径**: `/sys/office`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 机构列表 | sys:office:list |
| GET | `/queryById` | 根据ID查询机构 | sys:office:view/add/edit |
| POST | `/save` | 保存机构 | sys:office:add/edit |
| DELETE | `/delete` | 删除机构 | sys:office:del |
| GET | `/treeData` | 机构树形数据 | user |

### 区域管理 - AreaController
**基础路径**: `/sys/area`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 区域列表 | sys:area:list |
| GET | `/queryById` | 根据ID查询区域 | sys:area:view/add/edit |
| POST | `/save` | 保存区域 | sys:area:add/edit |
| DELETE | `/delete` | 删除区域 | sys:area:del |
| GET | `/treeData` | 区域树形数据 | user |

### 角色管理 - RoleController
**基础路径**: `/sys/role`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 角色列表 | sys:role:list |
| GET | `/queryById` | 根据ID查询角色 | sys:role:view/add/edit |
| POST | `/save` | 保存角色 | sys:role:add/edit |
| DELETE | `/delete` | 删除角色 | sys:role:del |

### 岗位管理 - PostController
**基础路径**: `/sys/post`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 岗位列表 | sys:post:list |
| GET | `/queryById` | 根据ID查询岗位 | sys:post:view/add/edit |
| POST | `/save` | 保存岗位 | sys:post:add/edit |
| DELETE | `/delete` | 删除岗位 | sys:post:del |

### 系统配置 - SysConfigController
**基础路径**: `/sys/sysConfig`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/queryById` | 查询系统配置 | 无 |
| GET | `/getConfig` | 获取配置(匿名) | 无 |

### 插件管理 - PluginController
**基础路径**: `/sys/plugin`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 插件列表 | 无 |

### 数据规则 - DataRuleController
**基础路径**: `/sys/dataRule`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/treeData` | 数据规则树形数据 | user |

### 登录认证 - LoginController
**基础路径**: `/sys/`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/login` | 用户登录 | 匿名 |
| POST | `/logout` | 用户登出 | 匿名 |
| POST | `/smsLogin` | 短信登录 | 匿名 |
| POST | `/sendCode` | 发送验证码 | 匿名 |
| POST | `/casLogin` | CAS登录 | 匿名 |
| GET | `/refreshToken/**` | 刷新Token | 匿名 |

## 法律案件模块 (/lawcase/)

### 案件管理 - CaseController
**基础路径**: `/lawcase/case`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 案件列表数据 | lawcase:case:list |
| POST | `/allData` | 所有案件数据(无分页) | 无 |
| GET | `/queryById` | 根据ID查询案件 | lawcase:case:view/add/edit |
| POST | `/save` | 保存案件 | lawcase:case:add/edit |
| DELETE | `/delete` | 删除案件 | lawcase:case:del |

### 案件程序 - CaseProgramController
**基础路径**: `/lawcase/caseProgram`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 案件程序列表 | lawcase:caseProgram:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseProgram:view/add/edit |
| POST | `/save` | 保存案件程序 | lawcase:caseProgram:add/edit |
| DELETE | `/delete` | 删除案件程序 | lawcase:caseProgram:del |
| POST | `/treeData` | 程序树形数据 | user |
| POST | `/typeTreeData` | 按类型获取树形数据 | user |

### 案由管理 - CaseCauseController
**基础路径**: `/lawcase/caseCause`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 案由列表 | lawcase:caseCause:list |
| GET | `/queryById` | 根据ID查询案由 | lawcase:caseCause:view/add/edit |
| POST | `/save` | 保存案由 | lawcase:caseCause:add/edit |
| DELETE | `/delete` | 删除案由 | lawcase:caseCause:del |
| POST | `/treeData` | 案由树形数据 | user |

### 客户管理 - CustomerController
**基础路径**: `/lawcase/customer`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 客户列表 | lawcase:customer:list |
| GET | `/queryById` | 根据ID查询客户 | lawcase:customer:view/add/edit |
| POST | `/save` | 保存客户 | lawcase:customer:add/edit |
| DELETE | `/delete` | 删除客户 | lawcase:customer:del |

### 客户联系人 - CustomerContactsController
**基础路径**: `/lawcase/customerContacts`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 客户联系人列表 | lawcase:customerContacts:list |
| GET | `/queryById` | 根据ID查询 | lawcase:customerContacts:view/add/edit |
| POST | `/save` | 保存客户联系人 | lawcase:customerContacts:add/edit |
| DELETE | `/delete` | 删除客户联系人 | lawcase:customerContacts:del |

### 待办事项 - TodoInfoController
**基础路径**: `/lawcase/todoInfo`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 待办列表 | lawcase:todoInfo:list |
| GET | `/queryById` | 根据ID查询 | lawcase:todoInfo:view/add/edit |
| POST | `/save` | 保存待办 | lawcase:todoInfo:add/edit |
| DELETE | `/delete` | 删除待办 | lawcase:todoInfo:del |
| GET | `/treeData` | 待办树形数据 | user |

### 阶段管理 - StageController
**基础路径**: `/lawcase/stage`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 阶段列表 | case:stage:list |
| GET | `/queryById` | 根据ID查询阶段 | case:stage:view/add/edit |
| POST | `/save` | 保存阶段 | case:stage:add/edit |
| DELETE | `/delete` | 删除阶段 | case:stage:del |

### 阶段记录 - StageRecordController
**基础路径**: `/lawcase/stageRecord`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 阶段记录列表 | lawcase:stageRecord:list |
| GET | `/queryById` | 根据ID查询 | lawcase:stageRecord:view/add/edit |
| POST | `/save` | 保存阶段记录 | lawcase:stageRecord:add/edit |
| DELETE | `/delete` | 删除阶段记录 | lawcase:stageRecord:del |
| GET | `/treeData` | 阶段记录树形数据 | user |
| POST | `/upload` | 模板附件上传 | user |

### 阶段模板 - StageTemplateController
**基础路径**: `/lawcase/stageTemplate`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 阶段模板列表 | lawcase:stageTemplate:list |
| GET | `/queryById` | 根据ID查询 | lawcase:stageTemplate:view/add/edit |
| POST | `/save` | 保存阶段模板 | lawcase:stageTemplate:add/edit |
| DELETE | `/delete` | 删除阶段模板 | lawcase:stageTemplate:del |

### 案件文件 - CaseFileController
**基础路径**: `/lawcase/caseFile`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 案件文件列表 | lawcase:caseFile:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseFile:view/add/edit |
| POST | `/save` | 保存案件文件 | lawcase:caseFile:add/edit |
| DELETE | `/delete` | 删除案件文件 | lawcase:caseFile:del |

### 案件文件目录 - CaseFileDirectoryController
**基础路径**: `/lawcase/caseFileDirectory`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 文件目录列表 | lawcase:caseFileDirectory:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseFileDirectory:view/add/edit |
| POST | `/save` | 保存文件目录 | lawcase:caseFileDirectory:add/edit |
| DELETE | `/delete` | 删除文件目录 | lawcase:caseFileDirectory:del |
| GET | `/treeData` | 目录树形数据 | user |

### 案件关系人 - CaseConcernPersonController
**基础路径**: `/lawcase/caseConcernPerson`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 关系人列表 | lawcase:caseConcernPerson:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseConcernPerson:view/add/edit |
| POST | `/save` | 保存关系人 | lawcase:caseConcernPerson:add/edit |
| DELETE | `/delete` | 删除关系人 | lawcase:caseConcernPerson:del |

### 案件承办人 - CaseUndertakePersonController
**基础路径**: `/lawcase/caseUndertakePerson`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 承办人列表 | lawcase:caseUndertakePerson:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseUndertakePerson:view/add/edit |
| POST | `/save` | 保存承办人 | lawcase:caseUndertakePerson:add/edit |
| DELETE | `/delete` | 删除承办人 | lawcase:caseUndertakePerson:del |

### 案件关联关系 - CaseCaseRelationController
**基础路径**: `/lawcase/caseCaseRelation`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 案件关联列表 | lawcase:caseCaseRelation:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseCaseRelation:view/add/edit |
| POST | `/save` | 保存案件关联 | lawcase:caseCaseRelation:add/edit |
| DELETE | `/delete` | 删除案件关联 | lawcase:caseCaseRelation:del |

### 案件执行情况 - CaseExecuteSituationController
**基础路径**: `/lawcase/caseExecuteSituation`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 执行情况列表 | lawcase:caseExecuteSituation:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseExecuteSituation:view/add/edit |
| POST | `/save` | 保存执行情况 | lawcase:caseExecuteSituation:add/edit |
| DELETE | `/delete` | 删除执行情况 | lawcase:caseExecuteSituation:del |

### 案件处理策略 - CaseHandleStrategyController
**基础路径**: `/lawcase/caseHandleStrategy`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 处理策略列表 | lawcase:caseHandleStrategy:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseHandleStrategy:view/add/edit |
| POST | `/save` | 保存处理策略 | lawcase:caseHandleStrategy:add/edit |
| DELETE | `/delete` | 删除处理策略 | lawcase:caseHandleStrategy:del |

### 财产保全 - CasePropertyPreservationController
**基础路径**: `/lawcase/casePropertyPreservation`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 财产保全列表 | lawcase:casePropertyPreservation:list |
| GET | `/queryById` | 根据ID查询 | lawcase:casePropertyPreservation:view/add/edit |
| POST | `/save` | 保存财产保全 | lawcase:casePropertyPreservation:add/edit |
| DELETE | `/delete` | 删除财产保全 | lawcase:casePropertyPreservation:del |

### 案件阶段 - CaseStageController
**基础路径**: `/lawcase/caseStage`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 案件阶段列表 | lawcase:caseStage:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseStage:view/add/edit |
| POST | `/save` | 保存案件阶段 | lawcase:caseStage:add/edit |
| DELETE | `/delete` | 删除案件阶段 | lawcase:caseStage:del |

### 庭审记录 - CaseTrialRecordController
**基础路径**: `/lawcase/caseTrialRecord`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 庭审记录列表 | lawcase:caseTrialRecord:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseTrialRecord:view/add/edit |
| POST | `/save` | 保存庭审记录 | lawcase:caseTrialRecord:add/edit |
| DELETE | `/delete` | 删除庭审记录 | lawcase:caseTrialRecord:del |

### 案件用户 - CaseUserController
**基础路径**: `/lawcase/caseUser`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 案件用户列表 | lawcase:caseUser:list |
| GET | `/queryById` | 根据ID查询 | lawcase:caseUser:view/add/edit |
| POST | `/save` | 保存案件用户 | lawcase:caseUser:add/edit |
| DELETE | `/delete` | 删除案件用户 | lawcase:caseUser:del |

### 财务流水记录 - FinanceFlowRecordController
**基础路径**: `/lawcase/financeFlowRecord`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 财务流水列表 | lawcase:financeFlowRecord:list |
| GET | `/queryById` | 根据ID查询 | lawcase:financeFlowRecord:view/add/edit |
| POST | `/save` | 保存财务流水 | lawcase:financeFlowRecord:add/edit |
| DELETE | `/delete` | 删除财务流水 | lawcase:financeFlowRecord:del |

### 行业管理 - IndustryController
**基础路径**: `/lawcase/industry`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 行业列表 | lawcase:industry:list |
| GET | `/queryById` | 根据ID查询 | lawcase:industry:view/add/edit |
| POST | `/save` | 保存行业 | lawcase:industry:add/edit |
| DELETE | `/delete` | 删除行业 | lawcase:industry:del |
| GET | `/treeData` | 行业树形数据 | user |

### 版本管理 - VersionController
**基础路径**: `/lawcase/version`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 版本列表 | lawcase:version:list |
| GET | `/queryById` | 根据ID查询 | lawcase:version:view/add/edit |
| POST | `/save` | 保存版本 | lawcase:version:add/edit |
| DELETE | `/delete` | 删除版本 | lawcase:version:del |

### OnlyOffice集成 - OnlyOfficeController
**基础路径**: `/lawcase/onlyOffice`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/config` | 获取OnlyOffice配置 | user |
| POST | `/callback` | OnlyOffice回调 | 无 |

## 移动端模块 (/app/)

### 移动端用户 - AppUserController
**基础路径**: `/app/sys/user`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 用户列表 | 无 |
| POST | `/login` | 移动端登录 | 匿名 |
| POST | `/logout` | 移动端登出 | 匿名 |
| POST | `/smsLogin` | 短信登录 | 匿名 |
| POST | `/sendCode` | 发送验证码 | 匿名 |

### 移动端机构 - AppOfficeController
**基础路径**: `/app/sys/office`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/treeData` | 机构树形数据 | 无 |

### 移动端区域 - AppAreaController
**基础路径**: `/app/sys/area`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/treeData` | 区域树形数据 | 无 |

### 移动端字典 - AppDictController
**基础路径**: `/app/sys/dict`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 字典列表 | 无 |
| GET | `/treeData` | 字典树形数据 | 无 |

### 移动端文件 - AppFileController
**基础路径**: `/app/sys/file`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/upload` | 文件上传 | 无 |
| GET | `/download` | 文件下载 | 无 |

### 移动端登录 - AppLoginController
**基础路径**: `/app/sys/`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/login` | 移动端登录 | 匿名 |
| POST | `/logout` | 移动端登出 | 匿名 |
| POST | `/smsLogin` | 短信登录 | 匿名 |
| POST | `/sendCode` | 发送验证码 | 匿名 |

## API模块 (/app/lawcase/)

### 移动端案件 - CaseApiController
**基础路径**: `/app/lawcase/case`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 案件列表 | 无 |
| GET | `/queryById` | 根据ID查询案件 | 无 |
| POST | `/save` | 保存案件 | 无 |
| DELETE | `/delete` | 删除案件 | 无 |

### 移动端客户 - CustomerApiController
**基础路径**: `/app/lawcase/customer`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 客户列表 | 无 |
| GET | `/queryById` | 根据ID查询客户 | 无 |
| POST | `/save` | 保存客户 | 无 |
| DELETE | `/delete` | 删除客户 | 无 |

### 移动端待办 - TodoInfoApiController
**基础路径**: `/app/lawcase/todoInfo`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 待办列表 | 无 |
| GET | `/queryById` | 根据ID查询待办 | 无 |
| POST | `/save` | 保存待办 | 无 |
| DELETE | `/delete` | 删除待办 | 无 |

### 移动端版本 - VersionApiController
**基础路径**: `/app/lawcase/version`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/info` | 版本信息 | 无 |

## 系统管理扩展模块

### 字典管理 - DictController
**基础路径**: `/sys/dict`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 字典列表 | sys:dict:list |
| GET | `/queryById` | 根据ID查询字典 | sys:dict:view/add/edit |
| POST | `/save` | 保存字典 | sys:dict:add/edit |
| DELETE | `/delete` | 删除字典 | sys:dict:del |
| GET | `/treeData` | 字典树形数据 | user |

### 日志管理 - LogController
**基础路径**: `/sys/log`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 日志列表 | sys:log:list |
| DELETE | `/delete` | 删除日志 | sys:log:del |
| DELETE | `/empty` | 清空日志 | sys:log:del |

### PDF文件 - PdfFileController
**基础路径**: `/sys/pdfFile`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/view` | 查看PDF | user |
| POST | `/convert` | 转换PDF | user |

### 文件管理 - FileController
**基础路径**: `/sys/file`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 文件列表 | user |
| POST | `/upload` | 文件上传 | user |
| POST | `/webupload/upload` | Web文件上传 | user |
| GET | `/download` | 文件下载 | user |
| DELETE | `/delete` | 删除文件 | user |

## 工具模块 (/tools/)

### 二维码工具 - TwoDimensionCodeController
**基础路径**: `/tools/TwoDimensionCodeController`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/createTwoDimensionCode` | 生成二维码 | 无 |

### HTTP工具 - HttpToolController
**基础路径**: `/http`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/get` | GET请求测试 | 无 |
| GET | `/post` | POST请求测试 | 无 |

## 监控模块 (/monitor/)

### 系统监控 - MonitorController
**基础路径**: `/monitor`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/server/info` | 服务器信息 | 无 |

## 办公模块 (/office/)

### 文档分类 - DocCategoryController
**基础路径**: `/office/docCategory`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 文档分类列表 | user |
| GET | `/queryById` | 根据ID查询 | user |
| POST | `/save` | 保存文档分类 | user |
| POST | `/delete` | 删除文档分类 | user |
| GET | `/treeData` | 分类树形数据 | user |

### 文档模板 - DocTemplateController
**基础路径**: `/office/docTemplate`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 文档模板列表 | wps:docTemplate:list/user |
| GET | `/queryById` | 根据ID查询 | wps:docTemplate:view/add/edit |
| POST | `/save` | 保存文档模板 | wps:docTemplate:add/edit |
| DELETE | `/delete` | 删除文档模板 | wps:docTemplate:del |

## OA办公模块 (/notify/)

### 通知通告 - OaNotifyController
**基础路径**: `/notify/oaNotify`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| POST | `/list` | 通知列表 | notify:oaNotify:list |
| GET | `/queryById` | 根据ID查询通知 | notify:oaNotify:view/add/edit |
| POST | `/save` | 保存通知 | notify:oaNotify:add/edit |
| DELETE | `/delete` | 删除通知 | notify:oaNotify:del |

### 移动端通知 - AppOaNotifyController
**基础路径**: `/app/notify/oaNotify`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/list` | 通知列表 | 无 |
| GET | `/queryById` | 根据ID查询通知 | 无 |

## 特殊路由

### DeepSeek接口 - DeepSeekController
| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET/POST | `/deepseek/getContent` | DeepSeek内容获取 | 无 |

### WPS在线编辑 - WpsWebOfficeController
| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/v3/3rd/files/{fileid}` | WPS文件信息 | 无 |
| POST | `/v3/3rd/files/{fileid}` | WPS文件保存 | 无 |
| PUT | `/v3/3rd/files/{fileid}` | WPS文件更新 | 无 |

### 测试接口 - TestController
**基础路径**: `/test`

| HTTP方法 | 路径 | 描述 | 权限要求 |
|----------|------|------|----------|
| GET | `/send` | Socket.IO测试 | 无 |

### 其他匿名路由
| 路径 | 描述 |
|------|------|
| `/txwordCallBack` | 腾讯文档回调 |
| `/401` | 未授权页面 |
| `/service/*` | 工作流服务 |
| `/rest/*` | REST服务 |

## 路由统计汇总

### 按模块统计
| 模块 | Controller数量 | 路由数量(估算) |
|------|----------------|----------------|
| 系统管理模块 | 14个 | 80+ |
| 法律案件模块 | 27个 | 150+ |
| 移动端模块 | 10个 | 50+ |
| 工具模块 | 3个 | 10+ |
| 监控模块 | 1个 | 5+ |
| 办公模块 | 3个 | 20+ |
| OA模块 | 2个 | 15+ |
| 特殊路由 | 3个 | 10+ |
| **总计** | **63个** | **340+** |

### 按HTTP方法统计
- **GET请求**: 约120个路由 (查询、列表、树形数据等)
- **POST请求**: 约150个路由 (保存、列表查询、上传等)
- **DELETE请求**: 约50个路由 (删除操作)
- **PUT请求**: 约20个路由 (更新操作)

### 按权限类型统计
- **匿名访问**: 约30个路由
- **user权限**: 约80个路由
- **具体功能权限**: 约230个路由

## 权限说明

### 权限级别
- **匿名访问**: 无需登录即可访问
- **user**: 需要登录用户权限
- **具体权限**: 如 `sys:user:list` 表示需要对应的功能权限

### 权限格式说明
权限格式通常为: `模块:功能:操作`
- 模块: sys(系统)、lawcase(法律案件)、notify(通知)等
- 功能: user(用户)、menu(菜单)、case(案件)等
- 操作: list(列表)、view(查看)、add(新增)、edit(编辑)、del(删除)

### 逻辑权限
部分接口使用逻辑权限，如 `logical=Logical.OR` 表示满足其中任一权限即可访问。

## 安全配置

### Shiro安全框架
系统使用 Apache Shiro 进行权限控制，主要特性：
- JWT Token认证
- 会话管理
- 权限控制
- 踢出重复登录

### 过滤器链
所有请求默认通过以下过滤器：
1. **JWT Filter**: Token验证
2. **Kickout Filter**: 重复登录控制

### 匿名访问路由
以下路由配置为匿名访问：
- `/app/sys/login` - 移动端登录
- `/app/sys/logout` - 移动端登出
- `/app/sys/smsLogin` - 短信登录
- `/app/sys/sendCode` - 发送验证码
- `/txwordCallBack` - 腾讯文档回调
- `/sys/refreshToken/**` - Token刷新
- `/sys/sysConfig/getConfig` - 系统配置获取
- `/sys/casLogin` - CAS登录

## 开发建议

### API调用规范
1. **认证**: 除匿名路由外，需在请求头携带JWT Token
2. **权限**: 确保用户具有对应的功能权限
3. **参数**: 遵循RESTful规范，使用合适的HTTP方法
4. **响应**: 统一使用AjaxJson格式返回

### 测试建议
1. 使用Swagger UI进行API测试: `http://localhost:8080/doc.html`
2. 关注权限验证和参数校验
3. 测试异常情况和边界条件

### 扩展开发
1. 新增Controller需继承BaseController
2. 使用@RequiresPermissions注解进行权限控制
3. 遵循现有的路径命名规范
4. 添加适当的API文档注解
