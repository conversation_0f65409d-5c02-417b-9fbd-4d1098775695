# JeePlus-Ord 项目启动指南

## 项目概述
- **项目名称**: JeePlus-Ord (律管云管理系统)
- **技术栈**: Spring Boot 2.2.12 + Spring Security + MyBatis Plus + Redis
- **Java版本**: JDK 1.8+
- **构建工具**: Maven 3.6+

## 环境要求

### 必需软件
1. **JDK 1.8+**
2. **Maven 3.6+**
3. **MySQL 5.7+** 或 **MySQL 8.0+**
4. **Redis 3.0+**
5. **Node.js 12+** (如果需要前端)

### 推荐IDE
- IntelliJ IDEA
- Eclipse
- Visual Studio Code

## 当前问题分析

根据您提供的错误信息，主要问题是Maven依赖解析失败：

```
Failed to read artifact descriptor for com.tencentcloudapi:tencentcloud-sdk-java:jar:4.0.11
Blocked mirror for repositories: [public (http://maven.aliyun.com/nexus/content/groups/public/, default, releases+snapshots)]
```

**问题原因**：
1. Maven仓库配置问题
2. 腾讯云SDK依赖无法下载
3. 阿里云镜像被阻止

## 解决方案

### 方案一：修复Maven配置

1. **检查Maven settings.xml配置**

找到Maven安装目录下的 `conf/settings.xml` 或用户目录下的 `.m2/settings.xml`，添加或修改镜像配置：

```xml
<settings>
    <mirrors>
        <!-- 阿里云中央仓库 -->
        <mirror>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        
        <!-- 华为云镜像 -->
        <mirror>
            <id>huaweicloud</id>
            <name>huawei maven</name>
            <url>https://repo.huaweicloud.com/repository/maven/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
    </mirrors>
    
    <profiles>
        <profile>
            <id>default</id>
            <repositories>
                <repository>
                    <id>central</id>
                    <url>https://repo1.maven.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>aliyun</id>
                    <url>https://maven.aliyun.com/repository/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>
    
    <activeProfiles>
        <activeProfile>default</activeProfile>
    </activeProfiles>
</settings>
```

2. **清理Maven缓存**

```bash
# 清理本地仓库缓存
mvn dependency:purge-local-repository

# 或者直接删除.m2目录下的repository文件夹
rm -rf ~/.m2/repository
```

### 方案二：修改项目POM配置

在项目根目录的 `pom.xml` 中添加仓库配置：

```xml
<repositories>
    <repository>
        <id>central</id>
        <name>Maven Central</name>
        <url>https://repo1.maven.org/maven2</url>
        <releases>
            <enabled>true</enabled>
        </releases>
    </repository>
    <repository>
        <id>aliyun</id>
        <name>Aliyun Maven</name>
        <url>https://maven.aliyun.com/repository/public</url>
        <releases>
            <enabled>true</enabled>
        </releases>
        <snapshots>
            <enabled>true</enabled>
        </snapshots>
    </repository>
    <repository>
        <id>tencent</id>
        <name>Tencent Maven</name>
        <url>https://mirrors.tencent.com/nexus/repository/maven-public/</url>
        <releases>
            <enabled>true</enabled>
        </releases>
    </repository>
</repositories>
```

### 方案三：手动下载依赖

如果上述方案仍然失败，可以手动下载腾讯云SDK：

```bash
# 手动安装腾讯云SDK到本地仓库
mvn install:install-file \
  -Dfile=tencentcloud-sdk-java-4.0.11.jar \
  -DgroupId=com.tencentcloudapi \
  -DartifactId=tencentcloud-sdk-java \
  -Dversion=4.0.11 \
  -Dpackaging=jar
```

## 数据库配置

### 1. 创建数据库

```sql
-- 创建数据库
CREATE DATABASE lawcase_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'lawcase'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON lawcase_db.* TO 'lawcase'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 修改数据库配置

编辑 `lawcase-admin/src/main/resources/application-druid.yml`：

```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ***************************************************************************************************************************************************
        username: root
        password: your_password
      # 从库数据源（可选）
      slave:
        enabled: false
        url: 
        username: 
        password: 
```

### 3. 导入数据库脚本

查找并执行SQL脚本：
```bash
# 通常在以下位置
find . -name "*.sql" -type f
# 或者
ls -la doc/sql/
ls -la sql/
```

## Redis配置

编辑 `lawcase-admin/src/main/resources/application.yml`：

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
```

## 启动步骤

### 1. 解决依赖问题

```bash
# 进入项目目录
cd /Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord

# 清理并重新下载依赖
mvn clean

# 跳过测试编译
mvn compile -DskipTests

# 如果还有问题，尝试强制更新
mvn clean compile -U -DskipTests
```

### 2. 启动Redis

```bash
# macOS (使用Homebrew)
brew services start redis

# 或者直接启动
redis-server

# Linux
systemctl start redis
# 或者
service redis start
```

### 3. 启动MySQL

```bash
# macOS (使用Homebrew)
brew services start mysql

# Linux
systemctl start mysql
# 或者
service mysql start
```

### 4. 编译项目

```bash
# 编译整个项目
mvn clean package -DskipTests

# 或者只编译不打包
mvn clean compile -DskipTests
```

### 5. 启动应用

#### 方式一：使用Maven启动

```bash
# 进入admin模块
cd lawcase-admin

# 启动应用
mvn spring-boot:run
```

#### 方式二：使用IDE启动

1. 导入项目到IDE
2. 找到 `lawcase-admin` 模块
3. 运行 `LawcaseApplication.java` 主类

#### 方式三：使用jar包启动

```bash
# 先打包
mvn clean package -DskipTests

# 启动jar包
java -jar lawcase-admin/target/lawcase-admin-3.3.2.jar
```

## 验证启动

### 1. 检查启动日志

启动成功后应该看到类似信息：
```
----------------------------------------------------------
Jeeplus Application running at:
- Local: http://localhost:8080/
- Network: http://*************:8080/
- swagger: http://*************:8080/doc.html
----------------------------------------------------------
```

### 2. 访问应用

- **主页**: http://localhost:8080
- **API文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui.html

### 3. 默认账号

通常默认管理员账号：
- 用户名: `admin`
- 密码: `admin123` 或 `123456`

## 常见问题解决

### 问题1：端口被占用

```bash
# 查看端口占用
lsof -i :8080

# 修改端口（在application.yml中）
server:
  port: 8081
```

### 问题2：数据库连接失败

1. 检查数据库是否启动
2. 检查用户名密码是否正确
3. 检查数据库URL是否正确
4. 检查防火墙设置

### 问题3：Redis连接失败

1. 检查Redis是否启动
2. 检查Redis配置
3. 可以临时禁用Redis（注释相关配置）

### 问题4：内存不足

```bash
# 增加JVM内存
export MAVEN_OPTS="-Xmx1024m -XX:MaxPermSize=256m"

# 或者在启动时指定
java -Xmx1024m -jar lawcase-admin-3.3.2.jar
```

## 开发模式启动

### 1. 开发环境配置

修改 `application.yml`：
```yaml
spring:
  profiles:
    active: dev
```

### 2. 热部署配置

添加依赖到 `lawcase-admin/pom.xml`：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <scope>runtime</scope>
    <optional>true</optional>
</dependency>
```

### 3. IDE配置

在IntelliJ IDEA中：
1. File → Settings → Build → Compiler → Build project automatically
2. Help → Find Action → Registry → compiler.automake.allow.when.app.running

## 生产环境部署

### 1. 打包

```bash
mvn clean package -DskipTests -Pprod
```

### 2. 启动脚本

创建 `start.sh`：
```bash
#!/bin/bash
nohup java -jar -Xmx1024m -Xms512m lawcase-admin-3.3.2.jar > app.log 2>&1 &
echo $! > app.pid
```

### 3. 停止脚本

创建 `stop.sh`：
```bash
#!/bin/bash
if [ -f app.pid ]; then
    kill -9 `cat app.pid`
    rm app.pid
fi
```

## 总结

按照以上步骤，您应该能够成功启动 JeePlus-Ord 项目。关键是先解决Maven依赖问题，然后配置好数据库和Redis，最后启动应用。如果遇到其他问题，请检查日志文件获取详细错误信息。
