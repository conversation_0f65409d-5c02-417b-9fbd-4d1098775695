# JeePlus 数据库连接问题诊断和解决方案

## 🔍 问题分析

您遇到的错误：
```
Could not create JPA EntityManager; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
```

这是典型的数据库连接问题，通常由以下原因引起：

## 🛠️ 解决步骤

### 1. 检查当前配置环境

项目当前使用的是 **production** 环境配置，数据库配置在：
`jeeplus/jeeplus-web/src/main/resources/application-production.yml`

当前数据库配置：
```yaml
spring:
  datasource: 
    username: root
    password: root  # ⚠️ 这里可能是问题所在
    url: ******************************************************************************************************************************************************************************************************************
```

### 2. 切换到开发环境 (推荐)

修改 `jeeplus/jeeplus-web/src/main/resources/application.yml`：

```yaml
spring:
  profiles:
    active: development  # 改为 development
    # active: production  # 注释掉这行
```

然后修改开发环境配置 `application-development.yml` 中的数据库密码：
```yaml
spring:
  datasource: 
    username: root
    password: 123456w  # 改为您的实际MySQL密码
```

### 3. 数据库连接测试

#### 方法一：使用命令行测试
```bash
# 测试MySQL连接
mysql -u root -p -h 127.0.0.1 -P 3306

# 检查数据库是否存在
mysql -u root -p -e "SHOW DATABASES LIKE 'law_case_center';"
```

#### 方法二：使用telnet测试端口
```bash
# 测试MySQL端口是否开放
telnet 127.0.0.1 3306
```

### 4. 创建数据库和用户

如果数据库不存在，请执行：

```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE law_case_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户（可选，更安全）
CREATE USER 'jeeplus'@'localhost' IDENTIFIED BY 'jeeplus123';
GRANT ALL PRIVILEGES ON law_case_center.* TO 'jeeplus'@'localhost';
FLUSH PRIVILEGES;

-- 查看数据库
SHOW DATABASES;
USE law_case_center;
SHOW TABLES;
```

### 5. 导入数据库结构

查找并导入数据库初始化脚本：

```bash
# 在项目根目录查找SQL文件
find . -name "*.sql" -type f

# 导入数据库结构（根据实际文件路径调整）
mysql -u root -p law_case_center < sql/数据库脚本.sql
```

### 6. 常见配置问题修复

#### 问题1：MySQL 8.0 兼容性
如果使用MySQL 8.0，可能需要调整配置：

```yaml
spring:
  datasource:
    url: ***********************************************************************************************************************************************************************************************************************************************
```

#### 问题2：时区问题
确保MySQL时区设置正确：

```sql
-- 检查时区
SELECT @@global.time_zone, @@session.time_zone;

-- 设置时区
SET GLOBAL time_zone = '+8:00';
```

#### 问题3：连接数限制
检查MySQL最大连接数：

```sql
SHOW VARIABLES LIKE 'max_connections';
SHOW STATUS LIKE 'Threads_connected';
```

### 7. 快速修复脚本

创建一个快速修复脚本 `fix-database.sh`：

```bash
#!/bin/bash

echo "=== JeePlus 数据库问题修复脚本 ==="

# 1. 检查MySQL服务
echo "1. 检查MySQL服务状态..."
if command -v systemctl &> /dev/null; then
    sudo systemctl status mysql
elif command -v brew &> /dev/null; then
    brew services list | grep mysql
fi

# 2. 测试数据库连接
echo "2. 测试数据库连接..."
read -p "请输入MySQL root密码: " -s mysql_password
echo

mysql -u root -p$mysql_password -e "SELECT 1;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ MySQL连接成功"
else
    echo "❌ MySQL连接失败，请检查密码和服务状态"
    exit 1
fi

# 3. 检查数据库是否存在
echo "3. 检查数据库..."
db_exists=$(mysql -u root -p$mysql_password -e "SHOW DATABASES LIKE 'law_case_center';" | wc -l)
if [ $db_exists -gt 1 ]; then
    echo "✅ 数据库 law_case_center 已存在"
else
    echo "❌ 数据库不存在，正在创建..."
    mysql -u root -p$mysql_password -e "CREATE DATABASE law_case_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo "✅ 数据库创建完成"
fi

# 4. 更新配置文件
echo "4. 更新配置文件..."
config_file="jeeplus/jeeplus-web/src/main/resources/application-production.yml"
if [ -f "$config_file" ]; then
    # 备份原配置
    cp "$config_file" "$config_file.backup"
    
    # 更新密码
    sed -i.bak "s/password: root/password: $mysql_password/" "$config_file"
    echo "✅ 配置文件已更新"
else
    echo "❌ 配置文件不存在: $config_file"
fi

echo "=== 修复完成 ==="
echo "请重新启动应用: ./start.sh backend"
```

### 8. 验证修复

修复后，重新启动应用：

```bash
# 停止当前应用
./start.sh stop

# 重新启动
./start.sh backend
```

检查启动日志，确认没有数据库连接错误。

### 9. 应急方案：使用H2内存数据库

如果MySQL问题暂时无法解决，可以临时使用H2内存数据库：

在 `application-development.yml` 中添加：

```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driverClassName: org.h2.Driver
    username: sa
    password: 
  h2:
    console:
      enabled: true
      path: /h2-console
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
```

## 🔧 常用诊断命令

```bash
# 检查MySQL进程
ps aux | grep mysql

# 检查端口占用
netstat -tlnp | grep 3306
lsof -i :3306

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log

# 检查MySQL配置
mysql --help | grep "Default options"
```

## 📞 获取更多帮助

如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. MySQL版本 (`mysql --version`)
3. Java版本 (`java -version`)
4. 完整的错误日志
5. MySQL服务状态

这样我可以提供更精确的解决方案。
